import pandas as pd
import plotly.graph_objects as go
from ipywidgets import Dropdown, Button, Output, VBox, HBox, Label, Text, Layout, ColorPicker
import itertools
import numpy as np  # 导入numpy以支持对数转换

# 添加IPython的display函数，用于在Jupyter中显示内容
try:
    from IPython.display import display
except ImportError:
    # 如果不在Jupyter环境中，提供一个简单的替代函数
    def display(obj):
        print("注意: 不在Jupyter环境中，无法显示交互式内容")
        print(f"对象类型: {type(obj)}")

# 配置原始文件路径（需修改为实际路径）
ORIGINAL_FILE = r'D:\LQ_2024\MJN_2024\沉积相\Zubair沉积相\Zubair_train\MJ-47original_data_edit_0514-2-for_train.csv'

# 特征单位映射（可根据需要修改）
UNITS = {
    'GR': 'API',
    'RT': 'Ω·m',
    'DEN': 'g/cm³',
    'AC': 'μs/ft',
    'CNL': '%',
    'PE': 'barn/e',
    'SP': 'mV',
    'CAL': 'in',
    'RS': 'Ω·m',
    'RD': 'Ω·m',
    'POTA': '%',
    'URAN': 'ppm',
    'THOR': 'ppm',
    'K': '%',
    'U': 'ppm',
    'TH': 'ppm',
    'Depth': 'm',
    'Permeability': 'mD',
    'Porosity': '%'
}


class InteractiveFaciesEditor:
    def __init__(self):
        # 初始化数据，使用更鲁棒的加载方式
        try:
            # 读取CSV文件
            self.df = pd.read_csv(ORIGINAL_FILE)

            # 处理列名中的空格
            self.df.columns = self.df.columns.str.strip()

            # 检查'Facies'列是否存在（不区分大小写）
            facies_col = None
            for col in self.df.columns:
                if col.lower().strip() == 'facies':
                    facies_col = col
                    break

            if facies_col is None:
                print(f"警告: 没有找到'Facies'列。可用的列: {list(self.df.columns)}")
                # 如果没有找到Facies列，创建一个默认值为0的列
                self.df['Facies'] = 0
            elif facies_col != 'Facies':
                # 如果列名大小写不一致，统一重命名为'Facies'
                self.df.rename(columns={facies_col: 'Facies'}, inplace=True)
                print(f"已将列 '{facies_col}' 重命名为 'Facies'")

            # 将原始的相类型 3,2,1,0 映射到 1,2,3,4
            print(f"Facies列的原始值: {self.df['Facies'].unique()}")

            # 检查Facies列的数据类型
            print(f"Facies列的数据类型: {self.df['Facies'].dtype}")

            # 如果数据已经是数值类型，则应用映射
            if pd.api.types.is_numeric_dtype(self.df['Facies']):
                facies_mapping = {3: 1, 2: 2, 1: 3, 0: 4}
                self.df['Facies'] = self.df['Facies'].map(lambda x: facies_mapping.get(x, x))
            else:
                # 如果不是数值类型，保留原始值，不进行映射
                print("注意: Facies列不是数值类型，保留原始值")

            print(f"映射后的Facies值: {self.df['Facies'].unique()}")

        except Exception as e:
            print(f"加载数据时出错: {str(e)}")
            # 创建一个空的DataFrame以防出错
            self.df = pd.DataFrame({'Facies': [0]})

        self.current_df = self.df.copy()

        # 专业地质配色方案 - 保留前4个原有颜色，增加10个新颜色
        self.colors = {
            # 保持原有的4个颜色不变
            1: '#ff7f0e',  # 橙色 (原 Facies 3)
            2: '#F3E80D',  # 黄色 (原 Facies 2)
            3: '#03F1FD',  # 浅天蓝色 (原 Facies 1)
            4: '#808080',  # 灰色 (原 Facies 0)

            # 增加10个专业地质配色
            5: '#8B4513',  # 棕色 (适合陆相沉积环境)
            6: '#006400',  # 深绿色 (适合浅海相)
            7: '#4B0082',  # 靛蓝色 (适合深水相)
            8: '#CD5C5C',  # 印度红 (适合氧化环境)
            9: '#2F4F4F',  # 深岩灰 (适合还原环境)
            10: '#9ACD32',  # 黄绿色 (适合草本植物发育的环境)
            11: '#BC8F8F',  # 玫瑰褐色 (适合砂岩相)
            12: '#5F9EA0',  # 军蓝色 (适合页岩相)
            13: '#D2691E',  # 巧克力色 (适合泥岩相)
            14: '#03F1FD',  # 丽贝卡紫 (适合火山岩相)
        }

        # 默认颜色选项 - 用于颜色选择下拉菜单
        self.default_colors = [
            ('#ff7f0e', '橙色'),
            ('#F3E80D', '黄色'),
            ('#03F1FD', '浅天蓝色'),
            ('#808080', '灰色'),
            ('#8B4513', '棕色'),
            ('#006400', '深绿色'),
            ('#4B0082', '靛蓝色'),
            ('#CD5C5C', '印度红'),
            ('#2F4F4F', '深岩灰'),
            ('#9ACD32', '黄绿色'),
            ('#BC8F8F', '玫瑰褐色'),
            ('#5F9EA0', '军蓝色'),
            ('#D2691E', '巧克力色'),
            ('#FF00FF', '紫色')
        ]

        # 相类型名称映射
        self.facies_names = {
            1: "内滩相",
            2: "潮坪藻席相",
            3: "潟湖相",
            4: "潮下带相",
            5: "中坡相",
        }

        # 标记符号
        self.symbols = [
            'circle', 'square', 'diamond', 'cross',
            'x', 'triangle-up', 'triangle-down', 'pentagon',
            'hexagon', 'star', 'hexagram', 'hourglass', 'bowtie',
            'circle-open', 'square-open', 'diamond-open', 'cross-open',
            'x-open', 'triangle-up-open', 'triangle-down-open'
        ]

        # UI元素
        self.selected_points_idx = []
        self.current_pair = None
        self.fig = None

        # 创建控件
        self.create_widgets()
        self.setup_ui()

        # 初始绘图
        if len(self.pairs) > 0:
            self.plot_data(self.pair_selector.value)
        else:
            print("警告: 没有足够的特征来创建组合")

    def create_widgets(self):
        """创建所有交互控件"""
        # 排除'Facies'列和只包含一个值的列
        features = [col for col in self.df.columns if col != 'Facies' and len(self.df[col].unique()) > 1]

        # 如果没有足够的特征，添加一些默认值
        if len(features) < 2:
            print("警告: 没有足够的特征用于分析")
            # 添加一些默认列以确保界面可以加载
            if 'Facies' in self.df.columns and len(self.df) > 0:
                self.df['Feature1'] = range(len(self.df))
                self.df['Feature2'] = self.df['Facies']
                features = ['Feature1', 'Feature2']

        self.pairs = [f"{x}-{y}" for x, y in itertools.combinations(features, 2)]
        if not self.pairs:
            self.pairs = ['X-Y']  # 提供一个默认值以防止错误

        # 使用更宽的布局
        wide_layout = Layout(width='300px')

        self.pair_selector = Dropdown(
            options=self.pairs,
            description='特征组合:',
            layout=wide_layout
        )

        self.facies_selector = Dropdown(
            options=sorted(list(range(1, 15))),  # 扩展到14种相类型
            description='Target Facies:',
            layout=wide_layout
        )

        # 添加颜色选择器
        self.color_picker = ColorPicker(
            concise=False,
            description='选中点颜色:',
            value='#ff7f0e',  # 默认颜色
            layout=Layout(width='200px')
        )

        # 添加相类型选择下拉菜单（用于修改颜色）
        self.facies_color_selector = Dropdown(
            options=[],  # 将在plot_data中动态更新
            description='选择相类型:',
            layout=Layout(width='250px')
        )

        # 添加默认颜色选择下拉菜单
        self.color_dropdown = Dropdown(
            options=[(f"{name} ({color})", color) for color, name in self.default_colors],
            description='选择颜色:',
            layout=Layout(width='250px')
        )

        # 添加应用颜色按钮
        self.apply_color_btn = Button(
            description="应用颜色",
            button_style='success',
            layout=Layout(width='120px')
        )

        self.title_input = Text(
            value='沉积相交互编辑器',
            description='图表标题:',
            layout=wide_layout
        )

        self.delete_btn = Button(
            description="删除选中点",
            button_style='danger',
            layout=Layout(width='150px')
        )

        self.modify_btn = Button(
            description="修改相类型",
            button_style='info',
            layout=Layout(width='150px')
        )

        self.save_btn = Button(
            description="保存更改",
            button_style='success',
            layout=Layout(width='150px')
        )

        # 添加图例位置控制按钮
        self.legend_left_btn = Button(
            description="图例左移",
            button_style='info',
            layout=Layout(width='100px')
        )

        self.legend_right_btn = Button(
            description="图例右移",
            button_style='info',
            layout=Layout(width='100px')
        )

        self.output_plot = Output()
        self.output_msg = Output(layout=Layout(height='80px'))

    def setup_ui(self):
        """设置UI布局和事件绑定"""
        self.pair_selector.observe(
            lambda change: self.plot_data(change.new) if change.new in self.pairs else None,
            names='value'
        )
        self.title_input.observe(
            lambda change: self.update_title(change.new),
            names='value'
        )
        self.delete_btn.on_click(self.delete_points)
        self.modify_btn.on_click(self.modify_facies)
        self.save_btn.on_click(lambda _: self.save_to_file())

        # 添加图例位置控制按钮的事件处理
        self.legend_left_btn.on_click(self.move_legend_left)
        self.legend_right_btn.on_click(self.move_legend_right)

        # 修改相类型时更新颜色选择器
        self.facies_selector.observe(lambda change: self.color_picker.value
                                    if change.new in self.colors else None,
                                    names='value')

        # 添加应用颜色按钮事件
        self.apply_color_btn.on_click(self.apply_facies_color)

        self.ui = VBox([
            HBox([self.title_input]),
            HBox([self.pair_selector]),
            self.output_plot,
            HBox([
                Label("操作:"),
                self.delete_btn,
                self.modify_btn,
                self.facies_selector,
                self.save_btn
            ]),
            HBox([
                Label("图例位置:"),
                self.legend_left_btn,
                self.legend_right_btn
            ]),
            HBox([
                self.color_picker
            ]),
            HBox([
                Label("修改颜色:"),
                self.facies_color_selector,
                self.color_dropdown,
                self.apply_color_btn
            ]),
            self.output_msg
        ])

    def update_title(self, new_title):
        """更新图表标题"""
        if self.fig:
            self.fig.update_layout(title=new_title)

    def save_to_file(self):
        """保存数据到原始文件"""
        try:
            # 直接保存当前数据框
            self.current_df.to_csv(ORIGINAL_FILE, index=False)
            with self.output_msg:
                self.output_msg.clear_output()
                print("✓ 数据已保存到原始文件")
        except Exception as e:
            with self.output_msg:
                self.output_msg.clear_output()
                print(f"⚠️ 保存文件时出错: {str(e)}")

    def get_axis_title(self, col_name):
        """生成带单位的坐标轴标题"""
        unit = UNITS.get(col_name.strip(), '')
        if unit:
            return f"{col_name} ({unit})"
        return col_name

    def plot_data(self, pair):
        """绘制交互式散点图"""
        self.current_pair = pair
        try:
            x_col, y_col = pair.split('-')

            # 确保列存在
            if x_col not in self.current_df.columns or y_col not in self.current_df.columns:
                with self.output_msg:
                    self.output_msg.clear_output()
                    print(f"⚠️ 列 '{x_col}' 或 '{y_col}' 不存在。可用列: {list(self.current_df.columns)}")
                return

            with self.output_plot:
                self.output_plot.clear_output(wait=True)

                # 使用FigureWidget以支持交互
                fig = go.FigureWidget()

                # 检查是否为渗透率数据（列名包含'perm'或其他指示符）
                is_perm_data = any(term in y_col.lower() for term in ['perm', 'k', 'permeability'])

                # 按相绘制不同颜色
                # 获取唯一的相类型值并尝试排序
                unique_facies = self.current_df['Facies'].unique()

                # 尝试将所有相类型转换为相同类型进行排序
                try:
                    # 尝试直接排序（如果所有值都是相同类型）
                    sorted_facies = sorted(unique_facies)
                except TypeError:
                    # 如果出现类型错误，将所有值转换为字符串再排序
                    sorted_facies = sorted([str(f) for f in unique_facies])
                    # 尝试将排序后的值转回原始类型
                    try:
                        sorted_facies = [int(f) if f.isdigit() else f for f in sorted_facies]
                    except:
                        pass  # 如果转换失败，保持字符串类型

                # 按排序后的相类型绘制
                for i, facies in enumerate(sorted_facies):
                    # 确保在数据框中查找时使用正确的类型
                    if isinstance(facies, str) and all(not isinstance(f, str) for f in self.current_df['Facies'].unique()):
                        # 如果facies是字符串但数据框中的值不是，尝试转换
                        try:
                            lookup_facies = int(facies) if facies.isdigit() else facies
                        except:
                            lookup_facies = facies
                    else:
                        lookup_facies = facies

                    subset = self.current_df[self.current_df['Facies'] == lookup_facies]

                    # 使用指定的颜色，如果相类型不在颜色字典中则使用默认颜色
                    try:
                        # 尝试直接使用facies作为键
                        color = self.colors.get(facies, None)
                        if color is None:
                            # 如果失败，尝试将facies转换为整数
                            if isinstance(facies, str) and facies.isdigit():
                                color = self.colors.get(int(facies), None)

                        # 如果仍然没有找到颜色，使用默认颜色
                        if color is None:
                            color = self.colors[list(self.colors.keys())[i % len(self.colors)]]
                    except:
                        # 如果出现任何错误，使用默认颜色
                        color = self.colors[list(self.colors.keys())[i % len(self.colors)]]

                    scatter = go.Scatter(
                        x=subset[x_col],
                        y=subset[y_col],
                        mode='markers',
                        marker=dict(
                            size=10,
                            color=color,
                            opacity=1.0,
                            symbol=self.symbols[i % len(self.symbols)]
                        ),
                        name=self.facies_names.get(facies, f'{facies}'),  # 使用自定义名称，不添加前缀
                        customdata=subset.index.tolist(),
                        selectedpoints=[],
                        selected=dict(marker=dict(color='#ff0000', size=14, opacity=1.0)),
                        unselected=dict(marker=dict(opacity=0.8))
                    )
                    fig.add_trace(scatter)

                # 设置图表布局
                layout_dict = dict(
                    title=self.title_input.value,
                    dragmode='select',
                    width=900,
                    height=700,
                    template='none',  # 不使用预设模板，避免额外的线条
                    # 去掉上边框和右边框
                    shapes=[
                        # 左边框
                        dict(
                            type='line',
                            xref='paper', yref='paper',
                            x0=0, y0=0, x1=0, y1=1,
                            line=dict(color='black', width=2)
                        ),
                        # 底边框
                        dict(
                            type='line',
                            xref='paper', yref='paper',
                            x0=0, y0=0, x1=1, y1=0,
                            line=dict(color='black', width=2)
                        )
                    ],

                    xaxis=dict(
                        title=dict(
                            text=self.get_axis_title(x_col),
                            font=dict(size=14, color='black', family='Arial, sans-serif')
                        ),
                        showgrid=True,
                        gridcolor='rgba(200, 200, 200, 0.5)',
                        zeroline=False,  # 禁用零线
                        showline=False,  # 隐藏轴线，因为我们使用自定义形状
                        ticks='outside',
                        tickwidth=2,
                        tickcolor='rgba(0, 0, 0, 1.0)',
                        tickfont=dict(size=12, color='black'),
                        mirror=False,  # 不镜像轴线
                        showspikes=False  # 禁用悬停线
                    ),
                    yaxis=dict(
                        title=dict(
                            text=self.get_axis_title(y_col),
                            font=dict(size=14, color='black', family='Arial, sans-serif')
                        ),
                        showgrid=True,
                        gridcolor='rgba(200, 200, 200, 0.5)',
                        zeroline=False,  # 禁用零线
                        showline=False,  # 隐藏轴线，因为我们使用自定义形状
                        ticks='outside',
                        tickwidth=2,
                        tickcolor='rgba(0, 0, 0, 1.0)',
                        tickfont=dict(size=12, color='black'),
                        mirror=False,  # 不镜像轴线
                        showspikes=False  # 禁用悬停线
                    ),
                    hoverlabel=dict(bgcolor="white"),
                    hovermode="closest",
                    legend=dict(
                        orientation="v",
                        yanchor="top",
                        y=1,
                        xanchor="right",
                        x=1.15,
                        bgcolor="rgba(255, 255, 255, 0.8)",
                        bordercolor="rgba(0, 0, 0, 0.5)",
                        borderwidth=1
                    )
                )

                # 如果是渗透率数据，设置为对数刻度
                if is_perm_data:
                    layout_dict['yaxis']['type'] = 'log'

                    # 确保Y轴范围合适
                    # 确保转换为数值类型并处理可能的字符串值
                    all_y_values = pd.to_numeric(self.current_df[y_col], errors='coerce').dropna().values
                    # 再次确认是数值类型
                    all_y_values = all_y_values.astype(float)
                    positive_y = all_y_values[all_y_values > 0]  # 只取正值用于对数刻度

                    if len(positive_y) > 0:
                        min_y = max(0.001, positive_y.min() * 0.5)  # 设置一个合理的最小值
                        max_y = positive_y.max() * 2
                        layout_dict['yaxis']['range'] = [np.log10(min_y), np.log10(max_y)]

                    # 添加对数轴的自定义刻度
                    layout_dict['yaxis']['tickvals'] = [0.001, 0.01, 0.1, 1, 10, 100, 1000, 10000]
                    layout_dict['yaxis']['ticktext'] = ['0.001', '0.01', '0.1', '1', '10', '100', '1000', '10000']

                fig.update_layout(**layout_dict)

                # 添加悬停信息
                fig.update_traces(
                    hovertemplate=
                    f"<b>{x_col}</b>: %{{x}}<br>" +
                    f"<b>{y_col}</b>: %{{y}}<br>" +
                    "<b>Facies</b>: %{fullData.name}<extra></extra>"
                )

                self.fig = fig

                # 选择点事件回调
                def selection_fn(trace, points, _):  # 使用下划线表示未使用的参数
                    with self.output_msg:
                        self.output_msg.clear_output()
                        if len(points.point_inds) > 0:
                            selected_indices = [trace.customdata[i] for i in points.point_inds]
                            self.selected_points_idx = selected_indices
                            print(
                                f"已选择 {len(selected_indices)} 个点 | 索引: {', '.join(map(str, selected_indices[:5]))}" +
                                ("..." if len(selected_indices) > 5 else ""))

                # 更新相类型选择器的选项
                facies_options = []
                for facies in sorted_facies:
                    facies_name = self.facies_names.get(facies, f"相类型 {facies}")
                    facies_options.append((facies_name, facies))

                self.facies_color_selector.options = facies_options

                # 如果有选项，默认选择第一个
                if facies_options:
                    self.facies_color_selector.value = facies_options[0][1]

                # 绑定选择事件
                for trace in fig.data:
                    trace.on_selection(selection_fn)

                # 显示图表
                display(fig)
        except Exception as e:
            with self.output_msg:
                self.output_msg.clear_output()
                print(f"⚠️ 绘图出错: {str(e)}")

    def delete_points(self, _):
        """删除选中数据点"""
        if not self.selected_points_idx:
            with self.output_msg:
                self.output_msg.clear_output()
                print("⚠️ 请先选择要删除的数据点")
            return

        original_count = len(self.current_df)
        self.current_df = self.current_df.drop(self.selected_points_idx)
        self.current_df.reset_index(drop=True, inplace=True)

        deleted_count = original_count - len(self.current_df)
        self.selected_points_idx = []
        self.plot_data(self.current_pair)

        with self.output_msg:
            self.output_msg.clear_output()
            print(f"✓ 已删除 {deleted_count} 个点 | 原始数量: {original_count} | 剩余数量: {len(self.current_df)}")

    def modify_facies(self, _):
        """修改选中点的相类型"""
        if not self.selected_points_idx:
            with self.output_msg:
                self.output_msg.clear_output()
                print("⚠️ 请先选择要修改的数据点")
            return

        new_facies = self.facies_selector.value
        selected_count = len(self.selected_points_idx)

        # 更新选中点的相类型
        self.current_df.loc[self.selected_points_idx, 'Facies'] = new_facies

        # 更新相类型的颜色（如果用户选择了新颜色）
        self.colors[new_facies] = self.color_picker.value

        self.selected_points_idx = []
        self.plot_data(self.current_pair)

        with self.output_msg:
            self.output_msg.clear_output()
            facies_name = self.facies_names.get(new_facies, str(new_facies))
            print(f"✓ 已将 {selected_count} 个点修改为 {facies_name}")

    def move_legend_left(self, _):
        """将图例向左移动"""
        if self.fig:
            # 获取当前图例位置
            current_x = self.fig.layout.legend.x if hasattr(self.fig.layout.legend, 'x') else 1.15
            # 向左移动0.1个单位
            new_x = max(0.5, current_x - 0.1)  # 限制最小值为0.5
            self.fig.layout.legend.x = new_x

            with self.output_msg:
                self.output_msg.clear_output()
                print(f"✓ 图例已向左移动到位置 x={new_x:.2f}")

    def move_legend_right(self, _):
        """将图例向右移动"""
        if self.fig:
            # 获取当前图例位置
            current_x = self.fig.layout.legend.x if hasattr(self.fig.layout.legend, 'x') else 1.15
            # 向右移动0.1个单位
            new_x = min(2.0, current_x + 0.1)  # 限制最大值为2.0
            self.fig.layout.legend.x = new_x

            with self.output_msg:
                self.output_msg.clear_output()
                print(f"✓ 图例已向右移动到位置 x={new_x:.2f}")

    def apply_facies_color(self, _):
        """应用选中的颜色到相类型"""
        if not self.facies_color_selector.value or not self.color_dropdown.value:
            with self.output_msg:
                self.output_msg.clear_output()
                print("⚠️ 请先选择相类型和颜色")
            return

        # 获取选中的相类型和颜色
        facies_value = self.facies_color_selector.value
        color = self.color_dropdown.value
        facies_name = self.facies_names.get(facies_value, f"相类型 {facies_value}")

        # 更新颜色
        self.colors[facies_value] = color

        # 重新绘制图表
        self.plot_data(self.current_pair)

        with self.output_msg:
            self.output_msg.clear_output()
            print(f"✓ 已将 {facies_name} 的颜色更新为 {color}")




# 启动编辑器
if __name__ == "__main__":
    try:
        # 在这里可以根据需要修改CSV文件路径
        # ORIGINAL_FILE = "你的数据文件路径.csv"

        editor = InteractiveFaciesEditor()
        display(editor.ui)
    except Exception as e:
        print(f"启动编辑器时出错: {str(e)}")
        print("请检查数据文件和列名是否正确。")

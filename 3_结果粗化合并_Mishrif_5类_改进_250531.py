import os
import numpy as np
import pandas as pd

# 全局配置 - 统一参数管理
CONFIG = {
    'min_thickness': 2.0,  # 最小层厚度（米）- 小于此值的层将被合并
    'dominant_ratio': 0.85,  # 主导相最小占比（0-1）- 用于优化层值（降低到0.6更合理）
    'value_range': (1, 10),  # 相值有效范围 - 支持1到10种相类型
    'merge_strategy': 'smart_conservative',  # 合并策略:
        # 'simple' - 简单合并到较厚邻层
        # 'conservative' - 保守合并(优先同相合并)
        # 'smart' - 智能评分合并
        # 'fidelity_first' - 保真度优先合并(太严格，合并少)
        # 'smart_conservative' - 智能保守合并(推荐，平衡合并效果和数据保真度)
    'max_deviation_ratio': 0.35,  # 最大允许偏离比例（35%）- 放宽限制，允许更多合并
    'max_merge_thickness': 20.0,  # 最大合并厚度（米）- 防止合并范围无限扩大
    'force_cleanup_thin_layers': True,  # 强制清理剩余薄层
}

"""
整体合并逻辑：
1. 初始层段识别：根据相值变化点划分地质层段
2. 薄层合并：对厚度<min_thickness的层进行合并
   - 简单策略：总是合并到较厚的邻层
   - 保守策略：优先合并到相同相的邻层
   - 智能策略：基于四维评分选择合并方向：
        a. 值相似性(40%) - 相值越接近得分越高
        b. 厚度平衡(20%) - 合并后厚度越均匀得分越高
        c. 地质连续性(30%) - 原始数据变化越少得分越高
        d. 特殊值保护(10%) - 储层相受特殊保护
   - 保真度优先策略：最小化与原始数据的偏离
        a. 计算合并前后的数据相似度
        b. 只有当偏离度<max_deviation_ratio时才允许合并
        c. 优先选择偏离度最小的合并方向
   - 智能保守策略：平衡合并效果和数据保真度，防止过度合并
3. 层值优化：检查每层主导相占比，超过dominant_ratio则更新层值
4. 最终数据生成：按原始采样间隔输出粗化结果

支持相类型：1-10种相类型（可通过value_range配置调整）
"""


def process_prn_data(input_file, output_file, config=None):
    """
    处理PRN文件，合并薄层并找出每段的主导值

    参数:
    input_file -- 输入PRN文件路径
    output_file -- 输出文件路径
    config -- 配置字典，如果为None则使用全局CONFIG
    """
    if config is None:
        config = CONFIG

    min_thickness = config['min_thickness']
    dominant_ratio = config['dominant_ratio']
    min_value, max_value = config['value_range']
    merge_strategy = config.get('merge_strategy', 'smart')

    try:
        # 尝试不同的分隔符读取文件
        try:
            df = pd.read_csv(input_file, delim_whitespace=True, header=None)
        except:
            try:
                df = pd.read_csv(input_file, sep=None, engine='python', header=None)
            except Exception as e:
                print(f"读取文件 {input_file} 时出错: {e}")
                return False

        # 确保数据至少有两列
        if df.shape[1] < 2:
            print(f"文件 {input_file} 格式错误: 需要至少两列数据")
            return False

        # 只保留前两列数据并重命名
        df = df.iloc[:, :2]
        df.columns = ['Depth', 'Value']

        # 确保数据是数值型
        df['Depth'] = pd.to_numeric(df['Depth'], errors='coerce')
        df['Value'] = pd.to_numeric(df['Value'], errors='coerce')
        df = df.dropna()

        # 将第二列值转为整数并确保在指定范围内
        df['Value'] = df['Value'].astype(int)
        df['Value'] = df['Value'].apply(lambda x: min(max(x, min_value), max_value))

        # 检查数据是否按深度排序
        if not df['Depth'].is_monotonic_increasing:
            df = df.sort_values('Depth').reset_index(drop=True)

        # 计算采样间隔
        depth_diff = df['Depth'].diff().dropna()
        sample_interval = depth_diff.mode()[0]  # 最常见的采样间隔

        print(f"文件 {input_file} 读取成功: {len(df)} 个数据点, 采样间隔 {sample_interval:.3f}米")
        print(f"使用配置: 最小厚度={min_thickness}米, 主导比例={dominant_ratio}, 值范围={min_value}-{max_value}")
        print(f"合并策略: {merge_strategy}")
        print_value_distribution(df, "初始值分布")

        # 步骤1: 识别值变化点，生成初始层段
        layers = identify_initial_layers(df)
        print(f"初始识别层数: {len(layers)}")
        analyze_thin_layers(layers, min_thickness, "初始识别后")

        # 步骤2: 改进的薄层合并
        merged_layers = merge_thin_layers_improved(layers, df, min_thickness, merge_strategy)
        print(f"合并后层数: {len(merged_layers)}")
        analyze_thin_layers(merged_layers, min_thickness, "薄层合并后")

        # 步骤3: 强制清理剩余薄层（如果启用）
        if config.get('force_cleanup_thin_layers', False):
            merged_layers = force_cleanup_remaining_thin_layers(merged_layers, df, min_thickness)
            print(f"强制清理后层数: {len(merged_layers)}")
            analyze_thin_layers(merged_layers, min_thickness, "强制清理后")

        # 步骤4: 为每个段找出主导值
        optimized_layers = optimize_layer_values(merged_layers, df, dominant_ratio)
        analyze_thin_layers(optimized_layers, min_thickness, "层值优化后")

        # 步骤4: 生成最终的采样点数据
        coarsened_df = generate_final_data(optimized_layers, df['Depth'].min(), df['Depth'].max(), sample_interval,
                                           config)

        # 打印最终的值分布情况
        print_value_distribution(coarsened_df, "最终结果值分布:")

        # 计算并打印数据保真度统计
        calculate_data_fidelity(df, coarsened_df)

        # 保存到输出文件
        coarsened_df.to_csv(output_file, sep='\t', header=False, index=False, float_format='%.2f')
        print(f"处理完成，输出到: {output_file}, 共 {len(coarsened_df)} 个数据点")

        return True

    except Exception as e:
        import traceback
        print(f"处理文件 {input_file} 时发生错误: {e}")
        print(traceback.format_exc())
        return False


def identify_initial_layers(df):
    """识别初始层段"""
    # 使用值变化点识别段落
    change_indices = np.where(np.diff(df['Value'].values) != 0)[0] + 1

    # 初始化结果列表
    layers = []

    # 添加第一段
    if len(change_indices) > 0:
        layers.append({
            'start_depth': df['Depth'].iloc[0],
            'end_depth': df['Depth'].iloc[change_indices[0] - 1],
            'value': df['Value'].iloc[0],
            'thickness': df['Depth'].iloc[change_indices[0] - 1] - df['Depth'].iloc[0],
            'point_count': change_indices[0]
        })

        # 添加中间段
        for i in range(len(change_indices) - 1):
            start_idx = change_indices[i]
            end_idx = change_indices[i + 1] - 1
            layers.append({
                'start_depth': df['Depth'].iloc[start_idx],
                'end_depth': df['Depth'].iloc[end_idx],
                'value': df['Value'].iloc[start_idx],
                'thickness': df['Depth'].iloc[end_idx] - df['Depth'].iloc[start_idx],
                'point_count': end_idx - start_idx + 1
            })

        # 添加最后一段
        start_idx = change_indices[-1]
        layers.append({
            'start_depth': df['Depth'].iloc[start_idx],
            'end_depth': df['Depth'].iloc[-1],
            'value': df['Value'].iloc[start_idx],
            'thickness': df['Depth'].iloc[-1] - df['Depth'].iloc[start_idx],
            'point_count': len(df) - start_idx
        })
    else:
        # 只有一段
        layers.append({
            'start_depth': df['Depth'].iloc[0],
            'end_depth': df['Depth'].iloc[-1],
            'value': df['Value'].iloc[0],
            'thickness': df['Depth'].iloc[-1] - df['Depth'].iloc[0],
            'point_count': len(df)
        })

    return pd.DataFrame(layers)


def merge_thin_layers_improved(layers_df, original_df, min_thickness, strategy='smart'):
    """
    改进的薄层合并算法

    参数:
    layers_df -- 初始层段DataFrame
    original_df -- 原始数据DataFrame
    min_thickness -- 最小厚度阈值
    strategy -- 合并策略 ('simple', 'smart', 'conservative')
    """
    print(f"\n开始改进的薄层合并 - 最小厚度:{min_thickness}米, 策略:{strategy}")

    # 创建副本避免修改原数据
    result_df = layers_df.copy().sort_values('start_depth').reset_index(drop=True)

    # 标记薄层
    thin_layers = result_df[result_df['thickness'] < min_thickness]
    print(f"总层数: {len(result_df)}, 薄层数: {len(thin_layers)}")

    if len(thin_layers) == 0:
        print("没有需要合并的薄层")
        return result_df

    # 根据策略选择合并方法
    if strategy == 'simple':
        result_df = merge_thin_layers_simple(result_df, original_df, min_thickness)
    elif strategy == 'conservative':
        result_df = merge_thin_layers_conservative(result_df, original_df, min_thickness)
    elif strategy == 'fidelity_first':
        result_df = merge_thin_layers_fidelity_first(result_df, original_df, min_thickness)
    elif strategy == 'smart_conservative':
        result_df = merge_thin_layers_smart_conservative(result_df, original_df, min_thickness)
    else:  # smart strategy
        result_df = merge_thin_layers_smart(result_df, original_df, min_thickness)

    print(f"薄层合并完成: 最终层数={len(result_df)}")
    return result_df


def merge_thin_layers_simple(layers_df, original_df, min_thickness):
    """简单合并策略：薄层总是与较厚的邻层合并"""
    result_df = layers_df.copy()

    i = 0
    while i < len(result_df):
        if result_df.iloc[i]['thickness'] >= min_thickness:
            i += 1
            continue

        # 处理薄层
        if i == 0:
            # 第一层，与下一层合并
            if i + 1 < len(result_df):
                result_df = merge_layers(result_df, i, i + 1, original_df, keep_first=False)
            else:
                i += 1
        elif i == len(result_df) - 1:
            # 最后一层，与前一层合并
            result_df = merge_layers(result_df, i - 1, i, original_df, keep_first=True)
            break
        else:
            # 中间层，与较厚的邻层合并
            prev_thickness = result_df.iloc[i - 1]['thickness']
            next_thickness = result_df.iloc[i + 1]['thickness']

            if prev_thickness >= next_thickness:
                result_df = merge_layers(result_df, i - 1, i, original_df, keep_first=True)
            else:
                result_df = merge_layers(result_df, i, i + 1, original_df, keep_first=False)

    return result_df


def merge_thin_layers_conservative(layers_df, original_df, min_thickness):
    """保守合并策略：优先保持相同值的层，避免过度合并"""
    result_df = layers_df.copy()

    i = 0
    while i < len(result_df):
        if result_df.iloc[i]['thickness'] >= min_thickness:
            i += 1
            continue

        # 处理薄层
        current_value = result_df.iloc[i]['value']

        if i == 0:
            # 第一层
            if i + 1 < len(result_df):
                result_df = merge_layers(result_df, i, i + 1, original_df, keep_first=False)
            else:
                i += 1
        elif i == len(result_df) - 1:
            # 最后一层
            result_df = merge_layers(result_df, i - 1, i, original_df, keep_first=True)
            break
        else:
            # 中间层，优先考虑值的匹配
            prev_value = result_df.iloc[i - 1]['value']
            next_value = result_df.iloc[i + 1]['value']

            if current_value == prev_value and current_value != next_value:
                # 与前层值相同，合并到前层
                result_df = merge_layers(result_df, i - 1, i, original_df, keep_first=True)
            elif current_value == next_value and current_value != prev_value:
                # 与后层值相同，合并到后层
                result_df = merge_layers(result_df, i, i + 1, original_df, keep_first=False)
            else:
                # 值都不同或都相同，选择较厚的层
                prev_thickness = result_df.iloc[i - 1]['thickness']
                next_thickness = result_df.iloc[i + 1]['thickness']

                if prev_thickness >= next_thickness:
                    result_df = merge_layers(result_df, i - 1, i, original_df, keep_first=True)
                else:
                    result_df = merge_layers(result_df, i, i + 1, original_df, keep_first=False)

    return result_df


def merge_thin_layers_smart(layers_df, original_df, min_thickness):
    """智能合并策略：综合考虑值相似性、厚度和地质连续性"""
    result_df = layers_df.copy()

    i = 0
    while i < len(result_df):
        if result_df.iloc[i]['thickness'] >= min_thickness:
            i += 1
            continue

        # 处理薄层
        current_value = result_df.iloc[i]['value']
        current_thickness = result_df.iloc[i]['thickness']

        if i == 0:
            # 第一层
            if i + 1 < len(result_df):
                result_df = merge_layers(result_df, i, i + 1, original_df, keep_first=False)
            else:
                i += 1
        elif i == len(result_df) - 1:
            # 最后一层
            result_df = merge_layers(result_df, i - 1, i, original_df, keep_first=True)
            break
        else:
            # 中间层，计算合并得分
            prev_value = result_df.iloc[i - 1]['value']
            next_value = result_df.iloc[i + 1]['value']
            prev_thickness = result_df.iloc[i - 1]['thickness']
            next_thickness = result_df.iloc[i + 1]['thickness']

            # 计算与前层合并的得分
            prev_score = calculate_merge_score(
                current_value, prev_value, current_thickness, prev_thickness,
                result_df.iloc[i]['start_depth'], result_df.iloc[i]['end_depth'],
                result_df.iloc[i - 1]['start_depth'], result_df.iloc[i - 1]['end_depth'],
                original_df
            )

            # 计算与后层合并的得分
            next_score = calculate_merge_score(
                current_value, next_value, current_thickness, next_thickness,
                result_df.iloc[i]['start_depth'], result_df.iloc[i]['end_depth'],
                result_df.iloc[i + 1]['start_depth'], result_df.iloc[i + 1]['end_depth'],
                original_df
            )

            # 选择得分更高的合并方向
            if prev_score >= next_score:
                result_df = merge_layers(result_df, i - 1, i, original_df, keep_first=True)
            else:
                result_df = merge_layers(result_df, i, i + 1, original_df, keep_first=False)

    return result_df


def merge_thin_layers_smart_conservative(layers_df, original_df, min_thickness):
    """
    智能保守合并策略：平衡合并效果和数据保真度

    特点：
    1. 优先合并到相同值的邻层
    2. 如果没有相同值，选择偏离度较小的方向
    3. 偏离度限制相对宽松（35%），允许更多合并
    4. 确保合并后的值来自原始数据
    """
    print(f"\n使用智能保守合并策略 - 平衡合并效果和保真度")

    result_df = layers_df.copy()

    i = 0
    while i < len(result_df):
        if result_df.iloc[i]['thickness'] >= min_thickness:
            i += 1
            continue

        # 处理薄层
        current_value = result_df.iloc[i]['value']

        if i == 0:
            # 第一层，只能与下一层合并
            if i + 1 < len(result_df):
                result_df = merge_layers(result_df, i, i + 1, original_df, keep_first=False)
                print(f"合并第{i}层到第{i+1}层")
            else:
                i += 1
        elif i == len(result_df) - 1:
            # 最后一层，只能与前一层合并
            result_df = merge_layers(result_df, i - 1, i, original_df, keep_first=True)
            print(f"合并第{i}层到第{i-1}层")
            break
        else:
            # 中间层，智能选择合并方向
            prev_value = result_df.iloc[i - 1]['value']
            next_value = result_df.iloc[i + 1]['value']

            # 策略1：优先合并到相同值的邻层
            if current_value == prev_value and current_value != next_value:
                merged_df = merge_layers(result_df, i - 1, i, original_df, keep_first=True)
                if len(merged_df) < len(result_df):  # 合并成功
                    result_df = merged_df
                    print(f"合并第{i}层到第{i-1}层 (相同值优先)")
                else:
                    # 合并失败，强制合并（忽略限制）
                    result_df = force_merge_layers(result_df, i - 1, i, original_df, keep_first=True)
                    print(f"强制合并第{i}层到第{i-1}层 (忽略限制)")
            elif current_value == next_value and current_value != prev_value:
                merged_df = merge_layers(result_df, i, i + 1, original_df, keep_first=False)
                if len(merged_df) < len(result_df):  # 合并成功
                    result_df = merged_df
                    print(f"合并第{i}层到第{i+1}层 (相同值优先)")
                else:
                    # 合并失败，强制合并（忽略限制）
                    result_df = force_merge_layers(result_df, i, i + 1, original_df, keep_first=False)
                    print(f"强制合并第{i}层到第{i+1}层 (忽略限制)")
            else:
                # 策略2：计算偏离度，选择较小的方向
                prev_deviation = calculate_merge_deviation(result_df, i - 1, i, original_df, keep_first=True)
                next_deviation = calculate_merge_deviation(result_df, i, i + 1, original_df, keep_first=False)

                max_allowed = CONFIG.get('max_deviation_ratio', 0.35)

                # 如果两个方向偏离度都可接受，选择较小的
                if prev_deviation <= max_allowed and next_deviation <= max_allowed:
                    if prev_deviation <= next_deviation:
                        merged_df = merge_layers(result_df, i - 1, i, original_df, keep_first=True)
                        if len(merged_df) < len(result_df):
                            result_df = merged_df
                            print(f"合并第{i}层到第{i-1}层，偏离度: {prev_deviation:.3f}")
                        else:
                            # 合并失败，强制合并
                            result_df = force_merge_layers(result_df, i - 1, i, original_df, keep_first=True)
                            print(f"强制合并第{i}层到第{i-1}层 (忽略限制)")
                    else:
                        merged_df = merge_layers(result_df, i, i + 1, original_df, keep_first=False)
                        if len(merged_df) < len(result_df):
                            result_df = merged_df
                            print(f"合并第{i}层到第{i+1}层，偏离度: {next_deviation:.3f}")
                        else:
                            # 合并失败，强制合并
                            result_df = force_merge_layers(result_df, i, i + 1, original_df, keep_first=False)
                            print(f"强制合并第{i}层到第{i+1}层 (忽略限制)")
                elif prev_deviation <= max_allowed:
                    merged_df = merge_layers(result_df, i - 1, i, original_df, keep_first=True)
                    if len(merged_df) < len(result_df):
                        result_df = merged_df
                        print(f"合并第{i}层到第{i-1}层，偏离度: {prev_deviation:.3f}")
                    else:
                        # 合并失败，强制合并
                        result_df = force_merge_layers(result_df, i - 1, i, original_df, keep_first=True)
                        print(f"强制合并第{i}层到第{i-1}层 (忽略限制)")
                elif next_deviation <= max_allowed:
                    merged_df = merge_layers(result_df, i, i + 1, original_df, keep_first=False)
                    if len(merged_df) < len(result_df):
                        result_df = merged_df
                        print(f"合并第{i}层到第{i+1}层，偏离度: {next_deviation:.3f}")
                    else:
                        # 合并失败，强制合并
                        result_df = force_merge_layers(result_df, i, i + 1, original_df, keep_first=False)
                        print(f"强制合并第{i}层到第{i+1}层 (忽略限制)")
                else:
                    # 策略3：如果偏离度都太大，尝试强制合并（但仍受厚度限制）
                    prev_thickness = result_df.iloc[i - 1]['thickness']
                    next_thickness = result_df.iloc[i + 1]['thickness']

                    if prev_thickness >= next_thickness:
                        merged_df = merge_layers(result_df, i - 1, i, original_df, keep_first=True)
                        if len(merged_df) < len(result_df):
                            result_df = merged_df
                            print(f"强制合并第{i}层到第{i-1}层 (较厚邻层)，偏离度: {prev_deviation:.3f}")
                        else:
                            # 强制合并失败，使用无限制合并
                            result_df = force_merge_layers(result_df, i - 1, i, original_df, keep_first=True)
                            print(f"无限制强制合并第{i}层到第{i-1}层")
                    else:
                        merged_df = merge_layers(result_df, i, i + 1, original_df, keep_first=False)
                        if len(merged_df) < len(result_df):
                            result_df = merged_df
                            print(f"强制合并第{i}层到第{i+1}层 (较厚邻层)，偏离度: {next_deviation:.3f}")
                        else:
                            # 强制合并失败，使用无限制合并
                            result_df = force_merge_layers(result_df, i, i + 1, original_df, keep_first=False)
                            print(f"无限制强制合并第{i}层到第{i+1}层")

    return result_df


def merge_thin_layers_fidelity_first(layers_df, original_df, min_thickness):
    """
    保真度优先合并策略：最小化与原始数据的偏离

    这个策略专门为了解决用户担心的"合并结果与原始数据偏离太大"的问题
    """
    print(f"\n使用保真度优先合并策略 - 最小化数据偏离")

    result_df = layers_df.copy()

    i = 0
    while i < len(result_df):
        if result_df.iloc[i]['thickness'] >= min_thickness:
            i += 1
            continue

        # 处理薄层
        if i == 0:
            # 第一层，只能与下一层合并
            if i + 1 < len(result_df):
                deviation = calculate_merge_deviation(result_df, i, i + 1, original_df, keep_first=False)
                if deviation <= CONFIG.get('max_deviation_ratio', 0.15):
                    result_df = merge_layers(result_df, i, i + 1, original_df, keep_first=False)
                    print(f"合并第{i}层到第{i+1}层，偏离度: {deviation:.3f}")
                else:
                    print(f"第{i}层偏离度过大({deviation:.3f})，跳过合并")
                    i += 1
            else:
                i += 1
        elif i == len(result_df) - 1:
            # 最后一层，只能与前一层合并
            deviation = calculate_merge_deviation(result_df, i - 1, i, original_df, keep_first=True)
            if deviation <= CONFIG.get('max_deviation_ratio', 0.15):
                result_df = merge_layers(result_df, i - 1, i, original_df, keep_first=True)
                print(f"合并第{i}层到第{i-1}层，偏离度: {deviation:.3f}")
            else:
                print(f"第{i}层偏离度过大({deviation:.3f})，跳过合并")
            break
        else:
            # 中间层，计算两个方向的偏离度
            prev_deviation = calculate_merge_deviation(result_df, i - 1, i, original_df, keep_first=True)
            next_deviation = calculate_merge_deviation(result_df, i, i + 1, original_df, keep_first=False)

            max_allowed = CONFIG.get('max_deviation_ratio', 0.15)

            # 选择偏离度最小且在允许范围内的合并方向
            if prev_deviation <= max_allowed and next_deviation <= max_allowed:
                if prev_deviation <= next_deviation:
                    result_df = merge_layers(result_df, i - 1, i, original_df, keep_first=True)
                    print(f"合并第{i}层到第{i-1}层，偏离度: {prev_deviation:.3f}")
                else:
                    result_df = merge_layers(result_df, i, i + 1, original_df, keep_first=False)
                    print(f"合并第{i}层到第{i+1}层，偏离度: {next_deviation:.3f}")
            elif prev_deviation <= max_allowed:
                result_df = merge_layers(result_df, i - 1, i, original_df, keep_first=True)
                print(f"合并第{i}层到第{i-1}层，偏离度: {prev_deviation:.3f}")
            elif next_deviation <= max_allowed:
                result_df = merge_layers(result_df, i, i + 1, original_df, keep_first=False)
                print(f"合并第{i}层到第{i+1}层，偏离度: {next_deviation:.3f}")
            else:
                print(f"第{i}层两个方向偏离度都过大(前:{prev_deviation:.3f}, 后:{next_deviation:.3f})，跳过合并")
                i += 1

    return result_df


def force_cleanup_remaining_thin_layers(layers_df, original_df, min_thickness):
    """
    强制清理剩余的薄层，忽略偏离度和厚度限制

    这是最后的清理步骤，确保没有薄层残留
    """
    print(f"\n开始强制清理剩余薄层（阈值: {min_thickness}米）...")

    result_df = layers_df.copy()
    cleanup_count = 0

    # 先检查有多少薄层需要清理
    thin_layers_before = result_df[result_df['thickness'] < min_thickness]
    print(f"发现 {len(thin_layers_before)} 个薄层需要强制清理:")
    for idx, layer in thin_layers_before.iterrows():
        print(f"  层 #{idx}: 厚度 {layer['thickness']:.2f}米, 值 {layer['value']}")

    i = 0
    while i < len(result_df):
        if result_df.iloc[i]['thickness'] >= min_thickness:
            i += 1
            continue

        print(f"强制清理薄层 #{i}: 厚度 {result_df.iloc[i]['thickness']:.2f}米")

        # 处理薄层 - 忽略所有限制，强制合并
        if i == 0:
            # 第一层，强制与下一层合并
            if i + 1 < len(result_df):
                result_df = force_merge_layers(result_df, i, i + 1, original_df, keep_first=False)
                cleanup_count += 1
                print(f"  → 强制合并到下一层")
            else:
                i += 1
        elif i == len(result_df) - 1:
            # 最后一层，强制与前一层合并
            result_df = force_merge_layers(result_df, i - 1, i, original_df, keep_first=True)
            cleanup_count += 1
            print(f"  → 强制合并到前一层")
            break
        else:
            # 中间层，选择较厚的邻层强制合并
            prev_thickness = result_df.iloc[i - 1]['thickness']
            next_thickness = result_df.iloc[i + 1]['thickness']

            if prev_thickness >= next_thickness:
                result_df = force_merge_layers(result_df, i - 1, i, original_df, keep_first=True)
                print(f"  → 强制合并到前一层 (较厚)")
            else:
                result_df = force_merge_layers(result_df, i, i + 1, original_df, keep_first=False)
                print(f"  → 强制合并到下一层 (较厚)")
            cleanup_count += 1

    print(f"强制清理完成，共清理了 {cleanup_count} 个薄层")

    # 最终检查是否还有薄层残留
    remaining_thin_layers = result_df[result_df['thickness'] < min_thickness]
    if len(remaining_thin_layers) > 0:
        print(f"⚠️  警告：仍有 {len(remaining_thin_layers)} 个薄层残留:")
        for idx, layer in remaining_thin_layers.iterrows():
            print(f"  层 #{idx}: 厚度 {layer['thickness']:.2f}米, 值 {layer['value']}")
    else:
        print(f"✅ 所有薄层已成功清理")

    return result_df


def force_merge_layers(layers_df, idx1, idx2, original_df, keep_first=True):
    """
    强制合并两个层，忽略厚度和偏离度限制
    """
    result_df = layers_df.copy()

    # 确保idx1 < idx2
    if idx1 > idx2:
        idx1, idx2 = idx2, idx1
        keep_first = not keep_first

    # 获取合并后的属性
    new_start = result_df.iloc[idx1]['start_depth']
    new_end = result_df.iloc[idx2]['end_depth']
    new_thickness = new_end - new_start
    new_point_count = result_df.iloc[idx1]['point_count'] + result_df.iloc[idx2]['point_count']

    # 基于原始数据确定合并后的值（忽略限制）
    new_value = determine_merged_value(new_start, new_end, original_df,
                                     result_df.iloc[idx1]['value'],
                                     result_df.iloc[idx2]['value'],
                                     keep_first)

    # 更新第一个层
    result_df.at[idx1, 'end_depth'] = new_end
    result_df.at[idx1, 'thickness'] = new_thickness
    result_df.at[idx1, 'value'] = new_value
    result_df.at[idx1, 'point_count'] = new_point_count

    # 删除第二个层
    result_df = result_df.drop(idx2).reset_index(drop=True)

    return result_df


def calculate_merge_deviation(layers_df, idx1, idx2, original_df, keep_first=True):
    """
    计算合并前后的数据偏离度

    返回值：0-1之间，0表示完全一致，1表示完全不同
    """
    # 获取合并区域的深度范围
    start_depth = min(layers_df.iloc[idx1]['start_depth'], layers_df.iloc[idx2]['start_depth'])
    end_depth = max(layers_df.iloc[idx1]['end_depth'], layers_df.iloc[idx2]['end_depth'])

    # 获取原始数据
    mask = (original_df['Depth'] >= start_depth) & (original_df['Depth'] <= end_depth)
    original_values = original_df.loc[mask, 'Value'].values

    if len(original_values) == 0:
        return 0.0

    # 关键修正：使用厚度主导值，而不是简单选择某个层的值
    merged_value = determine_merged_value(
        start_depth, end_depth, original_df,
        layers_df.iloc[idx1]['value'], layers_df.iloc[idx2]['value'],
        keep_first
    )

    # 计算偏离度：不同值的比例
    different_count = np.sum(original_values != merged_value)
    deviation_ratio = different_count / len(original_values)

    return deviation_ratio


def calculate_merge_score(val1, val2, thick1, thick2, start1, end1, start2, end2, original_df):
    """
    计算合并得分，综合考虑多个因素

    返回值越高表示合并效果越好
    """
    # 1. 值相似性得分 (0-1)
    if val1 == val2:
        value_score = 1.0
    else:
        # 值差异越小得分越高
        max_diff = 4  # 假设最大值差为4 (5-1)
        value_score = max(0, 1 - abs(val1 - val2) / max_diff)

    # 2. 厚度平衡得分 (0-1)
    total_thickness = thick1 + thick2
    if total_thickness > 0:
        # 避免厚度差异过大
        thickness_ratio = min(thick1, thick2) / max(thick1, thick2)
        thickness_score = thickness_ratio
    else:
        thickness_score = 0.5

    # 3. 地质连续性得分（基于原始数据的一致性）
    continuity_score = calculate_continuity_score(start1, end1, start2, end2, original_df)

    # 4. 特殊值保护得分
    protection_score = calculate_protection_score(val1, val2)

    # 加权综合得分
    weights = {
        'value': 0.4,
        'thickness': 0.2,
        'continuity': 0.3,
        'protection': 0.1
    }

    total_score = (
            weights['value'] * value_score +
            weights['thickness'] * thickness_score +
            weights['continuity'] * continuity_score +
            weights['protection'] * protection_score
    )

    return total_score


def calculate_continuity_score(start1, end1, start2, end2, original_df):
    """计算地质连续性得分"""
    # 获取合并区域的原始数据
    full_start = min(start1, start2)
    full_end = max(end1, end2)

    mask = (original_df['Depth'] >= full_start) & (original_df['Depth'] <= full_end)
    segment_values = original_df.loc[mask, 'Value'].values

    if len(segment_values) <= 1:
        return 0.5

    # 计算值的变化程度
    value_changes = np.sum(np.diff(segment_values) != 0)
    max_possible_changes = len(segment_values) - 1

    if max_possible_changes > 0:
        # 变化越少，连续性越好
        continuity_score = 1 - (value_changes / max_possible_changes)
    else:
        continuity_score = 1.0

    return continuity_score


def calculate_protection_score(val1, val2):
    """
    计算特殊值保护得分
    
    设计背景:
    在地质数据处理中，某些相类型（如储层相）具有特殊重要性，需要避免被过度合并稀释。
    本算法中值2、3代表储层相（如砂岩），是油气勘探的关键目标层段。
    
    保护逻辑:
    1. 当相邻层都是储层相(val1==val2)时，鼓励合并(得分1.0)，因为合并不会改变储层性质
    2. 当储层相与非储层相邻时，降低合并倾向(得分0.3)，避免储层特征被稀释
    3. 普通相合并给中等得分(0.7)，允许适度合并
    
    参数:
    val1, val2 -- 相邻两层的相值
    """
    # 定义重要地质相（储层相）
    important_values = [2, 3]  # 2: 砂岩储层, 3: 灰岩储层

    if val1 in important_values or val2 in important_values:
        if val1 == val2:
            return 1.0  # 相同的重要相，鼓励合并
        else:
            return 0.3  # 不同相，降低合并倾向
    else:
        return 0.7  # 普通相，中等倾向


def merge_layers(layers_df, idx1, idx2, original_df, keep_first=True):
    """
    合并两个相邻层，合并后的值必须是原始数据中该区域的主导值

    参数:
    layers_df -- 层段DataFrame
    idx1, idx2 -- 要合并的两个层的索引
    original_df -- 原始数据，用于确定合并区域的主导值
    keep_first -- True表示保留第一个层的属性，False表示保留第二个层的属性（仅作为备选）
    """
    result_df = layers_df.copy()

    # 确保idx1 < idx2
    if idx1 > idx2:
        idx1, idx2 = idx2, idx1
        keep_first = not keep_first

    # 获取合并后的属性
    new_start = result_df.iloc[idx1]['start_depth']
    new_end = result_df.iloc[idx2]['end_depth']
    new_thickness = new_end - new_start
    new_point_count = result_df.iloc[idx1]['point_count'] + result_df.iloc[idx2]['point_count']

    # 关键检查：防止合并厚度过大
    max_merge_thickness = CONFIG.get('max_merge_thickness', 10.0)
    if new_thickness > max_merge_thickness:
        print(f"  警告：合并厚度 {new_thickness:.1f}米 超过限制 {max_merge_thickness}米，拒绝合并")
        return result_df  # 拒绝合并，返回原数据

    # 关键改进：基于原始数据确定合并后的值
    new_value = determine_merged_value(new_start, new_end, original_df,
                                     result_df.iloc[idx1]['value'],
                                     result_df.iloc[idx2]['value'],
                                     keep_first)

    # 更新第一个层
    result_df.at[idx1, 'end_depth'] = new_end
    result_df.at[idx1, 'thickness'] = new_thickness
    result_df.at[idx1, 'value'] = new_value
    result_df.at[idx1, 'point_count'] = new_point_count

    # 删除第二个层
    result_df = result_df.drop(idx2).reset_index(drop=True)

    return result_df


def determine_merged_value(start_depth, end_depth, original_df, value1, value2, prefer_first=True):
    """
    根据原始数据确定合并区域的值

    核心原则：合并后的值必须是原始数据中该区域厚度最大的值

    参数:
    start_depth, end_depth -- 合并区域的深度范围
    original_df -- 原始数据
    value1, value2 -- 两个层的原始值
    prefer_first -- 当厚度相同时的偏好
    """
    # 获取合并区域内的原始数据
    mask = (original_df['Depth'] >= start_depth) & (original_df['Depth'] <= end_depth)
    segment_data = original_df.loc[mask].copy()

    if len(segment_data) == 0:
        # 如果没有数据，返回偏好值
        return value1 if prefer_first else value2

    # 计算采样间隔（假设数据是等间隔的）
    if len(segment_data) > 1:
        depth_diffs = segment_data['Depth'].diff().dropna()
        sample_interval = depth_diffs.mode()[0] if len(depth_diffs) > 0 else 0.1
    else:
        sample_interval = 0.1  # 默认间隔

    # 计算每个值的总厚度
    value_thickness = {}
    for value in segment_data['Value'].unique():
        value_points = np.sum(segment_data['Value'] == value)
        thickness = value_points * sample_interval
        value_thickness[value] = thickness

    # 找出厚度最大的值
    max_thickness_value = max(value_thickness.items(), key=lambda x: x[1])[0]

    print(f"  合并区域 {start_depth:.1f}-{end_depth:.1f}米 厚度统计: {value_thickness}, 选择值: {max_thickness_value}")

    # 确保返回的值确实存在于原始数据中
    return int(max_thickness_value)


def optimize_layer_values(layers_df, original_df, dominant_ratio):
    """为每个段找出主导值（按厚度计算）"""
    print(f"\n开始优化层值 - 主导厚度比例阈值: {dominant_ratio}")

    # 创建深拷贝防止修改原数据
    result_df = layers_df.copy()
    updated_count = 0

    # 处理每个层段
    for i, row in result_df.iterrows():
        start_depth = row['start_depth']
        end_depth = row['end_depth']

        # 在原始数据中查找该层段内的点
        mask = (original_df['Depth'] >= start_depth) & (original_df['Depth'] <= end_depth)
        segment_data = original_df.loc[mask].copy()

        if len(segment_data) == 0:
            continue

        # 计算采样间隔
        if len(segment_data) > 1:
            depth_diffs = segment_data['Depth'].diff().dropna()
            sample_interval = depth_diffs.mode()[0] if len(depth_diffs) > 0 else 0.1
        else:
            sample_interval = 0.1

        # 计算每个值的厚度和比例
        total_thickness = len(segment_data) * sample_interval
        value_thickness = {}

        for value in segment_data['Value'].unique():
            value_points = np.sum(segment_data['Value'] == value)
            thickness = value_points * sample_interval
            thickness_ratio = thickness / total_thickness
            value_thickness[value] = {'thickness': thickness, 'ratio': thickness_ratio}

        # 找出厚度最大的值
        max_thickness_value = max(value_thickness.items(), key=lambda x: x[1]['thickness'])[0]
        max_thickness_ratio = value_thickness[max_thickness_value]['ratio']

        # 如果主导值厚度占比超过阈值且与当前值不同，则更新
        if max_thickness_ratio >= dominant_ratio and max_thickness_value != row['value']:
            result_df.at[i, 'value'] = max_thickness_value
            print(
                f"层 #{i}: 深度{start_depth:.1f}-{end_depth:.1f}, 更新值 {row['value']} -> {max_thickness_value} (厚度占比: {max_thickness_ratio:.2f})")
            updated_count += 1

    print(f"共更新了 {updated_count} 个层的值")

    # 合并相邻且值相同的层
    result_df = merge_adjacent_same_value_layers(result_df)

    return result_df


def merge_adjacent_same_value_layers(result_df):
    """合并相邻且值相同的层"""
    i = 0
    merged = 0

    while i < len(result_df) - 1:
        current_value = result_df.iloc[i]['value']
        next_value = result_df.iloc[i + 1]['value']

        if current_value == next_value:
            # 合并这两层
            result_df.at[i, 'end_depth'] = result_df.iloc[i + 1]['end_depth']
            result_df.at[i, 'thickness'] = result_df.iloc[i]['end_depth'] - result_df.iloc[i]['start_depth']
            result_df.at[i, 'point_count'] = result_df.iloc[i]['point_count'] + result_df.iloc[i + 1]['point_count']
            result_df = result_df.drop(i + 1).reset_index(drop=True)
            merged += 1
        else:
            i += 1

    if merged > 0:
        print(f"合并了 {merged} 对相邻且值相同的层")

    return result_df


def generate_final_data(layers_df, min_depth, max_depth, sample_interval, config=None):
    """生成最终的采样点数据"""
    if config is None:
        config = CONFIG

    min_value, max_value = config['value_range']

    # 创建深度数组
    depths = np.arange(min_depth, max_depth + sample_interval, sample_interval)

    # 初始化值数组为-1（表示未分配）
    values = np.full(len(depths), -1, dtype=int)

    # 按顺序填充值，使用更精确的边界匹配
    for _, row in layers_df.iterrows():
        start_depth = row['start_depth']
        end_depth = row['end_depth']
        layer_value = row['value']

        # 找到最接近的深度点
        start_idx = np.argmin(np.abs(depths - start_depth))
        end_idx = np.argmin(np.abs(depths - end_depth))

        # 确保索引顺序正确
        if start_idx > end_idx:
            start_idx, end_idx = end_idx, start_idx

        # 填充该层段的值
        values[start_idx:end_idx+1] = layer_value

    # 处理未分配的点（如果有的话）
    unassigned_mask = values == -1
    if np.any(unassigned_mask):
        print(f"警告：发现 {np.sum(unassigned_mask)} 个未分配的深度点")

        # 使用最近邻插值填充未分配的点
        for i in np.where(unassigned_mask)[0]:
            # 找到最近的已分配点
            assigned_indices = np.where(values != -1)[0]
            if len(assigned_indices) > 0:
                nearest_idx = assigned_indices[np.argmin(np.abs(assigned_indices - i))]
                values[i] = values[nearest_idx]
                print(f"  深度 {depths[i]:.2f}米 分配为值 {values[i]}")
            else:
                # 如果没有已分配的点，使用第一层的值
                values[i] = layers_df.iloc[0]['value']

    # 确保值在有效范围内
    values = np.clip(values, min_value, max_value)

    # 合并深度和值
    result_data = np.column_stack((depths, values))

    return pd.DataFrame(result_data, columns=['Depth', 'Value'])


def print_value_distribution(df, message=""):
    """打印值的分布情况"""
    print(f"\n{message}")
    value_counts = df['Value'].value_counts().sort_index()
    total_points = len(df)
    for val, count in value_counts.items():
        percentage = (count / total_points) * 100
        print(f"值 {val}: {count} 个点 ({percentage:.1f}%)")


def calculate_data_fidelity(original_df, processed_df):
    """
    计算处理前后数据的保真度统计

    参数:
    original_df -- 原始数据
    processed_df -- 处理后数据
    """
    print(f"\n=== 数据保真度分析 ===")

    # 确保两个数据框有相同的深度点
    common_depths = set(original_df['Depth']).intersection(set(processed_df['Depth']))

    if len(common_depths) == 0:
        print("警告：原始数据和处理后数据没有共同的深度点")
        return

    # 筛选共同深度点的数据
    orig_subset = original_df[original_df['Depth'].isin(common_depths)].sort_values('Depth')
    proc_subset = processed_df[processed_df['Depth'].isin(common_depths)].sort_values('Depth')

    # 计算总体一致性
    total_points = len(orig_subset)
    matching_points = np.sum(orig_subset['Value'].values == proc_subset['Value'].values)
    overall_fidelity = matching_points / total_points

    print(f"总数据点: {total_points}")
    print(f"一致数据点: {matching_points}")
    print(f"总体保真度: {overall_fidelity:.3f} ({overall_fidelity*100:.1f}%)")

    # 按相值分析保真度（按厚度计算）
    print(f"\n各相值厚度保真度分析:")

    # 计算采样间隔
    if len(orig_subset) > 1:
        depth_diffs = orig_subset['Depth'].diff().dropna()
        sample_interval = depth_diffs.mode()[0] if len(depth_diffs) > 0 else 0.1
    else:
        sample_interval = 0.1

    for value in sorted(orig_subset['Value'].unique()):
        orig_mask = orig_subset['Value'] == value
        orig_thickness = np.sum(orig_mask) * sample_interval

        if orig_thickness > 0:
            # 原始数据中该相值的位置
            orig_positions = orig_subset[orig_mask].index
            # 处理后数据在相同位置的值
            proc_values_at_positions = proc_subset.loc[orig_positions, 'Value'].values
            # 计算该相值的厚度保真度
            preserved_thickness = np.sum(proc_values_at_positions == value) * sample_interval
            thickness_fidelity = preserved_thickness / orig_thickness

            print(f"  相值 {value}: {preserved_thickness:.1f}m/{orig_thickness:.1f}m = {thickness_fidelity:.3f} ({thickness_fidelity*100:.1f}%)")

    # 计算值分布变化（按厚度）
    print(f"\n厚度分布变化:")
    total_thickness = total_points * sample_interval

    # 计算原始数据各值的厚度分布
    orig_thickness_dist = {}
    for value in orig_subset['Value'].unique():
        thickness = np.sum(orig_subset['Value'] == value) * sample_interval
        orig_thickness_dist[value] = thickness

    # 计算处理后数据各值的厚度分布
    proc_thickness_dist = {}
    for value in proc_subset['Value'].unique():
        thickness = np.sum(proc_subset['Value'] == value) * sample_interval
        proc_thickness_dist[value] = thickness

    for value in sorted(set(orig_thickness_dist.keys()).union(set(proc_thickness_dist.keys()))):
        orig_thickness = orig_thickness_dist.get(value, 0)
        proc_thickness = proc_thickness_dist.get(value, 0)
        orig_pct = (orig_thickness / total_thickness) * 100
        proc_pct = (proc_thickness / total_thickness) * 100
        change = proc_pct - orig_pct

        print(f"  相值 {value}: {orig_pct:.1f}% → {proc_pct:.1f}% (变化: {change:+.1f}%)")

    # 保真度评估
    if overall_fidelity >= 0.95:
        print(f"\n✅ 保真度评估: 优秀 (≥95%)")
    elif overall_fidelity >= 0.85:
        print(f"\n✅ 保真度评估: 良好 (≥85%)")
    elif overall_fidelity >= 0.75:
        print(f"\n⚠️  保真度评估: 一般 (≥75%)")
    else:
        print(f"\n❌ 保真度评估: 较差 (<75%)，建议调整参数")

    print("=" * 50)


def analyze_layers(layers_df):
    """分析层段信息"""
    print(f"\n层段分析:")
    print(f"总层数: {len(layers_df)}")
    print(f"平均厚度: {layers_df['thickness'].mean():.2f}米")
    print(f"厚度范围: {layers_df['thickness'].min():.2f} - {layers_df['thickness'].max():.2f}米")

    # 按值统计层数
    value_counts = layers_df['value'].value_counts().sort_index()
    for val, count in value_counts.items():
        avg_thickness = layers_df[layers_df['value'] == val]['thickness'].mean()
        print(f"值 {val}: {count} 层, 平均厚度 {avg_thickness:.2f}米")


def analyze_thin_layers(layers_df, min_thickness, stage_name):
    """详细分析薄层情况"""
    thin_layers = layers_df[layers_df['thickness'] < min_thickness]

    print(f"\n=== {stage_name} 薄层分析 ===")
    print(f"总层数: {len(layers_df)}")
    print(f"薄层数: {len(thin_layers)} (< {min_thickness}米)")

    if len(thin_layers) > 0:
        print(f"薄层厚度范围: {thin_layers['thickness'].min():.3f} - {thin_layers['thickness'].max():.3f}米")
        print(f"薄层平均厚度: {thin_layers['thickness'].mean():.3f}米")

        # 显示前10个薄层的详细信息
        print(f"前10个薄层详情:")
        for i, (idx, layer) in enumerate(thin_layers.head(10).iterrows()):
            print(f"  层#{idx}: 深度{layer['start_depth']:.1f}-{layer['end_depth']:.1f}米, "
                  f"厚度{layer['thickness']:.3f}米, 值{layer['value']}")
            if i >= 9:  # 只显示前10个
                break

        # 按值统计薄层
        thin_value_counts = thin_layers['value'].value_counts().sort_index()
        print(f"薄层按值分布:")
        for val, count in thin_value_counts.items():
            avg_thickness = thin_layers[thin_layers['value'] == val]['thickness'].mean()
            print(f"  值{val}: {count}个薄层, 平均厚度{avg_thickness:.3f}米")
    else:
        print("✅ 没有薄层")

    print("=" * 50)


def process_directory(directory_path, output_directory=None, config=None):
    """
    处理目录中的所有PRN文件

    参数:
    directory_path -- 输入目录路径
    output_directory -- 输出目录路径
    config -- 配置字典，如果为None则使用全局CONFIG
    """
    if config is None:
        config = CONFIG

    if output_directory is None:
        output_directory = os.path.join(directory_path, 'optimized_results')

    # 确保输出目录存在
    if not os.path.exists(output_directory):
        os.makedirs(output_directory)

    # 获取所有PRN文件
    prn_files = [f for f in os.listdir(directory_path) if f.lower().endswith('.prn')]

    if not prn_files:
        print(f"在目录 {directory_path} 中未找到PRN文件")
        return

    # 显示处理进度
    total_files = len(prn_files)
    print(f"找到 {total_files} 个PRN文件待处理")

    successful = 0
    failed = 0

    # 处理每个PRN文件
    for i, prn_file in enumerate(prn_files):
        input_file = os.path.join(directory_path, prn_file)
        output_file = os.path.join(output_directory, prn_file)

        print(f"\n" + "=" * 60)
        print(f"处理文件 [{i + 1}/{total_files}]: {prn_file}")
        print("=" * 60)

        success = process_prn_data(input_file, output_file, config)

        if success:
            successful += 1
        else:
            failed += 1

    print(f"\n" + "=" * 60)
    print(f"批处理完成统计:")
    print(f"成功处理: {successful} 个文件")
    print(f"处理失败: {failed} 个文件")
    print(f"输出目录: {output_directory}")
    print("=" * 60)


# 使用示例
if __name__ == '__main__':
    # 可以选择不同的合并策略
    strategies = {
        'simple': {
            'min_thickness': 1.0,
            'dominant_ratio': 0.9,
            'value_range': (1, 10),  # 支持1-10种相类型
            'merge_strategy': 'simple'
        },
        'smart': {
            'min_thickness': 1.0,
            'dominant_ratio': 0.9,
            'value_range': (1, 10),  # 支持1-10种相类型
            'merge_strategy': 'smart'
        },
        'conservative': {
            'min_thickness': 1.0,
            'dominant_ratio': 0.9,
            'value_range': (1, 10),  # 支持1-10种相类型
            'merge_strategy': 'conservative'
        },
        'fidelity_first': {
            'min_thickness': 1.0,
            'dominant_ratio': 0.7,  # 降低主导比例要求
            'value_range': (1, 10),  # 支持1-10种相类型
            'merge_strategy': 'fidelity_first',
            'max_deviation_ratio': 0.15  # 最大允许15%的偏离（严格）
        },
        'smart_conservative': {
            'min_thickness': 1.0,
            'dominant_ratio': 0.6,  # 更低的主导比例要求
            'value_range': (1, 10),  # 支持1-10种相类型
            'merge_strategy': 'smart_conservative',
            'max_deviation_ratio': 0.35,  # 最大允许35%的偏离（宽松）
            'max_merge_thickness': 8.0,  # 最大合并厚度8米，防止过度合并
            'force_cleanup_thin_layers': True  # 强制清理剩余薄层
        }
    }

    # 选择使用的策略
    selected_strategy = 'smart_conservative'  # 推荐使用智能保守策略，平衡合并效果和保真度
    config = strategies[selected_strategy]

    # 指定PRN文件目录路径
    # 请在此处输入PRN文件目录路径
    directory_path =  r"D:\LQ_2024\MJN_2024\沉积相\Mishrif沉积相\new_test_20250520\4curves_for_train\Mishrif_预测结果_250525 - 副本"
    output_directory = os.path.join(os.path.dirname(directory_path), "optimized_results_0529")

    print(f"处理目录: {directory_path}")
    print(f"输出目录: {output_directory}")
    print(f"使用配置: {config}")

    # 使用配置处理所有文件
    process_directory(directory_path, output_directory, config)

    # 如果只想使用默认配置，可以这样调用：
    # process_directory(directory_path, output_directory)
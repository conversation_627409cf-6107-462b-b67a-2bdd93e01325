import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.preprocessing import StandardScaler
from sklearn.metrics.pairwise import euclidean_distances
from sklearn.cluster import DBSCAN
from scipy import stats
import warnings

warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False


class FaciesAnalyzer:
    def __init__(self, df):
        self.df = df.copy()
        self.variables = ['DEN', 'DT', 'NEU', 'GR']
        self.scaler = StandardScaler()
        self.outliers = []
        self.reclassified_points = []

    def load_data_from_csv(self, file_path):
        """从CSV文件加载数据"""
        try:
            df = pd.read_csv(file_path)
            print(f"数据加载成功，共{len(df)}行数据")
            return df
        except Exception as e:
            print(f"数据加载失败: {e}")
            return None

    def calculate_facies_centroids(self):
        """计算每个相带的质心"""
        centroids = {}
        facies_stats = {}

        for facies in self.df['Facies'].unique():
            facies_data = self.df[self.df['Facies'] == facies]

            # 计算质心
            centroid = facies_data[self.variables].mean()
            centroids[facies] = centroid

            # 计算统计信息
            stats_info = {
                'count': len(facies_data),
                'mean': facies_data[self.variables].mean(),
                'std': facies_data[self.variables].std(),
                'min': facies_data[self.variables].min(),
                'max': facies_data[self.variables].max(),
                '25%': facies_data[self.variables].quantile(0.25),
                '75%': facies_data[self.variables].quantile(0.75)
            }
            facies_stats[facies] = stats_info

        return centroids, facies_stats

    def detect_outliers_statistical(self, method='iqr', threshold=1.5):
        """使用统计方法检测离群点"""
        outliers = []

        for facies in self.df['Facies'].unique():
            facies_mask = self.df['Facies'] == facies
            facies_data = self.df[facies_mask]

            if len(facies_data) < 3:  # 样本太少跳过
                continue

            for var in self.variables:
                if method == 'iqr':
                    # 使用IQR方法
                    Q1 = facies_data[var].quantile(0.25)
                    Q3 = facies_data[var].quantile(0.75)
                    IQR = Q3 - Q1
                    lower_bound = Q1 - threshold * IQR
                    upper_bound = Q3 + threshold * IQR

                    outlier_mask = (facies_data[var] < lower_bound) | (facies_data[var] > upper_bound)

                elif method == 'zscore':
                    # 使用Z-score方法
                    z_scores = np.abs(stats.zscore(facies_data[var]))
                    outlier_mask = z_scores > threshold

                if outlier_mask.any():
                    outlier_indices = facies_data[outlier_mask].index.tolist()
                    for idx in outlier_indices:
                        outliers.append({
                            'index': idx,
                            'original_facies': facies,
                            'variable': var,
                            'value': self.df.loc[idx, var],
                            'method': method,
                            'reason': f'{var}超出正常范围'
                        })

        return outliers

    def detect_outliers_distance(self, distance_threshold=2.0):
        """使用距离方法检测离群点"""
        centroids, _ = self.calculate_facies_centroids()
        outliers = []

        # 标准化数据
        data_scaled = self.scaler.fit_transform(self.df[self.variables])

        for facies in self.df['Facies'].unique():
            facies_mask = self.df['Facies'] == facies
            facies_indices = self.df[facies_mask].index
            facies_data_scaled = data_scaled[facies_mask]

            if len(facies_data_scaled) < 2:
                continue

            # 计算到质心的距离
            centroid_scaled = self.scaler.transform([centroids[facies]])[0]
            distances = euclidean_distances(facies_data_scaled, [centroid_scaled]).flatten()

            # 使用距离阈值识别离群点
            distance_threshold_adjusted = distance_threshold * np.std(distances)
            outlier_mask = distances > distance_threshold_adjusted

            for i, is_outlier in enumerate(outlier_mask):
                if is_outlier:
                    original_idx = facies_indices[i]
                    outliers.append({
                        'index': original_idx,
                        'original_facies': facies,
                        'distance': distances[i],
                        'threshold': distance_threshold_adjusted,
                        'reason': f'距离相带质心过远 (距离={distances[i]:.3f})'
                    })

        return outliers

    def find_best_facies_for_outliers(self, outliers):
        """为离群点找到最合适的相带"""
        centroids, facies_stats = self.calculate_facies_centroids()
        reclassified = []

        # 标准化质心
        centroid_matrix = np.array([centroids[facies].values for facies in sorted(centroids.keys())])
        centroids_scaled = self.scaler.fit_transform(centroid_matrix)

        for outlier in outliers:
            idx = outlier['index']
            original_facies = outlier['original_facies']

            # 获取该点的标准化数据
            point_data = self.df.loc[idx, self.variables].values.reshape(1, -1)
            point_scaled = self.scaler.transform(point_data)[0]

            # 计算到所有相带质心的距离
            distances_to_centroids = {}
            for i, facies in enumerate(sorted(centroids.keys())):
                dist = euclidean_distances([point_scaled], [centroids_scaled[i]])[0][0]
                distances_to_centroids[facies] = dist

            # 找到最近的相带
            best_facies = min(distances_to_centroids, key=distances_to_centroids.get)
            best_distance = distances_to_centroids[best_facies]
            original_distance = distances_to_centroids[original_facies]

            # 计算改进程度
            improvement = original_distance - best_distance

            # 如果最佳相带不是原始相带，且有显著改进
            if best_facies != original_facies and improvement > 0.1:
                # 验证新分类的合理性
                validation_score = self.validate_reclassification(idx, best_facies, facies_stats)

                reclassified.append({
                    'index': idx,
                    'original_facies': original_facies,
                    'new_facies': best_facies,
                    'original_distance': original_distance,
                    'new_distance': best_distance,
                    'improvement': improvement,
                    'validation_score': validation_score,
                    'reason': f'更接近Facies {best_facies}质心',
                    'data_point': self.df.loc[idx, self.variables].to_dict()
                })

        return reclassified

    def validate_reclassification(self, idx, new_facies, facies_stats):
        """验证重新分类的合理性"""
        point_data = self.df.loc[idx, self.variables]
        new_facies_stats = facies_stats[new_facies]

        validation_score = 0
        total_vars = len(self.variables)

        for var in self.variables:
            value = point_data[var]
            mean = new_facies_stats['mean'][var]
            std = new_facies_stats['std'][var]
            q25 = new_facies_stats['25%'][var]
            q75 = new_facies_stats['75%'][var]

            # 检查是否在合理范围内
            if q25 <= value <= q75:
                validation_score += 1  # 在四分位数范围内，得满分
            elif abs(value - mean) <= 2 * std:
                validation_score += 0.5  # 在2倍标准差内，得半分

        return validation_score / total_vars

    def analyze_and_reclassify(self, statistical_method='iqr', statistical_threshold=1.5,
                               distance_threshold=2.0, min_validation_score=0.5):
        """主分析和重分类函数"""
        print("=== Hartha沉积相数据异常点检测与重分类分析 ===\n")

        # 1. 计算原始相带统计信息
        centroids, facies_stats = self.calculate_facies_centroids()

        print("=== 原始相带统计信息 ===")
        for facies in sorted(facies_stats.keys()):
            stats = facies_stats[facies]
            print(f"\nFacies {facies} (样本数: {stats['count']}):")
            for var in self.variables:
                print(f"  {var}: 均值={stats['mean'][var]:.3f}±{stats['std'][var]:.3f}, "
                      f"范围=[{stats['min'][var]:.3f}, {stats['max'][var]:.3f}]")

        # 2. 统计方法检测离群点
        print(f"\n=== 使用{statistical_method}方法检测统计离群点 ===")
        statistical_outliers = self.detect_outliers_statistical(statistical_method, statistical_threshold)
        print(f"检测到 {len(statistical_outliers)} 个统计离群点")

        # 3. 距离方法检测离群点
        print(f"\n=== 使用距离方法检测离群点 ===")
        distance_outliers = self.detect_outliers_distance(distance_threshold)
        print(f"检测到 {len(distance_outliers)} 个距离离群点")

        # 4. 合并离群点（去重）
        all_outlier_indices = set()
        for outlier in statistical_outliers + distance_outliers:
            all_outlier_indices.add(outlier['index'])

        combined_outliers = []
        for idx in all_outlier_indices:
            outlier_info = {'index': idx, 'original_facies': self.df.loc[idx, 'Facies']}
            combined_outliers.append(outlier_info)

        print(f"总共发现 {len(combined_outliers)} 个需要重新评估的点")

        # 5. 为离群点找到最佳相带
        print(f"\n=== 为异常点寻找最佳相带 ===")
        reclassified = self.find_best_facies_for_outliers(combined_outliers)

        # 6. 筛选高质量的重分类建议
        high_quality_reclassified = [r for r in reclassified
                                     if r['validation_score'] >= min_validation_score
                                     and r['improvement'] > 0.2]

        print(f"建议重新分类 {len(high_quality_reclassified)} 个点:")

        for r in high_quality_reclassified:
            print(f"\n数据点 {r['index']}: Facies {r['original_facies']} → Facies {r['new_facies']}")
            print(f"  改进程度: {r['improvement']:.3f}, 验证分数: {r['validation_score']:.3f}")
            print(f"  理由: {r['reason']}")
            print(f"  数据值: DEN={r['data_point']['DEN']:.3f}, DT={r['data_point']['DT']:.1f}, "
                  f"NEU={r['data_point']['NEU']:.3f}, GR={r['data_point']['GR']:.1f}")

        # 7. 应用重分类
        df_reclassified = self.df.copy()
        df_reclassified['Original_Facies'] = df_reclassified['Facies']
        df_reclassified['Reclassification_Reason'] = ''

        for r in high_quality_reclassified:
            idx = r['index']
            df_reclassified.loc[idx, 'Facies'] = r['new_facies']
            df_reclassified.loc[idx, 'Reclassification_Reason'] = r['reason']

        return df_reclassified, high_quality_reclassified, centroids, facies_stats

    def plot_before_after_comparison(self, df_reclassified, reclassified_points):
        """绘制重分类前后对比图"""
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))

        # 常用的交汇图组合
        plot_combinations = [
            ('DEN', 'NEU'),
            ('DT', 'GR'),
            ('DEN', 'DT'),
            ('NEU', 'GR'),
            ('DEN', 'GR'),
            ('DT', 'NEU')
        ]

        colors = ['red', 'blue', 'green', 'orange', 'purple', 'brown', 'pink', 'gray']

        for i, (var1, var2) in enumerate(plot_combinations):
            ax = axes[i // 3, i % 3]

            # 绘制原始分类
            for j, facies in enumerate(sorted(self.df['Facies'].unique())):
                original_data = self.df[self.df['Facies'] == facies]
                ax.scatter(original_data[var1], original_data[var2],
                           c=colors[j % len(colors)], alpha=0.6, s=30,
                           label=f'Original Facies {facies}', marker='o')

            # 标记重分类的点
            for r in reclassified_points:
                idx = r['index']
                x_val = self.df.loc[idx, var1]
                y_val = self.df.loc[idx, var2]

                # 原始位置用X标记
                ax.scatter(x_val, y_val, c='black', s=100, marker='x', linewidth=3)

                # 添加箭头指向新分类
                new_facies_color = colors[r['new_facies'] % len(colors)]
                ax.annotate(f"→F{r['new_facies']}",
                            (x_val, y_val),
                            xytext=(5, 5),
                            textcoords='offset points',
                            fontsize=8,
                            color=new_facies_color,
                            weight='bold')

            ax.set_xlabel(var1)
            ax.set_ylabel(var2)
            ax.set_title(f'{var1} vs {var2} 交汇图\n(X标记=重分类点)')
            ax.grid(True, alpha=0.3)
            ax.legend(fontsize=8)

        plt.tight_layout()
        plt.suptitle('重分类前后对比 - 交汇图分析', fontsize=16, y=1.02)
        plt.show()

    def generate_reclassification_report(self, df_reclassified, reclassified_points):
        """生成重分类报告"""
        print("\n" + "=" * 60)
        print("重分类详细报告")
        print("=" * 60)

        # 统计重分类情况
        reclassification_summary = {}
        for r in reclassified_points:
            original = r['original_facies']
            new = r['new_facies']
            key = f"{original}→{new}"
            reclassification_summary[key] = reclassification_summary.get(key, 0) + 1

        print("\n重分类统计:")
        for transition, count in reclassification_summary.items():
            print(f"  Facies {transition}: {count} 个点")

        # 重分类前后的相带分布
        print(f"\n相带分布变化:")
        original_counts = self.df['Facies'].value_counts().sort_index()
        new_counts = df_reclassified['Facies'].value_counts().sort_index()

        print("Facies  原始数量  新数量  变化")
        print("-" * 30)
        for facies in sorted(set(list(original_counts.index) + list(new_counts.index))):
            orig = original_counts.get(facies, 0)
            new = new_counts.get(facies, 0)
            change = new - orig
            change_str = f"+{change}" if change > 0 else str(change)
            print(f"  {facies}        {orig}       {new}     {change_str}")

        # 重分类依据分析
        print(f"\n重分类依据分析:")
        validation_scores = [r['validation_score'] for r in reclassified_points]
        improvements = [r['improvement'] for r in reclassified_points]

        print(f"  平均验证分数: {np.mean(validation_scores):.3f}")
        print(f"  平均改进程度: {np.mean(improvements):.3f}")
        print(f"  最大改进程度: {np.max(improvements):.3f}")


# 主程序
def main():
    """主程序"""
    # 读取实际CSV文件
    file_path = r"D:\LQ_2024\MJN_2024\沉积相\Hartha沉积相\20250527_Hartha_沉积相基础数据\MJ-29_ori_cor_5class_250529 - 原始备份.csv"

    try:
        df = pd.read_csv(file_path, encoding='utf-8')
        print("数据加载成功 (UTF-8编码)")
    except UnicodeDecodeError:
        try:
            df = pd.read_csv(file_path, encoding='gbk')
            print("数据加载成功 (GBK编码)")
        except UnicodeDecodeError:
            df = pd.read_csv(file_path, encoding='utf-8-sig')
            print("数据加载成功 (UTF-8-BOM编码)")

    print("数据概览:")
    print(df.head())
    print(f"\n数据形状: {df.shape}")
    print(f"列名: {df.columns.tolist()}")
    print(f"相带分布: {df['Facies'].value_counts().sort_index()}")

    # 检查数据完整性
    missing_cols = []
    required_cols = ['DEN', 'DT', 'NEU', 'GR', 'Facies']
    for col in required_cols:
        if col not in df.columns:
            missing_cols.append(col)

    if missing_cols:
        print(f"\n警告: 缺少必需的列: {missing_cols}")
        print("请检查CSV文件的列名是否正确")
        return None, None, None

    # 创建分析器
    analyzer = FaciesAnalyzer(df)

    # 执行分析和重分类
    df_reclassified, reclassified_points, centroids, facies_stats = analyzer.analyze_and_reclassify(
        statistical_method='iqr',
        statistical_threshold=1.5,
        distance_threshold=1.8,
        min_validation_score=0.4
    )

    # 绘制对比图
    if reclassified_points:
        analyzer.plot_before_after_comparison(df_reclassified, reclassified_points)

        # 生成详细报告
        analyzer.generate_reclassification_report(df_reclassified, reclassified_points)

        # 保存结果到同一目录
        output_path = r"D:\LQ_2024\MJN_2024\沉积相\Hartha沉积相\20250527_Hartha_沉积相基础数据\MJ-29_重分类结果.csv"
        df_reclassified.to_csv(output_path, index=False, encoding='utf-8-sig')
        print(f"\n结果已保存到: {output_path}")

    else:
        print("\n未发现需要重分类的异常点，原始分类较为合理。")

    return df, df_reclassified, reclassified_points


if __name__ == "__main__":
    # 直接运行实际数据分析
    original_df, reclassified_df, reclassified_points = main()
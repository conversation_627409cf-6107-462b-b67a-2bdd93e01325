import numpy as np

def fix_contour_format(input_file, output_file):
    # 读取原始数据
    data = []
    with open(input_file, 'r', encoding='utf-8-sig') as f:
        for line in f:
            # 跳过注释行和空行
            if line.strip().startswith('#') or not line.strip():
                continue
            try:
                # 处理数据行：去除前导空格并分割
                parts = [float(x) for x in line.strip().split()]
                if len(parts) == 4:
                    data.append(parts)
            except ValueError:
                print(f"跳过无效行: {line.strip()}")
    
    data = np.array(data)
    
    # 检查是否形成闭合轮廓
    first_point = data[0, :2]
    last_point = data[-1, :2]    # 将数据转换为所需格式
    with open(output_file, 'w') as f:
        for x, y, _, _ in data:
            # 格式化输出，确保坐标有6位小数，且只有单个空格分隔，第四列保持为1
            f.write(f"{x:.6f} {y:.6f} 1 1.000000\n")

if __name__ == "__main__":
    input_file = "geo_contours.prn"
    output_file = "geo_contours_fixed.prn"
    fix_contour_format(input_file, output_file)
    print(f"已将修改后的格式保存到 {output_file}")

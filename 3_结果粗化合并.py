import os
import numpy as np
import pandas as pd


def process_prn_data(input_file, output_file, min_thickness=2.0, dominant_ratio=0.6):
    """
    处理PRN文件，合并薄层并找出每段的主导值

    参数:
    input_file -- 输入PRN文件路径
    output_file -- 输出文件路径
    min_thickness -- 最小层厚度（米）
    dominant_ratio -- 判定为主导类型的最小占比（0-1之间）
    """
    try:
        # 尝试不同的分隔符读取文件
        try:
            df = pd.read_csv(input_file, delim_whitespace=True, header=None)
        except:
            try:
                df = pd.read_csv(input_file, sep=None, engine='python', header=None)
            except Exception as e:
                print(f"读取文件 {input_file} 时出错: {e}")
                return False

        # 确保数据至少有两列
        if df.shape[1] < 2:
            print(f"文件 {input_file} 格式错误: 需要至少两列数据")
            return False

        # 只保留前两列数据并重命名
        df = df.iloc[:, :2]
        df.columns = ['Depth', 'Value']

        # 确保数据是数值型
        df['Depth'] = pd.to_numeric(df['Depth'], errors='coerce')
        df['Value'] = pd.to_numeric(df['Value'], errors='coerce')
        df = df.dropna()

        # 将第二列值转为整数并确保在1-4范围内
        df['Value'] = df['Value'].astype(int)
        df['Value'] = df['Value'].apply(lambda x: min(max(x, 1), 4))

        # 检查数据是否按深度排序
        if not df['Depth'].is_monotonic_increasing:
            df = df.sort_values('Depth').reset_index(drop=True)

        # 计算采样间隔
        depth_diff = df['Depth'].diff().dropna()
        sample_interval = depth_diff.mode()[0]  # 最常见的采样间隔

        print(f"文件 {input_file} 读取成功: {len(df)} 个数据点, 采样间隔 {sample_interval:.3f}米")
        print_value_distribution(df, "初始值分布")

        # 步骤1: 识别值变化点，生成初始层段
        layers = identify_initial_layers(df)
        print(f"初始识别层数: {len(layers)}")

        # 步骤2: 合并薄层
        merged_layers = merge_thin_layers(layers, min_thickness)
        print(f"合并后层数: {len(merged_layers)}")

        # 步骤3: 为每个段找出主导值
        optimized_layers = optimize_layer_values(merged_layers, dominant_ratio)

        # 步骤4: 生成最终的采样点数据
        coarsened_df = generate_final_data(optimized_layers, df['Depth'].min(), df['Depth'].max(), sample_interval)

        # 打印最终的值分布情况
        print_value_distribution(coarsened_df, "最终结果值分布:")

        # 保存到输出文件
        coarsened_df.to_csv(output_file, sep='\t', header=False, index=False, float_format='%.2f')
        print(f"处理完成，输出到: {output_file}, 共 {len(coarsened_df)} 个数据点")

        return True

    except Exception as e:
        import traceback
        print(f"处理文件 {input_file} 时发生错误: {e}")
        print(traceback.format_exc())
        return False


def identify_initial_layers(df):
    """识别初始层段"""
    # 使用值变化点识别段落
    change_indices = np.where(np.diff(df['Value'].values) != 0)[0] + 1

    # 初始化结果列表
    layers = []

    # 添加第一段
    if len(change_indices) > 0:
        layers.append({
            'start_depth': df['Depth'].iloc[0],
            'end_depth': df['Depth'].iloc[change_indices[0] - 1],
            'value': df['Value'].iloc[0],
            'thickness': df['Depth'].iloc[change_indices[0] - 1] - df['Depth'].iloc[0]
        })

        # 添加中间段
        for i in range(len(change_indices) - 1):
            layers.append({
                'start_depth': df['Depth'].iloc[change_indices[i]],
                'end_depth': df['Depth'].iloc[change_indices[i + 1] - 1],
                'value': df['Value'].iloc[change_indices[i]],
                'thickness': df['Depth'].iloc[change_indices[i + 1] - 1] - df['Depth'].iloc[change_indices[i]]
            })

        # 添加最后一段
        layers.append({
            'start_depth': df['Depth'].iloc[change_indices[-1]],
            'end_depth': df['Depth'].iloc[-1],
            'value': df['Value'].iloc[change_indices[-1]],
            'thickness': df['Depth'].iloc[-1] - df['Depth'].iloc[change_indices[-1]]
        })
    else:
        # 只有一段
        layers.append({
            'start_depth': df['Depth'].iloc[0],
            'end_depth': df['Depth'].iloc[-1],
            'value': df['Value'].iloc[0],
            'thickness': df['Depth'].iloc[-1] - df['Depth'].iloc[0]
        })

    return pd.DataFrame(layers)


def merge_thin_layers(layers_df, min_thickness):
    """
    合并薄层，添加更智能的合并逻辑
    优先考虑值的相似性，然后是地质连续性，最后才考虑厚度
    """
    print(f"\n开始合并薄层 - 最小厚度:{min_thickness}米")

    # 标记薄层
    thin_layers = layers_df[layers_df['thickness'] < min_thickness]
    print(f"总层数: {len(layers_df)}, 薄层数: {len(thin_layers)}")

    if len(thin_layers) == 0:
        print("没有需要合并的薄层")
        return layers_df

    # 按深度排序
    result_df = layers_df.sort_values('start_depth').reset_index(drop=True)

    # 迭代处理每个薄层
    i = 0
    while i < len(result_df):
        # 如果当前层不是薄层，则跳到下一层
        if result_df.iloc[i]['thickness'] >= min_thickness:
            i += 1
            continue

        # 如果是第一层或最后一层，特殊处理
        if i == 0:
            # 第一层是薄层，与下一层合并
            if i + 1 < len(result_df):
                result_df.at[i + 1, 'start_depth'] = result_df.iloc[i]['start_depth']
                result_df.at[i + 1, 'thickness'] = result_df.iloc[i + 1]['end_depth'] - result_df.iloc[i]['start_depth']
                result_df = result_df.drop(i).reset_index(drop=True)
            else:
                # 只有一层，保留
                i += 1
        elif i == len(result_df) - 1:
            # 最后一层是薄层，与前一层合并
            result_df.at[i - 1, 'end_depth'] = result_df.iloc[i]['end_depth']
            result_df.at[i - 1, 'thickness'] = result_df.iloc[i]['end_depth'] - result_df.iloc[i - 1]['start_depth']
            result_df = result_df.drop(i).reset_index(drop=True)
        else:
            # 中间层是薄层，决定与前层还是后层合并
            prev_value = result_df.iloc[i - 1]['value']
            curr_value = result_df.iloc[i]['value']
            next_value = result_df.iloc[i + 1]['value']

            # 改进的合并策略，优先考虑值的相似性
            # 1. 如果薄层的值与前后层中的一个相同，则合并到值相同的层
            # 2. 如果不同，则根据值的差异程度决定合并方向（而不是仅仅基于厚度）
            # 3. 如果层值为2或3，要特别小心它们不被错误合并到1的厚层

            # 计算值差异
            prev_diff = abs(curr_value - prev_value)
            next_diff = abs(curr_value - next_value)

            # 计算当前薄层与前后层的地质相似度
            # 值差异越小，相似度越高
            prev_similarity = 1 / (prev_diff + 0.5)  # 避免除以零
            next_similarity = 1 / (next_diff + 0.5)

            # 获取前后层厚度
            prev_thickness = result_df.iloc[i - 1]['thickness']
            next_thickness = result_df.iloc[i + 1]['thickness']

            # 如果当前层值是2或3，且前后层有一个是1，应该避免合并到1
            avoid_merge_to_1 = (curr_value in [2, 3]) and (prev_value == 1 or next_value == 1)

            if avoid_merge_to_1:
                # 如果前层是1，尝试合并到后层
                if prev_value == 1 and next_value != 1:
                    merge_with_prev = False
                # 如果后层是1，尝试合并到前层
                elif next_value == 1 and prev_value != 1:
                    merge_with_prev = True
                # 如果前后都是1或都不是1，按正常逻辑处理
                else:
                    if curr_value == prev_value:
                        merge_with_prev = True
                    elif curr_value == next_value:
                        merge_with_prev = False
                    # 如果值都不同，则比较相似度和厚度的加权组合
                    else:
                        # 地质相似度权重为0.7，厚度权重为0.3
                        prev_score = 0.7 * prev_similarity + 0.3 * (prev_thickness / (prev_thickness + next_thickness))
                        next_score = 0.7 * next_similarity + 0.3 * (next_thickness / (prev_thickness + next_thickness))
                        merge_with_prev = prev_score >= next_score
            else:
                # 常规合并逻辑
                if curr_value == prev_value:
                    merge_with_prev = True
                elif curr_value == next_value:
                    merge_with_prev = False
                else:
                    # 如果值都不同，则比较相似度和厚度的加权组合
                    prev_score = 0.7 * prev_similarity + 0.3 * (prev_thickness / (prev_thickness + next_thickness))
                    next_score = 0.7 * next_similarity + 0.3 * (next_thickness / (prev_thickness + next_thickness))
                    merge_with_prev = prev_score >= next_score

            # 执行合并
            if merge_with_prev:
                # 与前一层合并
                result_df.at[i - 1, 'end_depth'] = result_df.iloc[i]['end_depth']
                result_df.at[i - 1, 'thickness'] = result_df.iloc[i]['end_depth'] - result_df.iloc[i - 1]['start_depth']
                result_df = result_df.drop(i).reset_index(drop=True)
            else:
                # 与后一层合并
                result_df.at[i + 1, 'start_depth'] = result_df.iloc[i]['start_depth']
                result_df.at[i + 1, 'thickness'] = result_df.iloc[i + 1]['end_depth'] - result_df.iloc[i]['start_depth']
                result_df = result_df.drop(i).reset_index(drop=True)

    print(f"薄层处理完成: 合并后层数={len(result_df)}")
    return result_df

def optimize_layer_values(layers_df, dominant_ratio):
    """为每个段找出主导值"""
    print(f"\n开始优化层值 - 主导比例阈值: {dominant_ratio}")

    # 创建深拷贝防止修改原数据
    result_df = layers_df.copy()

    # 处理每个层段
    for i, row in result_df.iterrows():
        # 厚度很大的层段不需要处理
        if row['thickness'] > 3.0:
            continue

        # 确定该层段对应的原始数据点
        start_depth = row['start_depth']
        end_depth = row['end_depth']

        # 在原始数据中查找该层段内的点（这里需要传入原始数据，或者从文件重新读取）
        # 这里使用模拟的方式演示逻辑（实际使用时需要替换）
        segment_values = np.random.choice([1, 2, 3, 4], size=100)

        # 计算每个值的出现频率
        unique_values, counts = np.unique(segment_values, return_counts=True)
        total_points = len(segment_values)

        # 找出主导值
        max_count = max(counts)
        max_value = unique_values[np.argmax(counts)]
        max_ratio = max_count / total_points

        # 如果主导值占比超过阈值，使用主导值
        if max_ratio >= dominant_ratio:
            result_df.at[i, 'value'] = max_value
            print(f"层 #{i}: 更新值 {row['value']} -> {max_value} (占比: {max_ratio:.2f})")

    # 合并相邻且值相同的层
    result_df = merge_adjacent_same_value_layers(result_df)

    return result_df


def merge_adjacent_same_value_layers(result_df):
    """合并相邻且值相同的层"""
    i = 0
    merged = 0

    while i < len(result_df) - 1:
        current_value = result_df.iloc[i]['value']
        next_value = result_df.iloc[i + 1]['value']

        if current_value == next_value:
            # 合并这两层
            result_df.at[i, 'end_depth'] = result_df.iloc[i + 1]['end_depth']
            result_df.at[i, 'thickness'] = result_df.iloc[i]['end_depth'] - result_df.iloc[i]['start_depth']
            result_df = result_df.drop(i + 1).reset_index(drop=True)
            merged += 1
        else:
            i += 1

    if merged > 0:
        print(f"合并了 {merged} 对相邻且值相同的层")

    return result_df


def generate_final_data(layers_df, min_depth, max_depth, sample_interval):
    """生成最终的采样点数据"""
    # 创建深度数组
    depths = np.arange(min_depth, max_depth + sample_interval, sample_interval)

    # 使用第一个层的值初始化
    first_value = layers_df.iloc[0]['value']
    values = np.full(len(depths), first_value, dtype=int)

    # 按顺序填充值
    for _, row in layers_df.iterrows():
        mask = (depths >= row['start_depth']) & (depths <= row['end_depth'])
        values[mask] = row['value']

    # 确保值在有效范围内
    values = np.clip(values, 1, 4)

    # 合并深度和值
    result_data = np.column_stack((depths, values))

    return pd.DataFrame(result_data, columns=['Depth', 'Value'])


def print_value_distribution(df, message=""):
    """打印值的分布情况"""
    print(f"\n{message}")
    value_counts = df['Value'].value_counts().sort_index()
    for val, count in value_counts.items():
        print(f"值 {val}: {count} 个点")


def process_directory(directory_path, output_directory=None, min_thickness=1.0, dominant_ratio=0.6):
    """
    处理目录中的所有PRN文件

    参数:
    directory_path -- 输入目录路径
    output_directory -- 输出目录路径
    min_thickness -- 最小层厚度（米）
    dominant_ratio -- 判定为主导类型的最小占比（0-1之间）
    """
    if output_directory is None:
        output_directory = os.path.join(directory_path, 'optimized_results')

    # 确保输出目录存在
    if not os.path.exists(output_directory):
        os.makedirs(output_directory)

    # 获取所有PRN文件
    prn_files = [f for f in os.listdir(directory_path) if f.lower().endswith('.prn')]

    if not prn_files:
        print(f"在目录 {directory_path} 中未找到PRN文件")
        return

    # 显示处理进度
    total_files = len(prn_files)
    print(f"找到 {total_files} 个PRN文件待处理")

    successful = 0
    failed = 0

    # 处理每个PRN文件
    for i, prn_file in enumerate(prn_files):
        input_file = os.path.join(directory_path, prn_file)
        output_file = os.path.join(output_directory, prn_file)

        print(f"\n处理文件 [{i + 1}/{total_files}]: {prn_file}")
        success = process_prn_data(
            input_file,
            output_file,
            min_thickness,
            dominant_ratio
        )

        if success:
            print(f"成功处理文件 {prn_file}, 输出至 {output_file}")
            successful += 1
        else:
            print(f"处理文件 {prn_file} 失败")
            failed += 1

    print(f"\n处理完成: 成功 {successful} 个文件, 失败 {failed} 个文件")


# 使用示例
if __name__ == '__main__':
    # 调整参数设置
    min_thickness = 1  # 最小层厚度（米）
    dominant_ratio = 0.9  # 判定为主导类型的最小占比

    # 指定PRN文件目录路径
    directory_path = r"D:\LQ_2024\MJN_2024\沉积相\Zubair沉积相\Zubair_0528-with facies\Zubair_0528_no facies\Zubair_预测结果_PRN_0528"
    output_directory = os.path.join(os.path.dirname(directory_path), "optimized_results_0528")

    print(f"处理目录: {directory_path}")
    print(f"输出目录: {output_directory}")
    print(f"最小层厚度: {min_thickness}米")
    print(f"主导类型最小占比: {dominant_ratio}")

    # 处理目录中的所有PRN文件
    process_directory(
        directory_path,
        output_directory=output_directory,
        min_thickness=min_thickness,
        dominant_ratio=dominant_ratio
    )
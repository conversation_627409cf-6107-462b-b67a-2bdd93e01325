import os
import pandas as pd
import numpy as np

path = r"D:\LQ_2024\MJN_2024\沉积相\Zubair沉积相\Zubair_0528-with facies\Zubair_0528_no facies"

for filename in os.listdir(path):
    if filename.endswith(".csv"):
        filepath = os.path.join(path, filename)

        # 读取CSV文件
        df = pd.read_csv(filepath, header=None)

        # 清理列名（第一行）
        for i in range(len(df.columns)):
            if isinstance(df.iloc[0, i], str):
                # 移除列名中的后缀
                header = df.iloc[0, i].strip()
                header = header.replace('EODfacies_0528_Lsy_continue', 'Facies')
                header = header.replace('_Zubair', '')
                df.iloc[0, i] = header

        # 保存第一行（列名）
        headers = df.iloc[0].copy()

        # 删除第二行（单位行）并且从第三行开始转换为数值
        data_part = df.iloc[2:].copy()
        for col in data_part.columns:
            data_part[col] = pd.to_numeric(data_part[col], errors='coerce')

        # 删除包含 -999.25 的行
        data_part = data_part[~(data_part == -999.25).any(axis=1)]

        # 创建新的DataFrame，只包含列名和有效数据
        new_df = pd.DataFrame([headers])  # 添加列名作为第一行
        new_df = pd.concat([new_df, data_part], ignore_index=True)

        # 保存处理后的文件
        new_df.to_csv(filepath, index=False, header=False, encoding='utf-8-sig')

        print(f"✅ 已处理: {filename} | 剩余行数: {len(data_part)}")

print("🎉 处理完成！列名后缀已清理，单位行已删除，无效值(-999.25)已删除。")
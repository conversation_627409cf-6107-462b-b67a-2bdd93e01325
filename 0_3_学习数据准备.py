import pandas as pd
import os
import glob
import random
import numpy as np

# 输入和输出文件路径设置
INPUT_DIR = r'D:\LQ_2024\MJN_2024\沉积相\Hartha沉积相\0511_6curves'
OUTPUT_FILE = r'D:\LQ_2024\MJN_2024\沉积相\Hartha沉积相\0511_6curves\sampled_well_data_20250512.csv'
SAMPLE_SIZE = 100  # 每个文件随机抽取的行数

def sample_csv_files():
    """
    从指定目录下遍历所有CSV文件，从每个文件中随机抽取指定行数的数据，
    并将所有抽样数据合并保存到一个新的CSV文件中。

    CSV文件格式预期包含以下列：DEPTH, GR, DEN, NEU, DT, PERM, PHIE
    """
    # 检查输入目录是否存在
    if not os.path.exists(INPUT_DIR):
        print(f"错误：输入目录 {INPUT_DIR} 不存在")
        return

    # 获取目录下所有CSV文件
    csv_files = glob.glob(os.path.join(INPUT_DIR, "*.csv"))

    if not csv_files:
        print(f"警告：在 {INPUT_DIR} 目录下未找到CSV文件")
        return

    # 创建一个空的DataFrame用于存储所有抽样数据
    all_samples = pd.DataFrame()

    # 遍历每个CSV文件并抽样
    for file_path in csv_files:
        file_name = os.path.basename(file_path)
        try:
            # 读取CSV文件
            df = pd.read_csv(file_path)

            # 检查文件是否有足够的行数
            if len(df) <= SAMPLE_SIZE:
                print(f"文件 {file_name} 行数不足 {SAMPLE_SIZE}，使用全部 {len(df)} 行")
                sampled_df = df
            else:
                # 随机抽取指定行数
                sampled_indices = random.sample(range(len(df)), SAMPLE_SIZE)
                sampled_df = df.iloc[sampled_indices].copy()

            # 添加文件名作为井名列
            well_name = os.path.splitext(file_name)[0]
            sampled_df['WELL'] = well_name

            # 将抽样数据添加到总DataFrame
            all_samples = pd.concat([all_samples, sampled_df], ignore_index=True)

            print(f"从文件 {file_name} 成功抽取 {len(sampled_df)} 行数据")

        except Exception as e:
            print(f"处理文件 {file_name} 时出错: {str(e)}")

    # 检查是否有抽样数据
    if all_samples.empty:
        print("未能从任何文件中抽取数据")
        return

    # 保存合并后的抽样数据到新CSV文件
    try:
        # 确保输出目录存在
        output_dir = os.path.dirname(OUTPUT_FILE)
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        all_samples.to_csv(OUTPUT_FILE, index=False)
        print(f"成功将抽样数据保存到: {OUTPUT_FILE}")
        print(f"总共抽取了 {len(all_samples)} 行数据，来自 {len(csv_files)} 个文件")

        # 显示数据统计信息
        print("\n数据统计信息:")
        print(f"列名: {', '.join(all_samples.columns)}")
        print(f"每个井的样本数:")
        print(all_samples['WELL'].value_counts())

    except Exception as e:
        print(f"保存数据时出错: {str(e)}")

if __name__ == '__main__':
    # 设置随机种子以确保结果可重现
    random.seed(42)
    np.random.seed(42)

    # 执行CSV文件抽样
    sample_csv_files()
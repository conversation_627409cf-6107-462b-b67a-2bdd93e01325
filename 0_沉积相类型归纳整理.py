import pandas as pd
import numpy as np
import os
import matplotlib.pyplot as plt
import seaborn as sns
from matplotlib.colors import ListedColormap
from matplotlib.lines import Line2D


def classify_facies(df, consider_properties=True):
    """
    将岩性数据归纳为5种沉积微相类型，基于岩性、沉积环境和物性参数

    参数:
    df: 包含Lithofacies、Lithofacies Association和物性参数的DataFrame
    consider_properties: 是否考虑物性参数进行分类调整

    返回:
    添加了Facies列的DataFrame
    """
    # 创建一个新列用于存储微相类型
    df['Facies'] = np.nan

    # 创建物性参数的数值列
    if consider_properties:
        # 处理渗透率列中的非数值数据
        if 'Permeability' in df.columns:
            df['Permeability_num'] = pd.to_numeric(df['Permeability'].replace('NMP', np.nan), errors='coerce')

        # 处理孔隙度列
        if 'Porosity' in df.columns:
            df['Porosity_num'] = pd.to_numeric(df['Porosity'], errors='coerce')

        # 处理密度列
        if 'Density' in df.columns:
            df['Density_num'] = pd.to_numeric(df['Density'], errors='coerce')

    # 定义各沉积相类型的物性特征范围 - 基于图像分析的自然分区
    facies_property_ranges = {
        1: {'Porosity': (25, 40), 'Permeability': (50, 10000), 'Density': (2.65, 2.70)}, # Facies 1: 藻类-鲕粒潮间带相（物性最好）
        2: {'Porosity': (20, 35), 'Permeability': (10, 100), 'Density': (2.65, 2.71)},   # Facies 2: 内坡滩相（物性次好）
        3: {'Porosity': (15, 25), 'Permeability': (1, 10), 'Density': (2.68, 2.72)},     # Facies 3: 内坡后滩相（中等物性）
        4: {'Porosity': (10, 20), 'Permeability': (0.1, 1), 'Density': (2.69, 2.73)},    # Facies 4: 内坡受保护潟湖相（较低物性）
        5: {'Porosity': (3, 10), 'Permeability': (0.01, 0.1), 'Density': (2.70, 2.75)}   # Facies 5: 中坡相（最低物性）
    }

    # 定义沉积相合并关系
    facies_merging = {
        1: "极高能浅水相 (Very High Energy Shallow Water Facies)", # Facies 1: 藻类-鲕粒潮间带相（物性最好）
        2: "高能浅水相 (High Energy Shallow Water Facies)",        # Facies 2: 内坡滩相（物性次好）
        3: "中能潮坪相 (Medium Energy Tidal Flat Facies)",         # Facies 3: 内坡后滩相（中等物性）
        4: "低能受限相 (Low Energy Restricted Facies)",            # Facies 4: 内坡受保护潟湖相（较低物性）
        5: "低能开阔相 (Low Energy Open Facies)"                   # Facies 5: 中坡相（最低物性）
    }

    # 保存原始的沉积相名称
    facies_names = {
        1: "Algal-Oncoidal Intertidal Facies",     # 藻类-鲕粒潮间带相（物性最好）
        2: "Inner Ramp Shoal Facies",              # 内坡滩相（物性次好）
        3: "Inner Ramp Backshoal Facies",          # 内坡后滩相（中等物性）
        4: "Inner Ramp Protected Lagoon Facies",   # 内坡受保护潟湖相（较低物性）
        5: "Mid-Ramp Facies"                       # 中坡相（最低物性）
    }

    # 保存沉积相合并信息
    df.attrs['facies_merging'] = facies_merging
    df.attrs['facies_names'] = facies_names

    # 定义复合条件处理函数
    def has_multiple_environments(row, environments):
        """检查一个样本是否同时属于多个环境"""
        if not isinstance(row['Lithofacies Association'], str):
            return False
        count = sum(env in row['Lithofacies Association'] for env in environments)
        return count > 1

    # 添加复合环境标记列
    df['Multiple_Environments'] = df.apply(
        lambda row: has_multiple_environments(
            row, ['Backshoal', 'Algal', 'Intertidal', 'Lagoon', 'Subtidal', 'Mid - Ramp']
        ),
        axis=1
    )

    # 函数：计算样本与各沉积相类型物性特征的匹配度
    def calculate_property_match(row, facies_property_ranges):
        """计算样本与各沉积相类型物性特征的匹配度，使用基于区域的分类方法"""
        matches = {}

        # 检查是否有必要的物性数据
        has_porosity = 'Porosity_num' in row and pd.notna(row['Porosity_num'])
        has_permeability = 'Permeability_num' in row and pd.notna(row['Permeability_num'])

        # 如果没有足够的物性数据，返回空匹配
        if not (has_porosity and has_permeability):
            for facies in facies_property_ranges.keys():
                matches[facies] = 0
            return matches

        # 获取样本的物性值
        porosity = row['Porosity_num']
        permeability = row['Permeability_num']

        # 定义区域边界函数 - 基于图像中的自然分区
        def in_region_1(por, perm):
            # 区域1：极高物性区域（未在图上标出，但对应于极高孔隙度和渗透率）
            return por > 30 and perm > 100

        def in_region_2(por, perm):
            # 区域2：高物性区域
            # 这是一个不规则形状，大致在孔隙度20-30%，渗透率10-300 mD范围内
            if por < 20 or por > 35:
                return False
            if perm < 10:
                return False

            # 定义区域2的上边界（与藻类-鲕粒潮间带相的分界）
            if por > 25 and perm > 50:
                return False  # 这是藻类-鲕粒潮间带相区域

            # 定义区域2的下边界（与区域3的分界）
            # 这是一条从(20,10)到(30,1)的曲线
            boundary_perm = 10 * np.exp(-0.23 * (por - 20))
            if perm < boundary_perm:
                return False  # 这是区域3

            return True

        def in_region_3(por, perm):
            # 区域3：中等物性区域
            # 这是一个不规则形状，大致在孔隙度15-25%，渗透率1-10 mD范围内
            if por < 12 or por > 25:
                return False
            if perm < 0.5 or perm > 20:
                return False

            # 定义区域3的上边界（与区域2的分界）
            # 这是一条从(20,10)到(30,1)的曲线
            upper_boundary_perm = 10 * np.exp(-0.23 * (por - 20))
            if perm > upper_boundary_perm and por >= 20:
                return False  # 这是区域2

            # 定义区域3的下边界（与区域4的分界）
            # 这是一条从(15,1)到(20,0.1)的曲线
            lower_boundary_perm = 1 * np.exp(-0.46 * (por - 15))
            if perm < lower_boundary_perm and por >= 15:
                return False  # 这是区域4

            return True

        def in_region_4(por, perm):
            # 区域4：低物性区域
            # 这是一个不规则形状，大致在孔隙度10-20%，渗透率0.1-1 mD范围内
            if por < 8 or por > 20:
                return False
            if perm < 0.05 or perm > 2:
                return False

            # 定义区域4的上边界（与区域3的分界）
            # 这是一条从(15,1)到(20,0.1)的曲线
            upper_boundary_perm = 1 * np.exp(-0.46 * (por - 15))
            if perm > upper_boundary_perm and por >= 15:
                return False  # 这是区域3

            # 定义区域4的下边界（与区域5的分界）
            # 这是一条从(8,0.1)到(15,0.01)的曲线
            lower_boundary_perm = 0.1 * np.exp(-0.35 * (por - 8))
            if perm < lower_boundary_perm:
                return False  # 这是区域5

            return True

        def in_region_5(por, perm):
            # 区域5：极低物性区域
            # 这是一个不规则形状，大致在孔隙度<10%，渗透率<0.1 mD范围内
            if por > 15:
                return False

            # 定义区域5的上边界（与区域4的分界）
            # 这是一条从(8,0.1)到(15,0.01)的曲线
            if por >= 8:
                upper_boundary_perm = 0.1 * np.exp(-0.35 * (por - 8))
                if perm > upper_boundary_perm:
                    return False  # 这是区域4

            return perm < 0.1  # 渗透率必须很低

        # 定义藻类-鲕粒潮间带相的区域函数（物性最好的区域）
        def in_region_algal_oncoidal(por, perm):
            # 藻类-鲕粒潮间带相：极高物性区域
            return por > 25 and perm > 50

        # 检查样本属于哪个区域
        region_functions = {
            1: in_region_algal_oncoidal,  # 物性最好区域对应于facies 1 - Algal-Oncoidal Intertidal Facies
            2: in_region_2,  # 区域2（高物性区域）对应于facies 2 - Inner Ramp Shoal Facies
            3: in_region_3,  # 区域3（中等物性区域）对应于facies 3 - Inner Ramp Backshoal Facies
            4: in_region_4,  # 区域4（低物性区域）对应于facies 4 - Inner Ramp Protected Lagoon Facies
            5: in_region_5   # 区域5（极低物性区域）对应于facies 5 - Mid-Ramp Facies
        }

        # 计算每个区域的匹配度
        for facies, region_func in region_functions.items():
            if region_func(porosity, permeability):
                matches[facies] = 1.0
            else:
                # 计算到区域边界的距离作为匹配度
                # 这里简化处理，使用物性范围的匹配度
                score = 0
                total = 2  # 只考虑孔隙度和渗透率

                # 检查孔隙度
                min_val, max_val = facies_property_ranges[facies]['Porosity']
                if min_val <= porosity <= max_val:
                    score += 1
                elif porosity < min_val:
                    score += max(0, 1 - (min_val - porosity) / min_val)
                else:  # porosity > max_val
                    score += max(0, 1 - (porosity - max_val) / max_val)

                # 检查渗透率（使用对数尺度）
                min_val, max_val = facies_property_ranges[facies]['Permeability']
                log_perm = np.log10(permeability)
                log_min = np.log10(min_val)
                log_max = np.log10(max_val)

                if log_min <= log_perm <= log_max:
                    score += 1
                elif log_perm < log_min:
                    score += max(0, 1 - (log_min - log_perm) / 2)  # 2个数量级的差异
                else:  # log_perm > log_max
                    score += max(0, 1 - (log_perm - log_max) / 2)  # 2个数量级的差异

                matches[facies] = score / total

        return matches

    # 如果考虑物性参数，计算每个样本的物性匹配度
    if consider_properties:
        # 创建物性匹配度列
        for facies in facies_property_ranges.keys():
            df[f'Property_Match_{facies}'] = 0.0

        # 计算每个样本的物性匹配度
        for idx, row in df.iterrows():
            matches = calculate_property_match(row, facies_property_ranges)
            for facies, match in matches.items():
                df.at[idx, f'Property_Match_{facies}'] = match

    # 按优先级顺序进行分类
    # 1. 颗粒滩相(Grainstone Shoal Facies) - 最高优先级
    # 首先基于薄片鉴定的沉积相（Lithofacies Association）
    shoal_env_mask = (
        df['Lithofacies Association'].str.contains('Backshoal', na=False) |
        df['Lithofacies Association'].str.contains('Shoal', na=False) |
        df['Lithofacies Association'].str.contains('Bank', na=False) |
        df['Lithofacies Association'].str.contains('Bar', na=False)
    )

    # 应用基于薄片鉴定的分类
    df.loc[shoal_env_mask, 'Facies'] = 2
    shoal_count_env = shoal_env_mask.sum()

    # 如果考虑物性参数（作为辅助判断依据）
    if consider_properties:
        # 确保物性列存在
        has_porosity = 'Porosity' in df.columns
        has_permeability = 'Permeability' in df.columns

        # 创建物性数值列（如果尚未创建）
        if has_porosity and 'Porosity_num' not in df.columns:
            df['Porosity_num'] = pd.to_numeric(df['Porosity'], errors='coerce')

        if has_permeability and 'Permeability_num' not in df.columns:
            df['Permeability_num'] = pd.to_numeric(df['Permeability'].replace('NMP', np.nan), errors='coerce')

        # 基于物性的分类（辅助依据，仅用于未分类样本）
        property_masks = {1: [], 2: [], 3: [], 4: [], 5: []}

        # 藻类-鲕粒潮间带相（物性最好区域）- 对应Facies 1
        if has_porosity and has_permeability:
            # 基于物性的极高物性区域
            mask1 = (
                (df['Facies'].isna()) &  # 仅处理未分类样本
                (df['Porosity_num'] > 25) &  # 极高孔隙度
                (df['Permeability_num'] > 50)  # 极高渗透率
            )
            property_masks[1].append(mask1)

            # Algal或Oncoidal环境中的样本
            mask2 = (
                (df['Facies'].isna()) &  # 仅处理未分类样本
                (
                    (df['Lithofacies Association'].str.contains('Algal', na=False)) |
                    (df['Lithofacies Association'].str.contains('Oncoidal', na=False))
                ) &
                (df['Porosity_num'] > 20)  # 较高孔隙度
            )
            property_masks[1].append(mask2)

        # 区域2（高物性区域）- 对应Facies 2
        if has_porosity and has_permeability:
            # 基本物性条件
            mask1 = (
                (df['Facies'].isna()) &  # 仅处理未分类样本
                (df['Porosity_num'].between(20, 35)) &  # 高孔隙度
                (df['Permeability_num'] > 10) &  # 中高渗透率
                (df['Permeability_num'] <= 100)  # 不是极高渗透率
            )
            property_masks[2].append(mask1)

            # Inner Ramp Shoal或Backshoal环境
            mask2 = (
                (df['Facies'].isna()) &  # 仅处理未分类样本
                (
                    (df['Lithofacies Association'].str.contains('Shoal', na=False)) |
                    (df['Lithofacies Association'].str.contains('Backshoal', na=False))
                ) &
                (df['Porosity_num'] > 15) &  # 较高孔隙度
                (df['Permeability_num'] > 5)  # 中等渗透率
            )
            property_masks[2].append(mask2)

        # 区域3（中等物性区域）- 对应Facies 3
        if has_porosity and has_permeability:
            # 基本物性条件
            mask1 = (
                (df['Facies'].isna()) &  # 仅处理未分类样本
                (df['Porosity_num'].between(15, 25)) &  # 中等孔隙度
                (df['Permeability_num'].between(1, 10))  # 中等渗透率
            )
            property_masks[3].append(mask1)

            # Inner Ramp Backshoal或Lagoon环境
            mask2 = (
                (df['Facies'].isna()) &  # 仅处理未分类样本
                (
                    (df['Lithofacies Association'].str.contains('Backshoal', na=False)) |
                    (df['Lithofacies Association'].str.contains('Lagoon', na=False))
                ) &
                (df['Porosity_num'].between(12, 20)) &  # 中等孔隙度
                (df['Permeability_num'].between(0.5, 5))  # 中低渗透率
            )
            property_masks[3].append(mask2)

        # 区域4（低物性区域）- 对应Facies 4
        if has_porosity and has_permeability:
            # 基本物性条件
            mask1 = (
                (df['Facies'].isna()) &  # 仅处理未分类样本
                (df['Porosity_num'].between(10, 20)) &  # 中低孔隙度
                (df['Permeability_num'].between(0.1, 1))  # 低渗透率
            )
            property_masks[4].append(mask1)

            # Protected Lagoon环境
            mask2 = (
                (df['Facies'].isna()) &  # 仅处理未分类样本
                (df['Lithofacies Association'].str.contains('Lagoon', na=False)) &
                (df['Lithofacies Association'].str.contains('Protected', na=False)) &
                (df['Porosity_num'] < 15)  # 低孔隙度
            )
            property_masks[4].append(mask2)

        # 区域5（极低物性区域）- 对应Facies 5
        if has_porosity and has_permeability:
            # 基本物性条件
            mask1 = (
                (df['Facies'].isna()) &  # 仅处理未分类样本
                (
                    (df['Porosity_num'] < 10) |  # 极低孔隙度
                    (df['Permeability_num'] < 0.1)  # 极低渗透率
                )
            )
            property_masks[5].append(mask1)

            # Mid-Ramp或Clay prone环境
            mask2 = (
                (df['Facies'].isna()) &  # 仅处理未分类样本
                (
                    (df['Lithofacies Association'].str.contains('Mid - Ramp', na=False)) |
                    (df['Lithofacies Association'].str.contains('Clay', na=False))
                )
            )
            property_masks[5].append(mask2)

        # 应用物性辅助分类
        facies_counts = {}
        for facies, masks in property_masks.items():
            if masks:
                # 合并该沉积相的所有条件
                combined_mask = pd.Series(False, index=df.index)
                for mask in masks:
                    combined_mask = combined_mask | mask

                # 应用分类
                df.loc[combined_mask, 'Facies'] = facies
                facies_counts[facies] = combined_mask.sum()
            else:
                facies_counts[facies] = 0

        # 计算总共分类的样本数
        total_classified_count = sum(facies_counts.values())
        print(f"Total {total_classified_count} samples classified based on physical properties")

        # 打印分类结果
        facies_names = {
            1: "Algal-Oncoidal Intertidal Facies",     # 藻类-鲕粒潮间带相（物性最好）
            2: "Inner Ramp Shoal Facies",              # 内坡滩相（物性次好）
            3: "Inner Ramp Backshoal Facies",          # 内坡后滩相（中等物性）
            4: "Inner Ramp Protected Lagoon Facies",   # 内坡受保护潟湖相（较低物性）
            5: "Mid-Ramp Facies"                       # 中坡相（最低物性）
        }

        # 打印基于薄片鉴定的分类结果
        if shoal_count_env > 0:
            print(f"Identified {shoal_count_env} samples as {facies_names[2]} (2) based on thin section analysis")

        # 打印基于物性的分类结果
        print("\nPhysical property-based classification results:")
        for facies, count in facies_counts.items():
            if count > 0:
                print(f"  Identified {count} samples as {facies_names[facies]} ({facies}) based on physical properties")

        print(f"Total {sum(facies_counts.values())} samples classified based on physical properties")
    else:
        # 如果不考虑物性，仅基于薄片鉴定
        print(f"Identified {shoal_count_env} samples as Inner Ramp Shoal Facies (2) based on thin section analysis only")

    # 检查内坡滩相识别结果
    shoal_count = (df['Facies'] == 2).sum()
    if shoal_count == 0:
        print("Warning: No samples identified as Inner Ramp Shoal Facies (2)")
        # 如果没有识别出内坡滩相，强制将部分G和PG岩性样本归为内坡滩相
        grain_mask = (df['Lithofacies'].isin(['G', 'PG'])) & (df['Facies'].isna())
        if grain_mask.any():
            df.loc[grain_mask, 'Facies'] = 2
            print(f"Forced {grain_mask.sum()} grain-supported samples to be classified as Inner Ramp Shoal Facies (2)")

    # 应用物性修正机制
    if consider_properties:
        # 记录原始分类结果
        df['Original_Facies'] = df['Facies'].copy()

        # 物性异常样本的阈值
        property_threshold = 0.7  # 物性匹配度阈值

        # 找出物性异常的样本
        anomaly_samples = []
        for idx, row in df.iterrows():
            if pd.notna(row['Facies']):
                current_facies = int(row['Facies'])
                current_match = row[f'Property_Match_{current_facies}']

                # 检查是否有更匹配的沉积相类型
                better_matches = []
                for facies in facies_property_ranges.keys():
                    if facies != current_facies and row[f'Property_Match_{facies}'] > current_match + 0.2:
                        better_matches.append((facies, row[f'Property_Match_{facies}']))

                # 如果有更匹配的沉积相类型，考虑修正
                if better_matches and current_match < property_threshold:
                    better_matches.sort(key=lambda x: x[1], reverse=True)
                    best_facies, best_match = better_matches[0]

                    # 记录异常样本
                    anomaly_samples.append({
                        'Index': idx,
                        'Depth': row['Depth'],
                        'Lithofacies': row['Lithofacies'],
                        'Environment': row['Lithofacies Association'],
                        'Original_Facies': current_facies,
                        'Suggested_Facies': best_facies,
                        'Original_Match': current_match,
                        'Suggested_Match': best_match,
                        'Porosity': row.get('Porosity_num', np.nan),
                        'Permeability': row.get('Permeability_num', np.nan),
                        'Density': row.get('Density_num', np.nan)
                    })

                    # 修正分类
                    df.at[idx, 'Facies'] = best_facies

        # 打印物性修正结果
        if anomaly_samples:
            print(f"\nProperty-based corrections applied to {len(anomaly_samples)} samples:")
            for sample in anomaly_samples[:5]:  # 只显示前5个
                original_facies = sample['Original_Facies']
                suggested_facies = sample['Suggested_Facies']
                print(f"  Sample at depth {sample['Depth']}: {sample['Lithofacies']} - '{sample['Environment']}'")
                print(f"    Changed from {facies_names[original_facies]} to {facies_names[suggested_facies]}")
                print(f"    Property match: {sample['Original_Match']:.2f} -> {sample['Suggested_Match']:.2f}")

            if len(anomaly_samples) > 5:
                print(f"    ... and {len(anomaly_samples) - 5} more samples")

            # 保存异常样本信息
            df.attrs['property_corrections'] = anomaly_samples

    # 2. 潮间带-藻类相(Intertidal-Algal Facies)
    # 基于薄片鉴定的沉积相
    intertidal_mask = (
        (df['Lithofacies Association'].str.contains('Algal', na=False)) |
        (df['Lithofacies Association'].str.contains('Intertidal', na=False))
    ) & (df['Facies'].isna())  # 只处理未分类的样本

    # 应用基于薄片鉴定的分类
    df.loc[intertidal_mask, 'Facies'] = 1
    intertidal_count = intertidal_mask.sum()
    if intertidal_count > 0:
        print(f"Identified {intertidal_count} samples as Intertidal-Algal Facies (1) based on thin section analysis")

    # 3. 受保护潟湖相(Protected Lagoon Facies)
    # 基于薄片鉴定的沉积相
    lagoon_mask = (
        (df['Lithofacies Association'].str.contains('Lagoon', na=False))
    ) & (df['Facies'].isna())  # 只处理未分类的样本

    # 应用基于薄片鉴定的分类
    df.loc[lagoon_mask, 'Facies'] = 3
    lagoon_count = lagoon_mask.sum()
    if lagoon_count > 0:
        print(f"Identified {lagoon_count} samples as Protected Lagoon Facies (3) based on thin section analysis")

    # 4. 潮下带相(Subtidal Facies)
    # 基于薄片鉴定的沉积相
    subtidal_mask = (
        (df['Lithofacies Association'].str.contains('Subtidal', na=False))
    ) & (df['Facies'].isna())  # 只处理未分类的样本

    # 应用基于薄片鉴定的分类
    df.loc[subtidal_mask, 'Facies'] = 4
    subtidal_count = subtidal_mask.sum()
    if subtidal_count > 0:
        print(f"Identified {subtidal_count} samples as Subtidal Facies (4) based on thin section analysis")

    # 5. 中坡相(Mid-Ramp Facies)
    # 基于薄片鉴定的沉积相
    midramp_mask = (
        (df['Lithofacies Association'].str.contains('Mid - Ramp', na=False))
    ) & (df['Facies'].isna())  # 只处理未分类的样本

    # 应用基于薄片鉴定的分类
    df.loc[midramp_mask, 'Facies'] = 5
    midramp_count = midramp_mask.sum()
    if midramp_count > 0:
        print(f"Identified {midramp_count} samples as Mid-Ramp Facies (5) based on thin section analysis")

    # 处理复合环境：如果有样本同时包含多个环境关键词，且尚未分类
    # 这种情况很少见，因为薄片鉴定通常会给出明确的环境
    # 但为了代码的健壮性，我们仍然处理这种情况
    if consider_properties:
        # 检查是否有未分类样本
        unclassified_after_env = df['Facies'].isna().sum()
        if unclassified_after_env > 0:
            print(f"Note: {unclassified_after_env} samples could not be classified based on thin section analysis alone")
            print("Applying physical property-based classification for these samples...")

            # 基于物性参数进行辅助分类
            if 'Porosity_num' in df.columns and 'Permeability_num' in df.columns:
                # 定义沉积相名称
                facies_names = {
                    1: "Algal-Oncoidal Intertidal Facies",     # 藻类-鲕粒潮间带相（物性最好）
                    2: "Inner Ramp Shoal Facies",              # 内坡滩相（物性次好）
                    3: "Inner Ramp Backshoal Facies",          # 内坡后滩相（中等物性）
                    4: "Inner Ramp Protected Lagoon Facies",   # 内坡受保护潟湖相（较低物性）
                    5: "Mid-Ramp Facies"                       # 中坡相（最低物性）
                }

                # 藻类-鲕粒潮间带相（物性最好区域）- 对应Facies 1
                extreme_high_prop_mask = (
                    (df['Facies'].isna()) &
                    (df['Porosity_num'] > 25) &
                    (df['Permeability_num'] > 50)
                )
                df.loc[extreme_high_prop_mask, 'Facies'] = 1
                extreme_high_prop_count = extreme_high_prop_mask.sum()
                if extreme_high_prop_count > 0:
                    print(f"  {extreme_high_prop_count} extremely high-property samples classified as {facies_names[1]} (1)")

                # 区域2（高物性区域）- 对应Facies 2
                high_prop_mask = (
                    (df['Facies'].isna()) &
                    (df['Porosity_num'].between(20, 35)) &
                    (df['Permeability_num'] > 10) &
                    (df['Permeability_num'] <= 100)
                )
                df.loc[high_prop_mask, 'Facies'] = 2
                high_prop_count = high_prop_mask.sum()
                if high_prop_count > 0:
                    print(f"  {high_prop_count} high-property samples classified as {facies_names[2]} (2)")

                # 区域3（中等物性区域）- 对应Facies 3
                mid_high_prop_mask = (
                    (df['Facies'].isna()) &
                    (df['Porosity_num'].between(15, 25)) &
                    (df['Permeability_num'].between(1, 10))
                )
                df.loc[mid_high_prop_mask, 'Facies'] = 3
                mid_high_prop_count = mid_high_prop_mask.sum()
                if mid_high_prop_count > 0:
                    print(f"  {mid_high_prop_count} medium-high property samples classified as {facies_names[3]} (3)")

                # 区域4（低物性区域）- 对应Facies 4
                mid_low_prop_mask = (
                    (df['Facies'].isna()) &
                    (df['Porosity_num'].between(10, 20)) &
                    (df['Permeability_num'].between(0.1, 1))
                )
                df.loc[mid_low_prop_mask, 'Facies'] = 4
                mid_low_prop_count = mid_low_prop_mask.sum()
                if mid_low_prop_count > 0:
                    print(f"  {mid_low_prop_count} medium-low property samples classified as {facies_names[4]} (4)")

                # 区域5（极低物性区域）- 对应Facies 5
                low_prop_mask = (
                    (df['Facies'].isna()) &
                    (
                        (df['Porosity_num'] < 10) |
                        (df['Permeability_num'] < 0.1)
                    )
                )
                df.loc[low_prop_mask, 'Facies'] = 5
                low_prop_count = low_prop_mask.sum()
                if low_prop_count > 0:
                    print(f"  {low_prop_count} low-property samples classified as {facies_names[5]} (5)")

    # 处理未分类样本
    unclassified_mask = df['Facies'].isna()
    if unclassified_mask.any():
        print(f"Warning: {unclassified_mask.sum()} samples could not be classified based on thin section analysis or properties.")

        # 分析未分类样本
        unclassified_df = df[unclassified_mask]
        if len(unclassified_df) > 0:
            # 统计未分类样本的岩性分布
            litho_counts = unclassified_df['Lithofacies'].value_counts()
            if len(litho_counts) > 0:
                print("Lithofacies distribution of remaining unclassified samples:")
                for litho, count in litho_counts.items():
                    print(f"  {litho}: {count}")

            # 检查未分类样本的环境描述
            env_info = []
            for idx, row in unclassified_df.iterrows():
                if isinstance(row['Lithofacies Association'], str) and row['Lithofacies Association'].strip():
                    env_info.append(f"  Sample at depth {row['Depth']}: {row['Lithofacies']} - '{row['Lithofacies Association']}'")

            if env_info:
                print("Environment descriptions of unclassified samples:")
                for info in env_info[:5]:  # 只显示前5个，避免信息过多
                    print(info)
                if len(env_info) > 5:
                    print(f"  ... and {len(env_info) - 5} more")

            # 定义沉积相名称
            facies_names = {
                1: "Algal-Oncoidal Intertidal Facies",     # 藻类-鲕粒潮间带相（物性最好）
                2: "Inner Ramp Shoal Facies",              # 内坡滩相（物性次好）
                3: "Inner Ramp Backshoal Facies",          # 内坡后滩相（中等物性）
                4: "Inner Ramp Protected Lagoon Facies",   # 内坡受保护潟湖相（较低物性）
                5: "Mid-Ramp Facies"                       # 中坡相（最低物性）
            }

            # 基于岩性进行默认分类
            # 1. 颗粒岩类型默认归为内坡滩相（区域2，Facies 2）
            grain_mask = unclassified_mask & df['Lithofacies'].isin(['G', 'PG', 'R'])
            df.loc[grain_mask, 'Facies'] = 2
            if grain_mask.sum() > 0:
                print(f"  {grain_mask.sum()} grain-supported samples classified as {facies_names[2]} (2)")

            # 2. 泥质颗粒岩默认归为内坡后滩-潟湖相（区域3，Facies 3）
            muddy_grain_mask = unclassified_mask & df['Lithofacies'].isin(['WP', 'P'])
            df.loc[muddy_grain_mask, 'Facies'] = 3
            if muddy_grain_mask.sum() > 0:
                print(f"  {muddy_grain_mask.sum()} muddy grain-supported samples classified as {facies_names[3]} (3)")

            # 3. 颗粒泥岩默认归为内坡受保护潟湖相（区域4，Facies 4）
            grainy_mud_mask = unclassified_mask & df['Lithofacies'].isin(['PM', 'WM'])
            df.loc[grainy_mud_mask, 'Facies'] = 4
            if grainy_mud_mask.sum() > 0:
                print(f"  {grainy_mud_mask.sum()} grainy mud-supported samples classified as {facies_names[4]} (4)")

            # 4. 纯泥岩默认归为中坡相（区域5，Facies 5）
            mud_mask = unclassified_mask & df['Lithofacies'].isin(['M'])
            df.loc[mud_mask, 'Facies'] = 5
            if mud_mask.sum() > 0:
                print(f"  {mud_mask.sum()} pure mud samples classified as {facies_names[5]} (5)")

            # 5. 其他未分类样本默认归为内坡后滩-潟湖相（区域3，Facies 3）
            still_unclassified = df['Facies'].isna()
            if still_unclassified.sum() > 0:
                df.loc[still_unclassified, 'Facies'] = 3
                print(f"  {still_unclassified.sum()} other samples classified as {facies_names[3]} (3)")

            print("Default classification applied to remaining unclassified samples.")
        else:
            # 如果没有未分类样本的信息，全部默认为内坡后滩-潟湖相（区域3，Facies 3）
            df.loc[unclassified_mask, 'Facies'] = 3
            print(f"All remaining unclassified samples set to {facies_names[3]} (3) by default.")

    # 将Facies列转换为整数类型
    df['Facies'] = df['Facies'].astype('Int64')  # 使用Int64允许存在NaN值

    # 删除临时列
    temp_columns = ['Permeability_num', 'Porosity_num', 'Density_num', 'Multiple_Environments']
    for col in temp_columns:
        if col in df.columns:
            df = df.drop(columns=[col])

    return df


def process_excel_file(input_file, output_file=None, consider_properties=True):
    """
    处理Excel文件，添加沉积微相分类

    参数:
    input_file: 输入Excel文件路径
    output_file: 输出Excel文件路径，如果为None则覆盖输入文件
    consider_properties: 是否考虑物性参数进行分类调整

    返回:
    添加了Facies列的DataFrame
    """
    # 读取Excel文件
    print(f"Reading file: {input_file}")
    df = pd.read_excel(input_file)

    # 清理列名中的空格
    df.columns = df.columns.str.strip()
    print(f"列名清理后: {df.columns.tolist()}")

    # 检查必要的列是否存在
    required_cols = ['Depth', 'Lithofacies', 'Lithofacies Association']
    missing_cols = [col for col in required_cols if col not in df.columns]
    if missing_cols:
        raise ValueError(f"Input file is missing required columns: {', '.join(missing_cols)}")

    # 应用微相分类
    print("Applying sedimentary facies classification...")
    df = classify_facies(df, consider_properties)

    # 打印分类统计
    facies_counts = df['Facies'].value_counts().sort_index()
    print("\nSedimentary Facies Classification Statistics:")
    facies_names = {
        1: "Algal-Oncoidal Intertidal Facies",     # 藻类-鲕粒潮间带相（物性最好）
        2: "Inner Ramp Shoal Facies",              # 内坡滩相（物性次好）
        3: "Inner Ramp Backshoal Facies",          # 内坡后滩相（中等物性）
        4: "Inner Ramp Protected Lagoon Facies",   # 内坡受保护潟湖相（较低物性）
        5: "Mid-Ramp Facies"                       # 中坡相（最低物性）
    }
    for facies, count in facies_counts.items():
        print(f"Facies {facies} - {facies_names.get(facies, 'Unknown')}: {count} rows ({count / len(df) * 100:.1f}%)")

    # 保存结果
    if output_file is None:
        output_file = input_file

    # 添加沉积相合并列
    facies_merging = {
        1: "极高能浅水相 (Very High Energy Shallow Water Facies)", # 藻类-鲕粒潮间带相：物性最好
        2: "高能浅水相 (High Energy Shallow Water Facies)",        # 内坡滩相：物性次好
        3: "中能潮坪相 (Medium Energy Tidal Flat Facies)",         # 内坡后滩相：中等物性
        4: "低能受限相 (Low Energy Restricted Facies)",            # 内坡受保护潟湖相：较低物性
        5: "低能开阔相 (Low Energy Open Facies)"                   # 中坡相：最低物性
    }
    df['Merged_Facies'] = df['Facies'].apply(lambda x: facies_merging.get(x, np.nan) if pd.notna(x) else np.nan)

    # 保存沉积相合并信息到DataFrame属性
    df.attrs['facies_merging'] = facies_merging
    df.attrs['facies_names'] = {
        1: "Intertidal Facies",                    # 潮间带相（物性最好）
        2: "Inner Ramp Shoal Facies",              # 内坡滩相（物性次好）
        3: "Inner Ramp Backshoal Facies",          # 内坡后滩相（中等物性）
        4: "Inner Ramp Protected Lagoon Facies",   # 内坡受保护潟湖相（较低物性）
        5: "Mid-Ramp Facies"                       # 中坡相（最低物性）
    }

    print(f"\nSaving results to: {output_file}")
    df.to_excel(output_file, index=False)

    return df


def plot_facies_distribution(df, save_path=None):
    """
    绘制沉积微相分布图

    参数:
    df: 包含Depth和Facies列的DataFrame
    save_path: 保存图像的路径，如果为None则不保存
    """
    if 'Depth' not in df.columns or 'Facies' not in df.columns:
        print("Error: Missing Depth or Facies columns in data")
        return

    # 设置图表样式
    plt.figure(figsize=(10, 12))

    # 定义颜色映射 - 按照沉积相类型定义颜色
    # 1: Algal-Oncoidal Intertidal Facies - 橙色
    # 2: Inner Ramp Shoal Facies - 黄色
    # 3: Inner Ramp Backshoal Facies - 浅蓝色
    # 4: Inner Ramp Protected Lagoon Facies - 灰色
    # 5: Mid-Ramp Facies - 棕色
    colors = ['#ff7f0e', '#F3E80D', '#808080', '#9E9E9E', '#8B4513']
    cmap = ListedColormap(colors)

    # 绘制沉积微相分布
    plt.scatter(df['Facies'], df['Depth'], c=df['Facies'], cmap=cmap,
                s=100, edgecolor='black', alpha=0.8)

    # 反转Y轴，使深度从上到下增加
    plt.gca().invert_yaxis()

    # 设置X轴刻度和标签
    plt.xticks([1, 2, 3, 4, 5])
    plt.xlabel('Facies Type', fontsize=12)
    plt.ylabel('Depth (m)', fontsize=12)
    plt.title('Sedimentary Facies Distribution', fontsize=14)

    # 添加图例
    facies_names = {
        1: "Intertidal Facies",                    # 潮间带相（物性最好）
        2: "Inner Ramp Shoal Facies",              # 内坡滩相（物性次好）
        3: "Inner Ramp Backshoal Facies",          # 内坡后滩相（中等物性）
        4: "Inner Ramp Protected Lagoon Facies",   # 内坡受保护潟湖相（较低物性）
        5: "Mid-Ramp Facies"                       # 中坡相（最低物性）
    }

    # 创建自定义图例
    legend_elements = [Line2D([0], [0], marker='o', color='w', markerfacecolor=colors[i - 1],
                              markersize=10, label=facies_names[i])
                       for i in range(1, 6) if i in df['Facies'].unique()]

    plt.legend(handles=legend_elements, loc='best', fontsize=10)

    # 添加网格线
    plt.grid(True, linestyle='--', alpha=0.7)

    plt.tight_layout()

    # 保存图像
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Image saved to: {save_path}")

    # 显示图像
    plt.show()


def plot_facies_properties(df, save_path=None):
    """
    绘制不同沉积微相的物性分布图

    参数:
    df: 包含Facies列和可选的Porosity和Permeability列的DataFrame
    save_path: 保存图像的路径，如果为None则不保存
    """
    if 'Facies' not in df.columns:
        print("Error: Missing Facies column in data")
        return

    # 检查是否有物性数据可用
    has_porosity = 'Porosity' in df.columns
    has_permeability = 'Permeability' in df.columns

    if not has_porosity and not has_permeability:
        print("Warning: No property data (Porosity or Permeability) available for plotting")
        return

    # 处理渗透率列中的非数值数据
    if has_permeability:
        df['Permeability_num'] = pd.to_numeric(df['Permeability'].replace('NMP', np.nan), errors='coerce')
        has_permeability = df['Permeability_num'].notna().any()  # 确认有有效数据

    # 确认孔隙度数据有效
    if has_porosity:
        df['Porosity_num'] = pd.to_numeric(df['Porosity'], errors='coerce')
        has_porosity = df['Porosity_num'].notna().any()  # 确认有有效数据

    # 定义颜色映射
    colors = ['#ff7f0e', '#F3E80D', '#03F1FD', '#9E9E9E', '#8B4513']

    # 确定需要创建的子图数量
    n_plots = sum([has_porosity, has_permeability])
    if n_plots == 0:
        print("Error: No valid property data available for plotting")
        return

    # 创建子图
    _, axes = plt.subplots(1, n_plots, figsize=(7 * n_plots, 6))

    # 如果只有一个子图，确保axes是一个数组
    if n_plots == 1:
        axes = [axes]

    plot_idx = 0

    # 绘制孔隙度箱线图
    if has_porosity:
        # 创建一个颜色映射字典，将每个Facies值映射到对应的颜色
        facies_color_dict = {i+1: colors[i] for i in range(len(colors))}

        # 使用自定义颜色绘制箱线图
        sns.boxplot(x='Facies', y='Porosity_num', data=df, ax=axes[plot_idx],
                   palette=facies_color_dict, hue='Facies', legend=False)
        axes[plot_idx].set_title('Porosity Distribution by Facies Type', fontsize=14)
        axes[plot_idx].set_xlabel('Facies Type', fontsize=12)
        axes[plot_idx].set_ylabel('Porosity (%)', fontsize=12)

        # 获取实际存在的Facies值并设置刻度
        facies_values = sorted(df['Facies'].dropna().unique())
        axes[plot_idx].set_xticks(range(len(facies_values)))
        axes[plot_idx].set_xticklabels([str(int(i)) for i in facies_values])

        # 添加参考线标明常见的孔隙度阈值
        porosity_thresholds = [5, 10, 15, 20, 25, 30]
        for threshold in porosity_thresholds:
            axes[plot_idx].axhline(y=threshold, color='gray', linestyle='--', alpha=0.5)
            axes[plot_idx].text(-0.5, threshold, f"{threshold}%", fontsize=8,
                               verticalalignment='center', horizontalalignment='right')

        # 添加孔隙度质量区间说明
        quality_text = "Porosity Quality:\n<5%: Poor\n5-10%: Fair\n10-15%: Moderate\n15-20%: Good\n>20%: Excellent"
        axes[plot_idx].text(0.02, 0.98, quality_text,
                 transform=axes[plot_idx].transAxes, fontsize=9,
                 verticalalignment='top', horizontalalignment='left',
                 bbox=dict(boxstyle='round', facecolor='white', alpha=0.7))

        # 添加网格线作为参考
        axes[plot_idx].grid(True, which='major', linestyle='--', alpha=0.7)

        plot_idx += 1

    # 绘制渗透率箱线图（对数尺度但显示实际值）
    if has_permeability:
        # 添加一个小值以避免对数为0
        df['Permeability_log'] = np.log10(df['Permeability_num'].replace(0, 0.01))

        # 创建一个颜色映射字典，将每个Facies值映射到对应的颜色
        facies_color_dict = {i+1: colors[i] for i in range(len(colors))}

        # 使用自定义颜色绘制箱线图
        sns.boxplot(x='Facies', y='Permeability_log', data=df, ax=axes[plot_idx],
                   palette=facies_color_dict, hue='Facies', legend=False)
        axes[plot_idx].set_title('Permeability Distribution by Facies Type', fontsize=14)
        axes[plot_idx].set_xlabel('Facies Type', fontsize=12)
        axes[plot_idx].set_ylabel('Permeability (mD)', fontsize=12)

        # 获取实际存在的Facies值并设置刻度
        facies_values = sorted(df['Facies'].dropna().unique())
        axes[plot_idx].set_xticks(range(len(facies_values)))
        axes[plot_idx].set_xticklabels([str(int(i)) for i in facies_values])

        # 设置Y轴为对数刻度但显示实际值
        # 定义主要刻度位置（对数值）
        major_ticks = [-3, -2, -1, 0, 1, 2, 3, 4]
        # 对应的实际渗透率值
        major_tick_labels = ['0.001', '0.01', '0.1', '1', '10', '100', '1000', '10000']

        # 设置Y轴刻度和标签
        axes[plot_idx].set_yticks(major_ticks)
        axes[plot_idx].set_yticklabels(major_tick_labels)

        # 添加网格线作为参考
        axes[plot_idx].grid(True, which='major', linestyle='--', alpha=0.7)

        # 添加说明文本
        axes[plot_idx].text(0.02, 0.02, 'Note: Y-axis uses logarithmic scale',
                 transform=axes[plot_idx].transAxes, fontsize=9,
                 verticalalignment='bottom', bbox=dict(boxstyle='round', facecolor='white', alpha=0.7))

    # 添加图例
    facies_names = {
        1: "Intertidal Facies",     # 藻类-鲕粒潮间带相（物性最好）
        2: "Inner Ramp Shoal Facies",              # 内坡滩相（物性次好）
        3: "Inner Ramp Backshoal Facies",          # 内坡后滩相（中等物性）
        4: "Inner Ramp Protected Lagoon Facies",   # 内坡受保护潟湖相（较低物性）
        5: "Mid-Ramp Facies"                       # 中坡相（最低物性）
    }

    # 在每个子图上添加注释
    for ax in axes:
        facies_values = sorted(df['Facies'].dropna().unique())

        # 创建一个图例说明
        legend_text = "Facies Types:\n"
        for facies_int in sorted([int(f) for f in facies_values]):
            if facies_int in facies_names:
                legend_text += f"{facies_int}: {facies_names[facies_int]}\n"

        # 添加图例说明到图表右上角
        ax.text(0.98, 0.98, legend_text,
                transform=ax.transAxes, fontsize=9,
                verticalalignment='top', horizontalalignment='right',
                bbox=dict(boxstyle='round', facecolor='white', alpha=0.9))

        # 在X轴下方添加简短标签
        for idx, facies in enumerate(facies_values):
            facies_int = int(facies)
            if facies_int in facies_names:
                # 提取环境名称的主要部分
                short_name = ""
                if "Shoal" in facies_names[facies_int]:
                    short_name = "Shoal"
                elif "Backshoal" in facies_names[facies_int]:
                    short_name = "Backshoal"
                elif "Lagoon" in facies_names[facies_int]:
                    short_name = "Lagoon"
                elif "Mid-Ramp" in facies_names[facies_int]:
                    short_name = "Mid-Ramp"
                elif "Intertidal" in facies_names[facies_int]:
                    short_name = "Intertidal"

                # 使用索引位置而不是facies值作为x坐标
                ax.annotate(short_name,
                            xy=(idx, ax.get_ylim()[0]),
                            xytext=(idx, ax.get_ylim()[0] - 0.05 * (ax.get_ylim()[1] - ax.get_ylim()[0])),
                            ha='center', fontsize=9, fontweight='bold',
                            color=colors[min(facies_int-1, len(colors)-1)])

    # 调整布局
    plt.tight_layout()

    # 保存图像
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Image saved to: {save_path}")

    # 显示图像
    plt.show()

    # 删除临时列
    temp_columns = ['Permeability_log']
    for col in temp_columns:
        if col in df.columns:
            df = df.drop(columns=[col])

    # 保留物性数值列和匹配度列，以便后续分析
    # 如果需要删除这些列，可以取消注释以下代码
    # property_columns = ['Permeability_num', 'Porosity_num', 'Density_num']
    # for col in property_columns:
    #     if col in df.columns:
    #         df = df.drop(columns=[col])
    #
    # match_columns = [col for col in df.columns if col.startswith('Property_Match_')]
    # df = df.drop(columns=match_columns, errors='ignore')

    return df

def generate_property_report(df):
    """生成物性分析报告"""
    print("\n===== Property Analysis by Facies Type =====")

    # 获取沉积相名称
    facies_names = df.attrs.get('facies_names', {
        1: "Intertidal Facies",                    # 潮间带相（物性最好）
        2: "Inner Ramp Shoal Facies",              # 内坡滩相（物性次好）
        3: "Inner Ramp Backshoal Facies",          # 内坡后滩相（中等物性）
        4: "Inner Ramp Protected Lagoon Facies",   # 内坡受保护潟湖相（较低物性）
        5: "Mid-Ramp Facies"                       # 中坡相（最低物性）
    })

    # 分析每种沉积相类型的物性特征
    for facies in sorted(df['Facies'].dropna().unique()):
        facies_int = int(facies)
        facies_df = df[df['Facies'] == facies]
        print(f"\nFacies {facies_int} - {facies_names.get(facies_int, 'Unknown')}:")
        print(f"  Sample count: {len(facies_df)} ({len(facies_df) / len(df) * 100:.1f}%)")

        # 分析孔隙度
        if 'Porosity_num' in df.columns:
            porosity_data = facies_df['Porosity_num'].dropna()
            if len(porosity_data) > 0:
                print(f"  Porosity: {porosity_data.mean():.2f}% (range: {porosity_data.min():.2f}-{porosity_data.max():.2f}%)")
                print(f"    - Low (<8%): {(porosity_data < 8).sum()} samples ({(porosity_data < 8).sum() / len(porosity_data) * 100:.1f}%)")
                print(f"    - Medium (8-15%): {((porosity_data >= 8) & (porosity_data <= 15)).sum()} samples ({((porosity_data >= 8) & (porosity_data <= 15)).sum() / len(porosity_data) * 100:.1f}%)")
                print(f"    - High (>15%): {(porosity_data > 15).sum()} samples ({(porosity_data > 15).sum() / len(porosity_data) * 100:.1f}%)")

        # 分析渗透率
        if 'Permeability_num' in df.columns:
            perm_data = facies_df['Permeability_num'].dropna()
            if len(perm_data) > 0:
                print(f"  Permeability: {perm_data.median():.2f} mD (range: {perm_data.min():.2f}-{perm_data.max():.2f} mD)")
                print(f"    - Low (<1 mD): {(perm_data < 1).sum()} samples ({(perm_data < 1).sum() / len(perm_data) * 100:.1f}%)")
                print(f"    - Medium (1-10 mD): {((perm_data >= 1) & (perm_data <= 10)).sum()} samples ({((perm_data >= 1) & (perm_data <= 10)).sum() / len(perm_data) * 100:.1f}%)")
                print(f"    - High (>10 mD): {(perm_data > 10).sum()} samples ({(perm_data > 10).sum() / len(perm_data) * 100:.1f}%)")

        # 分析密度
        if 'Density_num' in df.columns:
            density_data = facies_df['Density_num'].dropna()
            if len(density_data) > 0:
                print(f"  Density: {density_data.mean():.2f} g/cm³ (range: {density_data.min():.2f}-{density_data.max():.2f} g/cm³)")

    print("\n===== End of Property Analysis =====")

def plot_porosity_distribution(df, save_path=None):
    """
    绘制孔隙度分布箱线图

    参数:
    df: 包含Facies列和Porosity列的DataFrame
    save_path: 保存图像的路径，如果为None则不保存
    """
    print("开始执行孔隙度分布图函数...")

    # 检查必要的列是否存在
    if 'Facies' not in df.columns:
        print("Error: Missing Facies column in data")
        return

    # 检查Porosity列，考虑可能的变体
    porosity_col = None
    for col in df.columns:
        if col.lower().startswith('porosity'):
            porosity_col = col
            print(f"找到孔隙度列: {porosity_col}")
            break

    if porosity_col is None:
        print("Error: Missing Porosity column in data")
        print(f"可用的列: {df.columns.tolist()}")
        return

    # 确认孔隙度数据有效
    print(f"转换孔隙度数据为数值类型，原始数据类型: {df[porosity_col].dtype}")
    df_copy = df.copy()  # 创建数据副本，避免修改原始数据
    df_copy['Porosity_num'] = pd.to_numeric(df_copy[porosity_col], errors='coerce')

    valid_count = df_copy['Porosity_num'].notna().sum()
    print(f"有效的孔隙度数值: {valid_count}")

    if valid_count == 0:
        print("Error: No valid Porosity data available for plotting")
        return

    # 定义颜色映射 - 按照沉积相类型定义颜色
    # 1: Intertidal Facies - 橙色
    # 2: Inner Ramp Shoal Facies - 黄色
    # 3: Inner Ramp Backshoal Facies - 浅蓝色
    # 4: Inner Ramp Protected Lagoon Facies - 灰色
    # 5: Mid-Ramp Facies - 棕色
    colors = ['#FF9800', '#FFEB3B', '#03A9F4', '#9E9E9E', '#795548']
    print("定义颜色映射完成")

    # 创建一个颜色映射字典，将每个Facies值映射到对应的颜色
    facies_color_dict = {i+1: colors[i] for i in range(len(colors))}

    # 创建图表
    print("开始创建图表...")
    plt.figure(figsize=(10, 6))

    # 使用自定义颜色绘制箱线图
    print("开始绘制箱线图...")
    try:
        ax = sns.boxplot(x='Facies', y='Porosity_num', data=df_copy,
                        palette=facies_color_dict, hue='Facies', legend=False)
        print("箱线图绘制成功")
    except Exception as e:
        print(f"绘制箱线图时出错: {e}")
        import traceback
        print(traceback.format_exc())
        return

    # 设置标题和标签
    plt.title('Porosity Distribution by Facies Type', fontsize=14)
    plt.xlabel('Facies Type', fontsize=12)
    plt.ylabel('Porosity (%)', fontsize=12)

    # 获取实际存在的Facies值并设置刻度
    print("获取Facies值...")
    facies_values = sorted(df_copy['Facies'].dropna().unique())
    print(f"找到的Facies值: {facies_values}")
    plt.xticks(range(len(facies_values)), [str(int(i)) for i in facies_values])

    # 设置明确的Y轴刻度
    porosity_thresholds = [0, 5, 10, 15, 20, 25, 30, 35]
    plt.yticks(porosity_thresholds)

    # 添加参考线标明常见的孔隙度阈值
    for threshold in [5, 10, 15, 20, 25, 30]:
        plt.axhline(y=threshold, color='gray', linestyle='--', alpha=0.5)

    # 添加孔隙度质量区间说明
    quality_text = "Porosity Quality:\n<5%: Poor\n5-10%: Fair\n10-15%: Moderate\n15-20%: Good\n>20%: Excellent"
    plt.text(0.02, 0.98, quality_text,
             transform=ax.transAxes, fontsize=9,
             verticalalignment='top', horizontalalignment='left',
             bbox=dict(boxstyle='round', facecolor='white', alpha=0.7))

    # 添加图例
    facies_names = {
        1: "Intertidal Facies",                    # 潮间带相（物性最好）
        2: "Inner Ramp Shoal Facies",              # 内坡滩相（物性次好）
        3: "Inner Ramp Backshoal Facies",          # 内坡后滩相（中等物性）
        4: "Inner Ramp Protected Lagoon Facies",   # 内坡受保护潟湖相（较低物性）
        5: "Mid-Ramp Facies"                       # 中坡相（最低物性）
    }

    # 创建一个图例说明
    legend_text = "Facies Types:\n"
    for facies_int in sorted([int(f) for f in facies_values]):
        if facies_int in facies_names:
            legend_text += f"{facies_int}: {facies_names[facies_int]}\n"

    # 添加图例说明到图表右上角
    plt.text(0.98, 0.98, legend_text,
            transform=ax.transAxes, fontsize=9,
            verticalalignment='top', horizontalalignment='right',
            bbox=dict(boxstyle='round', facecolor='white', alpha=0.9))

    # 在X轴下方添加简短标签
    for idx, facies in enumerate(facies_values):
        facies_int = int(facies)
        if facies_int in facies_names:
            # 提取环境名称的主要部分
            short_name = ""
            if "Shoal" in facies_names[facies_int]:
                short_name = "Shoal"
            elif "Backshoal" in facies_names[facies_int]:
                short_name = "Backshoal"
            elif "Lagoon" in facies_names[facies_int]:
                short_name = "Lagoon"
            elif "Mid-Ramp" in facies_names[facies_int]:
                short_name = "Mid-Ramp"
            elif "Intertidal" in facies_names[facies_int]:
                short_name = "Intertidal"

            # 使用索引位置而不是facies值作为x坐标
            plt.annotate(short_name,
                        xy=(idx, plt.ylim()[0]),
                        xytext=(idx, plt.ylim()[0] - 0.05 * (plt.ylim()[1] - plt.ylim()[0])),
                        ha='center', fontsize=9, fontweight='bold',
                        color=colors[min(facies_int-1, len(colors)-1)])

    # 添加网格线作为参考
    plt.grid(True, which='major', linestyle='--', alpha=0.7)

    # 调整布局
    plt.tight_layout()

    # 保存图像
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Image saved to: {save_path}")

    # 显示图像
    plt.show()
    print("孔隙度分布图显示完成")

    return df_copy


def plot_permeability_distribution(df, save_path=None):
    """
    绘制渗透率分布箱线图

    参数:
    df: 包含Facies列和Permeability列的DataFrame
    save_path: 保存图像的路径，如果为None则不保存
    """
    print("开始执行渗透率分布图函数...")

    # 检查必要的列是否存在
    if 'Facies' not in df.columns:
        print("Error: Missing Facies column in data")
        return

    # 检查Permeability列，考虑可能的变体
    permeability_col = None
    for col in df.columns:
        if col.lower().startswith('permeability'):
            permeability_col = col
            print(f"找到渗透率列: {permeability_col}")
            break

    if permeability_col is None:
        print("Error: Missing Permeability column in data")
        print(f"可用的列: {df.columns.tolist()}")
        return

    # 处理渗透率列中的非数值数据
    print(f"转换渗透率数据为数值类型，原始数据类型: {df[permeability_col].dtype}")
    df_copy = df.copy()  # 创建数据副本，避免修改原始数据
    df_copy['Permeability_num'] = pd.to_numeric(df_copy[permeability_col].replace('NMP', np.nan), errors='coerce')

    valid_count = df_copy['Permeability_num'].notna().sum()
    print(f"有效的渗透率数值: {valid_count}")

    if valid_count == 0:
        print("Error: No valid Permeability data available for plotting")
        return

    # 添加一个小值以避免对数为0
    df_copy['Permeability_log'] = np.log10(df_copy['Permeability_num'].replace(0, 0.01))

    # 定义颜色映射 - 按照沉积相类型定义颜色
    # 1: Algal-Oncoidal Intertidal Facies - 橙色
    # 2: Inner Ramp Shoal Facies - 黄色
    # 3: Inner Ramp Backshoal Facies - 浅蓝色
    # 4: Inner Ramp Protected Lagoon Facies - 灰色
    # 5: Mid-Ramp Facies - 棕色
    colors = ['#FF9800', '#FFEB3B', '#03A9F4', '#9E9E9E', '#795548']

    # 创建一个颜色映射字典，将每个Facies值映射到对应的颜色
    facies_color_dict = {i+1: colors[i] for i in range(len(colors))}
    print("定义颜色映射完成")

    # 创建图表
    print("开始创建图表...")
    plt.figure(figsize=(10, 6))

    # 使用自定义颜色绘制箱线图
    print("开始绘制箱线图...")
    try:
        ax = sns.boxplot(x='Facies', y='Permeability_log', data=df_copy,
                        palette=facies_color_dict, hue='Facies', legend=False)
        print("箱线图绘制成功")
    except Exception as e:
        print(f"绘制箱线图时出错: {e}")
        import traceback
        print(traceback.format_exc())
        return

    # 设置标题和标签
    plt.title('Permeability Distribution by Facies Type', fontsize=14)
    plt.xlabel('Facies Type', fontsize=12)
    plt.ylabel('Permeability (mD)', fontsize=12)

    # 获取实际存在的Facies值并设置刻度
    print("获取Facies值...")
    facies_values = sorted(df_copy['Facies'].dropna().unique())
    print(f"找到的Facies值: {facies_values}")
    plt.xticks(range(len(facies_values)), [str(int(i)) for i in facies_values])

    # 设置Y轴为对数刻度但显示实际值
    # 定义主要刻度位置（对数值）
    major_ticks = [-3, -2, -1, 0, 1, 2, 3, 4]
    # 对应的实际渗透率值
    major_tick_labels = ['0.001', '0.01', '0.1', '1', '10', '100', '1000', '10000']

    # 设置Y轴刻度和标签
    plt.yticks(major_ticks, major_tick_labels)

    # 添加图例
    facies_names = {
        1: "Intertidal Facies",                    # 潮间带相（物性最好）
        2: "Inner Ramp Shoal Facies",              # 内坡滩相（物性次好）
        3: "Inner Ramp Backshoal Facies",          # 内坡后滩相（中等物性）
        4: "Inner Ramp Protected Lagoon Facies",   # 内坡受保护潟湖相（较低物性）
        5: "Mid-Ramp Facies"                       # 中坡相（最低物性）
    }

    # 创建一个图例说明
    legend_text = "Facies Types:\n"
    for facies_int in sorted([int(f) for f in facies_values]):
        if facies_int in facies_names:
            legend_text += f"{facies_int}: {facies_names[facies_int]}\n"

    # 添加图例说明到图表右上角
    plt.text(0.98, 0.98, legend_text,
            transform=ax.transAxes, fontsize=9,
            verticalalignment='top', horizontalalignment='right',
            bbox=dict(boxstyle='round', facecolor='white', alpha=0.9))

    # 在X轴下方添加简短标签
    for idx, facies in enumerate(facies_values):
        facies_int = int(facies)
        if facies_int in facies_names:
            # 提取环境名称的主要部分
            short_name = ""
            if "Shoal" in facies_names[facies_int]:
                short_name = "Shoal"
            elif "Backshoal" in facies_names[facies_int]:
                short_name = "Backshoal"
            elif "Lagoon" in facies_names[facies_int]:
                short_name = "Lagoon"
            elif "Mid-Ramp" in facies_names[facies_int]:
                short_name = "Mid-Ramp"
            elif "Intertidal" in facies_names[facies_int]:
                short_name = "Intertidal"

            # 使用索引位置而不是facies值作为x坐标
            plt.annotate(short_name,
                        xy=(idx, plt.ylim()[0]),
                        xytext=(idx, plt.ylim()[0] - 0.05 * (plt.ylim()[1] - plt.ylim()[0])),
                        ha='center', fontsize=9, fontweight='bold',
                        color=colors[min(facies_int-1, len(colors)-1)])

    # 添加网格线作为参考
    plt.grid(True, which='major', linestyle='--', alpha=0.7)

    # 添加说明文本
    plt.text(0.02, 0.02, 'Note: Y-axis uses logarithmic scale',
             transform=ax.transAxes, fontsize=9,
             verticalalignment='bottom', bbox=dict(boxstyle='round', facecolor='white', alpha=0.7))

    # 调整布局
    plt.tight_layout()

    # 保存图像
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Image saved to: {save_path}")

    # 显示图像
    plt.show()

    # 删除临时列
    if 'Permeability_log' in df_copy.columns:
        df_copy = df_copy.drop(columns=['Permeability_log'])

    print("渗透率分布图显示完成")
    return df_copy


def generate_facies_merging_report(df):
    """生成沉积相合并报告"""
    print("\n===== Sedimentary Facies Merging Report =====")

    # 获取沉积相名称和合并信息
    facies_names = df.attrs.get('facies_names', {
        1: "Intertidal Facies",                    # 潮间带相（物性最好）
        2: "Inner Ramp Shoal Facies",              # 内坡滩相（物性次好）
        3: "Inner Ramp Backshoal Facies",          # 内坡后滩相（中等物性）
        4: "Inner Ramp Protected Lagoon Facies",   # 内坡受保护潟湖相（较低物性）
        5: "Mid-Ramp Facies"                       # 中坡相（最低物性）
    })

    facies_merging = df.attrs.get('facies_merging', {
        1: "极高能浅水相 (Very High Energy Shallow Water Facies)", # Facies 1: 藻类-鲕粒潮间带相（物性最好）
        2: "高能浅水相 (High Energy Shallow Water Facies)",        # Facies 2: 内坡滩相（物性次好）
        3: "中能潮坪相 (Medium Energy Tidal Flat Facies)",         # Facies 3: 内坡后滩相（中等物性）
        4: "低能受限相 (Low Energy Restricted Facies)",            # Facies 4: 内坡受保护潟湖相（较低物性）
        5: "低能开阔相 (Low Energy Open Facies)"                   # Facies 5: 中坡相（最低物性）
    })

    # 创建合并后的沉积相列
    df['Merged_Facies'] = df['Facies'].apply(lambda x: facies_merging.get(x, np.nan) if pd.notna(x) else np.nan)

    # 统计合并前后的沉积相分布
    print("Original Facies Distribution:")
    for facies in sorted(df['Facies'].dropna().unique()):
        facies_int = int(facies)
        count = (df['Facies'] == facies).sum()
        print(f"  Facies {facies_int} - {facies_names.get(facies_int, 'Unknown')}: {count} samples ({count / len(df) * 100:.1f}%)")

    print("\nMerged Facies Distribution:")
    merged_counts = df['Merged_Facies'].value_counts()
    for merged_facies, count in merged_counts.items():
        print(f"  {merged_facies}: {count} samples ({count / len(df) * 100:.1f}%)")

    print("\nFacies Merging Details:")
    for facies_int, merged_name in facies_merging.items():
        if facies_int in facies_names:
            print(f"  Facies {facies_int} ({facies_names[facies_int]}) -> {merged_name}")

    print("\n===== End of Facies Merging Report =====")

    return


if __name__ == "__main__":
    # 设置输入和输出文件路径
    input_file = r"D:\LQ_2024\MJN_2024\沉积相\Hartha沉积相\沉积相转换-250509.xlsx"  # 请修改为您的文件路径
    output_file = r"D:\LQ_2024\MJN_2024\沉积相\Hartha沉积相\岩性-沉积相转换数据_带微相分类.xlsx"  # 输出文件路径

    # 处理文件
    try:
        # 分类数据
        df = process_excel_file(input_file, output_file, consider_properties=True)

        # 绘制沉积微相分布图
        plot_facies_distribution(df, save_path="Facies_Distribution.png")

        # 先检查数据中是否有孔隙度和渗透率列
        print("\n检查数据列...")
        has_porosity = 'Porosity' in df.columns
        has_permeability = 'Permeability' in df.columns

        if has_porosity:
            print(f"孔隙度列存在，包含 {df['Porosity'].notna().sum()} 个有效值")
        else:
            print("警告：数据中缺少Porosity列")

        if has_permeability:
            print(f"渗透率列存在，包含 {df['Permeability'].notna().sum()} 个有效值")
        else:
            print("警告：数据中缺少Permeability列")

        # 绘制孔隙度分布图
        if has_porosity:
            try:
                print("\n开始绘制孔隙度分布图...")
                plot_porosity_distribution(df, save_path="Porosity_Distribution.png")
                print("孔隙度分布图绘制完成")
            except Exception as e:
                print(f"绘制孔隙度分布图时出错: {e}")
                import traceback
                print(traceback.format_exc())

        # 绘制渗透率分布图
        if has_permeability:
            try:
                print("\n开始绘制渗透率分布图...")
                plot_permeability_distribution(df, save_path="Permeability_Distribution.png")
                print("渗透率分布图绘制完成")
            except Exception as e:
                print(f"绘制渗透率分布图时出错: {e}")
                import traceback
                print(traceback.format_exc())

        # 生成物性分析报告
        generate_property_report(df)

        # 生成沉积相合并报告
        generate_facies_merging_report(df)

        print("\nProcessing completed!")
        print(f"Processed {len(df)} rows of data")
        print(f"Results saved to: {output_file}")
        print("Images saved to current directory")
    except Exception as e:
        print(f"Error during processing: {e}")
        import traceback

        print(traceback.format_exc())
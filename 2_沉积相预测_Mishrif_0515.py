import os
import pandas as pd
import numpy as np
import joblib
import logging
from datetime import datetime

# 输入输出路径设置
MODEL_PATH = r'D:\LQ_2024\MJN_2024\沉积相\Mishrif沉积相\20250525\best_facies_model.pkl'
INPUT_PATH = r'D:\LQ_2024\MJN_2024\沉积相\Mishrif沉积相\new_test_20250520\4curves_for_train'
OUTPUT_DIR = os.path.join(INPUT_PATH, 'Mishrif_预测结果_250525')

# 日志设置
LOG_FILE = f"facies_prediction_batch_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(LOG_FILE, encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("批量岩相预测")

# 特征工程函数（含强制生成关键特征）
def feature_engineering(df, feature_cols):
    extended_df = df.copy()

    # 增强GR特征的权重 - 创建GR的额外特征
    if 'GR' in df.columns:
        # 创建GR的平方和平方根特征，增强非线性关系
        extended_df['GR_SQUARED'] = df['GR'] ** 2
        extended_df['GR_SQRT'] = np.sqrt(df['GR'].clip(lower=0.001))  # 避免负值

        # 创建GR与其他特征的交互项
        for feat in feature_cols:
            if feat != 'GR':
                extended_df[f'GR_TIMES_{feat}'] = df['GR'] * df[feat]

    # 强制生成关键特征
    if 'DT' in df.columns and 'GR' in df.columns:
        extended_df['DT_DIV_GR'] = df['DT'] / df['GR'].replace(0, 1e-10)
        # 添加反向比值
        extended_df['GR_DIV_DT'] = df['GR'] / df['DT'].replace(0, 1e-10)

    if 'GR' in df.columns and 'NEU' in df.columns:
        extended_df['GR_DIV_NEU'] = df['GR'] / df['NEU'].replace(0, 1e-10)
        # 添加反向比值
        extended_df['NEU_DIV_GR'] = df['NEU'] / df['GR'].replace(0, 1e-10)

    if 'DEN' in df.columns and 'NEU' in df.columns:
        extended_df['DEN_DIV_NEU'] = df['DEN'] / df['NEU'].replace(0, 1e-10)
        # 添加反向比值
        extended_df['NEU_DIV_DEN'] = df['NEU'] / df['DEN'].replace(0, 1e-10)

    if 'DEN' in df.columns and 'DT' in df.columns:
        extended_df['DEN_DIV_DT'] = df['DEN'] / df['DT'].replace(0, 1e-10)
        # 添加反向比值
        extended_df['DT_DIV_DEN'] = df['DT'] / df['DEN'].replace(0, 1e-10)

    # 移除与深度相关的特征生成
    # 注释掉原来的代码，保留注释以便了解历史
    # if depth_col in df.columns:
    #     for feat in feature_cols:
    #         extended_df[f'{feat}_DEPTH_NORM'] = extended_df[feat] / extended_df[depth_col]

    # 添加标准化特征 - 与训练代码完全一致
    # 为所有特征添加MinMax和Z-Score标准化
    for feat in feature_cols:
        if feat in df.columns:
            # Z-score normalization
            zscore_col = f'{feat}_ZSCORE'
            mean, std = df[feat].mean(), df[feat].std()
            if std > 0:  # 避免除零
                extended_df[zscore_col] = (df[feat] - mean) / std
            else:
                extended_df[zscore_col] = 0

            # Min-Max normalization
            minmax_col = f'{feat}_MINMAX'
            min_val, max_val = df[feat].min(), df[feat].max()
            if max_val > min_val:  # 避免除零
                extended_df[minmax_col] = (df[feat] - min_val) / (max_val - min_val)
            else:
                extended_df[minmax_col] = 0

            # 为GR添加额外的分位数特征
            if feat == 'GR':
                # 分位数特征
                q25 = df[feat].quantile(0.25)
                q50 = df[feat].quantile(0.50)
                q75 = df[feat].quantile(0.75)

                # 相对于分位数的位置
                extended_df['GR_Q25_RATIO'] = (df[feat] - q25) / (q75 - q25 + 1e-10)
                extended_df['GR_Q50_RATIO'] = (df[feat] - q50) / (q75 - q25 + 1e-10)
                extended_df['GR_Q75_RATIO'] = (df[feat] - q75) / (q75 - q25 + 1e-10)
        else:
            # 如果特征不存在，添加默认值
            extended_df[f'{feat}_MINMAX'] = 0
            extended_df[f'{feat}_ZSCORE'] = 0
            logger.warning(f"⚠️ 数据中缺少{feat}列，使用默认值0替代")

    # 自动生成高相关性比值特征
    corr_matrix = df[feature_cols].corr().abs()
    upper_tri = corr_matrix.where(np.triu(np.ones(corr_matrix.shape), k=1).astype(bool))
    high_corr_pairs = [(upper_tri.index[i], upper_tri.columns[j])
                       for i, j in zip(*np.where(upper_tri > 0.3))]  # 降低相关性阈值，增加特征数量

    # 确保GR相关的比值特征被优先创建
    gr_pairs = [(feat1, feat2) for feat1, feat2 in high_corr_pairs if 'GR' in feat1 or 'GR' in feat2]
    other_pairs = [(feat1, feat2) for feat1, feat2 in high_corr_pairs if 'GR' not in feat1 and 'GR' not in feat2]

    # 先处理GR相关的比值，再处理其他比值
    for feat1, feat2 in gr_pairs + other_pairs[:15]:  # 增加比值特征数量
        ratio_name = f'{feat1}_DIV_{feat2}'
        if ratio_name not in extended_df.columns:
            extended_df[ratio_name] = df[feat1] / df[feat2].replace(0, 1e-10)

    # 确保GR在key_features中
    key_features = list(feature_cols)  # 复制列表
    if 'GR' in feature_cols and 'GR' not in key_features[:3]:
        # 确保GR在前3个特征中
        if 'GR' in key_features:
            key_features.remove('GR')
        key_features = ['GR'] + key_features

    key_features = key_features[:min(5, len(key_features))]  # 限制为前5个特征
    window_sizes = [3, 5, 7]  # 增加一个窗口大小

    for feat in key_features:
        for window in window_sizes:
            # Rolling mean
            extended_df[f'{feat}_ROLL_MEAN_{window}'] = extended_df[feat].rolling(window=window, center=True).mean()
            # Rolling standard deviation
            extended_df[f'{feat}_ROLL_STD_{window}'] = extended_df[feat].rolling(window=window, center=True).std()
            # Trend feature
            extended_df[f'{feat}_TREND_{window}'] = extended_df[feat].diff(window).apply(
                lambda x: 1 if x > 0 else (-1 if x < 0 else 0))

            # 为GR添加额外的滚动特征
            if feat == 'GR':
                # 滚动最大值和最小值
                extended_df[f'GR_ROLL_MAX_{window}'] = extended_df[feat].rolling(
                    window=window, center=True).max()
                extended_df[f'GR_ROLL_MIN_{window}'] = extended_df[feat].rolling(
                    window=window, center=True).min()
                # 滚动范围（最大值-最小值）
                extended_df[f'GR_ROLL_RANGE_{window}'] = extended_df[f'GR_ROLL_MAX_{window}'] - extended_df[f'GR_ROLL_MIN_{window}']

    extended_df = extended_df.bfill().ffill()
    new_features = [col for col in extended_df.columns if col not in df.columns and col != 'DEPTH']

    # 记录GR相关特征
    gr_features = [f for f in new_features if 'GR' in f]
    logger.info(f"Added {len(gr_features)} GR-related features to enhance GR importance")

    return extended_df, feature_cols + new_features

# 加载模型函数
def load_model_with_metadata(model_path):
    if not os.path.exists(model_path):
        raise FileNotFoundError(f"模型文件不存在: {model_path}")
    content = joblib.load(model_path)
    if 'model' not in content or 'metadata' not in content:
        raise ValueError("模型文件必须包含 'model' 和 'metadata'")
    return content['model'], content['metadata']

# 主程序
if __name__ == "__main__":
    # 创建输出目录
    os.makedirs(OUTPUT_DIR, exist_ok=True)

    print(f"📦 加载模型: {MODEL_PATH}")
    try:
        model, metadata = load_model_with_metadata(MODEL_PATH)
        original_features = [col.replace(' ', '') for col in metadata['original_features']]
        selected_features = [col.replace(' ', '') for col in metadata['selected_features']]

        csv_files = [f for f in os.listdir(INPUT_PATH) if f.lower().endswith('.csv')]
        logger.info(f"📁 发现 {len(csv_files)} 个CSV文件")

        for filename in csv_files:
            filepath = os.path.join(INPUT_PATH, filename)
            try:
                df = pd.read_csv(filepath)
                df.columns = df.columns.str.replace(' ', '')  # 清理列名空格

                if 'Facies' in df.columns:
                    df = df.drop(columns='Facies')

                if not all(col in df.columns for col in original_features):
                    missing = [col for col in original_features if col not in df.columns]
                    raise ValueError(f"⚠️ 缺失原始特征列: {missing}")

                logger.info(f"开始特征工程处理: {filename}")
                df_ext, generated_features = feature_engineering(df, original_features)
                logger.info(f"特征工程完成，生成了 {len(generated_features)} 个特征")

                # 检查是否生成了所有需要的标准化特征
                missing_norm_features = []
                for feat in original_features:
                    if f'{feat}_MINMAX' not in df_ext.columns:
                        missing_norm_features.append(f'{feat}_MINMAX')
                    if f'{feat}_ZSCORE' not in df_ext.columns:
                        missing_norm_features.append(f'{feat}_ZSCORE')

                if missing_norm_features:
                    logger.warning(f"⚠️ 缺少以下标准化特征: {missing_norm_features}")
                else:
                    logger.info("✅ 成功生成所有标准化特征")

                if not all(col in df_ext.columns for col in selected_features):
                    missing = [col for col in selected_features if col not in df_ext.columns]
                    logger.error(f"⚠️ 缺失扩展特征列: {missing}")
                    raise ValueError(f"⚠️ 缺失扩展特征列: {missing}")

                X_pred = df_ext[selected_features]
                df_ext['Predicted_Facies'] = model.predict(X_pred)

                # 只保留 DEPTH 和预测结果，并格式化
                result_df = df_ext[['DEPTH', 'Predicted_Facies']].copy()
                result_df['DEPTH'] = result_df['DEPTH'].round(2)  # 可调精度
                result_df['Predicted_Facies'] = result_df['Predicted_Facies'].astype(int)

                # 写入 .prn 文件（列宽40，右对齐）
                prn_lines = []
                for _, row in result_df.iterrows():
                    line = f"{str(row['DEPTH']).rjust(40)}{str(row['Predicted_Facies']).rjust(40)}"
                    prn_lines.append(line)

                prn_name = os.path.splitext(filename)[0] + '.prn'
                prn_path = os.path.join(OUTPUT_DIR, prn_name)
                with open(prn_path, 'w', encoding='utf-8') as f:
                    f.write('\n'.join(prn_lines))

                logger.info(f"✅ 处理成功: {filename} -> {prn_name}")
            except Exception as fe:
                logger.error(f"❌ 处理失败: {filename} | 错误: {fe}")

        print(f"🎉 全部处理完成！PRN 文件已保存至: {OUTPUT_DIR}")
    except Exception as e:
        print(f"❌ 总体错误: {str(e)}")

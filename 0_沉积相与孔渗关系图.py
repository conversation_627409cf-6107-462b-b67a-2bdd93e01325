import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from matplotlib.colors import ListedColormap
from matplotlib.lines import Line2D
import tkinter as tk
from tkinter import ttk, colorchooser, filedialog, messagebox
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure
import matplotlib.patches as patches


class InteractiveFaciesVisualizer:
    """
    交互式沉积相可视化工具，支持颜色选择和实时预览
    """

    def __init__(self):
        self.root = tk.Tk()
        self.root.title("沉积相可视化工具 - 交互式颜色选择器")
        self.root.geometry("1200x800")

        # 相类型信息
        self.facies_names = {
            1: "Intertidal Facies (潮间带相)",
            2: "Inner Ramp Subtidal Facies (内坡亚潮带相)",
            3: "Inner Ramp Backshoal Facies (内坡后滩相)",
            4: "Inner Ramp Protected Lagoon Facies (内坡受保护潟湖相)",
            5: "Mid-Ramp Facies (中坡相)"
        }

        # 初始化颜色（随机生成，用户必须自己选择）
        self.colors = {
            1: '#FF0000',  # 红色
            2: '#00FF00',  # 绿色
            3: '#0000FF',  # 蓝色
            4: '#FFFF00',  # 黄色
            5: '#FF00FF'  # 紫色
        }

        self.df = None
        self.setup_ui()

    def setup_ui(self):
        """设置用户界面"""
        # 创建主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 左侧控制面板
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))

        # 文件选择区域
        file_frame = ttk.LabelFrame(control_frame, text="数据文件", padding=10)
        file_frame.pack(fill=tk.X, pady=(0, 10))

        self.file_path_var = tk.StringVar()
        file_entry = ttk.Entry(file_frame, textvariable=self.file_path_var, width=40)
        file_entry.pack(side=tk.TOP, fill=tk.X, pady=(0, 5))

        file_button = ttk.Button(file_frame, text="选择CSV文件", command=self.select_file)
        file_button.pack(side=tk.TOP, fill=tk.X)

        # 颜色选择区域
        color_frame = ttk.LabelFrame(control_frame, text="颜色设置", padding=10)
        color_frame.pack(fill=tk.X, pady=(0, 10))

        # 为每个相类型创建颜色选择按钮
        self.color_buttons = {}
        self.color_labels = {}

        for facies_id in sorted(self.facies_names.keys()):
            facies_frame = ttk.Frame(color_frame)
            facies_frame.pack(fill=tk.X, pady=2)

            # 相类型标签
            label = ttk.Label(facies_frame, text=f"相类型 {facies_id}:", width=12)
            label.pack(side=tk.LEFT)

            # 颜色显示标签
            color_label = tk.Label(facies_frame, width=8, height=1,
                                   bg=self.colors[facies_id], relief=tk.RAISED, borderwidth=2)
            color_label.pack(side=tk.LEFT, padx=(5, 5))
            self.color_labels[facies_id] = color_label

            # 颜色选择按钮
            color_button = ttk.Button(facies_frame, text="选择颜色",
                                      command=lambda fid=facies_id: self.choose_color(fid))
            color_button.pack(side=tk.LEFT, padx=(5, 0))
            self.color_buttons[facies_id] = color_button

        # 相类型名称显示
        names_frame = ttk.LabelFrame(control_frame, text="相类型说明", padding=10)
        names_frame.pack(fill=tk.X, pady=(0, 10))

        for facies_id, name in self.facies_names.items():
            name_label = ttk.Label(names_frame, text=f"{facies_id}: {name}", wraplength=250)
            name_label.pack(anchor=tk.W, pady=1)

        # 操作按钮区域
        button_frame = ttk.Frame(control_frame)
        button_frame.pack(fill=tk.X, pady=(0, 10))

        # 随机颜色按钮
        random_button = ttk.Button(button_frame, text="随机生成颜色", command=self.generate_random_colors)
        random_button.pack(fill=tk.X, pady=(0, 5))

        # 绘图按钮
        plot_porosity_button = ttk.Button(button_frame, text="绘制孔隙度分布图",
                                          command=self.plot_porosity)
        plot_porosity_button.pack(fill=tk.X, pady=(0, 5))

        plot_permeability_button = ttk.Button(button_frame, text="绘制渗透率分布图",
                                              command=self.plot_permeability)
        plot_permeability_button.pack(fill=tk.X, pady=(0, 5))

        # 保存按钮
        save_button = ttk.Button(button_frame, text="保存当前图表", command=self.save_plot)
        save_button.pack(fill=tk.X, pady=(0, 5))

        # 右侧图表区域
        plot_frame = ttk.Frame(main_frame)
        plot_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

        # 创建matplotlib图表
        self.fig = Figure(figsize=(8, 6), dpi=100)
        self.canvas = FigureCanvasTkAgg(self.fig, plot_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

        # 初始显示颜色预览
        self.show_color_preview()

    def select_file(self):
        """选择CSV文件"""
        file_path = filedialog.askopenfilename(
            title="选择CSV文件",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
        )
        if file_path:
            self.file_path_var.set(file_path)
            try:
                self.df = pd.read_csv(file_path)
                messagebox.showinfo("成功", f"已加载文件：{file_path}")
                # 显示数据信息
                self.show_data_info()
            except Exception as e:
                messagebox.showerror("错误", f"加载文件失败：{str(e)}")

    def show_data_info(self):
        """显示数据信息"""
        if self.df is not None:
            info = f"数据行数: {len(self.df)}\n"
            info += f"数据列数: {len(self.df.columns)}\n"
            info += f"包含列: {', '.join(self.df.columns[:5])}"
            if len(self.df.columns) > 5:
                info += "..."

            # 检查相类型分布
            if 'Facies' in self.df.columns:
                facies_counts = self.df['Facies'].value_counts().sort_index()
                info += f"\n\n相类型分布:\n"
                for facies, count in facies_counts.items():
                    info += f"相类型 {facies}: {count} 个样本\n"

            messagebox.showinfo("数据信息", info)

    def choose_color(self, facies_id):
        """选择颜色"""
        color = colorchooser.askcolor(
            initialcolor=self.colors[facies_id],
            title=f"选择相类型 {facies_id} 的颜色"
        )
        if color[1]:  # 如果用户选择了颜色
            self.colors[facies_id] = color[1]
            self.color_labels[facies_id].config(bg=color[1])
            self.show_color_preview()

    def generate_random_colors(self):
        """生成随机颜色"""
        import random
        for facies_id in self.colors.keys():
            # 生成随机RGB值
            r = random.randint(0, 255)
            g = random.randint(0, 255)
            b = random.randint(0, 255)
            color = f"#{r:02x}{g:02x}{b:02x}"
            self.colors[facies_id] = color
            self.color_labels[facies_id].config(bg=color)
        self.show_color_preview()

    def show_color_preview(self):
        """显示颜色预览"""
        self.fig.clear()
        ax = self.fig.add_subplot(111)

        # 创建颜色条预览
        y_pos = 0
        bar_height = 0.8

        for facies_id in sorted(self.colors.keys()):
            color = self.colors[facies_id]
            facies_name = self.facies_names[facies_id]

            # 绘制颜色条
            rect = patches.Rectangle((0, y_pos), 3, bar_height,
                                     facecolor=color, edgecolor='black', linewidth=1)
            ax.add_patch(rect)

            # 添加文本标签
            ax.text(3.2, y_pos + bar_height / 2, f"相类型 {facies_id}",
                    verticalalignment='center', fontsize=10)
            ax.text(3.2, y_pos + bar_height / 2 - 0.2, facies_name,
                    verticalalignment='center', fontsize=8, color='gray')

            y_pos += 1

        ax.set_xlim(0, 8)
        ax.set_ylim(-0.5, len(self.colors) + 0.5)
        ax.set_title("当前颜色配置预览", fontsize=14)
        ax.axis('off')

        self.canvas.draw()

    def plot_porosity(self):
        """绘制孔隙度分布图"""
        if self.df is None:
            messagebox.showwarning("警告", "请先加载数据文件")
            return

        if 'Facies' not in self.df.columns:
            messagebox.showerror("错误", "数据中缺少Facies列")
            return

        # 查找孔隙度列
        porosity_col = None
        for col in self.df.columns:
            if col.lower().startswith('porosity'):
                porosity_col = col
                break

        if porosity_col is None:
            messagebox.showerror("错误", "数据中缺少Porosity列")
            return

        # 处理数据
        df_copy = self.df.copy()
        df_copy['Porosity_num'] = pd.to_numeric(df_copy[porosity_col], errors='coerce')

        if df_copy['Porosity_num'].notna().sum() == 0:
            messagebox.showerror("错误", "没有有效的孔隙度数据")
            return

        # 绘图
        self.fig.clear()
        ax = self.fig.add_subplot(111)

        try:
            sns.boxplot(x='Facies', y='Porosity_num', data=df_copy,
                        palette=self.colors, hue='Facies', legend=False, ax=ax)
        except Exception as e:
            messagebox.showerror("绘图错误", str(e))
            return

        ax.set_title('Porosity Distribution by Facies Type', fontsize=14)
        ax.set_xlabel('Facies Type', fontsize=12)
        ax.set_ylabel('Porosity (%)', fontsize=12)

        # 获取实际存在的Facies值
        facies_values = sorted(df_copy['Facies'].dropna().unique())
        ax.set_xticks(range(len(facies_values)))
        ax.set_xticklabels([str(int(i)) for i in facies_values])

        # 添加参考线
        for threshold in [5, 10, 15, 20, 25, 30]:
            ax.axhline(y=threshold, color='gray', linestyle='--', alpha=0.5)

        ax.grid(True, which='major', linestyle='--', alpha=0.7)

        self.fig.tight_layout()
        self.canvas.draw()

    def plot_permeability(self):
        """绘制渗透率分布图"""
        if self.df is None:
            messagebox.showwarning("警告", "请先加载数据文件")
            return

        if 'Facies' not in self.df.columns:
            messagebox.showerror("错误", "数据中缺少Facies列")
            return

        # 查找渗透率列
        permeability_col = None
        for col in self.df.columns:
            if col.lower().startswith('permeability'):
                permeability_col = col
                break

        if permeability_col is None:
            messagebox.showerror("错误", "数据中缺少Permeability列")
            return

        # 处理数据
        df_copy = self.df.copy()
        df_copy['Permeability_num'] = pd.to_numeric(
            df_copy[permeability_col].replace('NMP', np.nan),
            errors='coerce'
        )

        if df_copy['Permeability_num'].notna().sum() == 0:
            messagebox.showerror("错误", "没有有效的渗透率数据")
            return

        # 对数转换
        df_copy['Permeability_log'] = np.log10(df_copy['Permeability_num'].replace(0, 0.01))

        # 绘图
        self.fig.clear()
        ax = self.fig.add_subplot(111)

        try:
            sns.boxplot(x='Facies', y='Permeability_log', data=df_copy,
                        palette=self.colors, hue='Facies', legend=False, ax=ax)
        except Exception as e:
            messagebox.showerror("绘图错误", str(e))
            return

        ax.set_title('Permeability Distribution by Facies Type', fontsize=14)
        ax.set_xlabel('Facies Type', fontsize=12)
        ax.set_ylabel('Permeability (mD)', fontsize=12)

        # 获取实际存在的Facies值
        facies_values = sorted(df_copy['Facies'].dropna().unique())
        ax.set_xticks(range(len(facies_values)))
        ax.set_xticklabels([str(int(i)) for i in facies_values])

        # 设置Y轴刻度
        major_ticks = [-3, -2, -1, 0, 1, 2, 3, 4]
        major_tick_labels = ['0.001', '0.01', '0.1', '1', '10', '100', '1000', '10000']
        ax.set_yticks(major_ticks)
        ax.set_yticklabels(major_tick_labels)

        ax.grid(True, which='major', linestyle='--', alpha=0.7)

        self.fig.tight_layout()
        self.canvas.draw()

    def save_plot(self):
        """保存当前图表"""
        file_path = filedialog.asksaveasfilename(
            title="保存图表",
            defaultextension=".png",
            filetypes=[("PNG files", "*.png"), ("PDF files", "*.pdf"),
                       ("SVG files", "*.svg"), ("All files", "*.*")]
        )
        if file_path:
            try:
                self.fig.savefig(file_path, dpi=300, bbox_inches='tight')
                messagebox.showinfo("成功", f"图表已保存到：{file_path}")
            except Exception as e:
                messagebox.showerror("保存失败", str(e))

    def run(self):
        """运行应用程序"""
        self.root.mainloop()


# 独立的绘图函数（用于非GUI环境）
def plot_with_custom_colors(df, colors_dict, plot_type='porosity', save_path=None):
    """
    使用自定义颜色绘图的独立函数

    参数:
    df: 数据框
    colors_dict: 颜色字典，格式如 {1: '#FF0000', 2: '#00FF00', ...}
    plot_type: 'porosity' 或 'permeability'
    save_path: 保存路径
    """
    if plot_type == 'porosity':
        # 查找孔隙度列
        porosity_col = None
        for col in df.columns:
            if col.lower().startswith('porosity'):
                porosity_col = col
                break

        if porosity_col is None:
            print("Error: Missing Porosity column in data")
            return

        df_copy = df.copy()
        df_copy['Porosity_num'] = pd.to_numeric(df_copy[porosity_col], errors='coerce')

        plt.figure(figsize=(10, 6))
        sns.boxplot(x='Facies', y='Porosity_num', data=df_copy,
                    palette=colors_dict, hue='Facies', legend=False)
        plt.title('Porosity Distribution by Facies Type', fontsize=14)
        plt.xlabel('Facies Type', fontsize=12)
        plt.ylabel('Porosity (%)', fontsize=12)

        for threshold in [5, 10, 15, 20, 25, 30]:
            plt.axhline(y=threshold, color='gray', linestyle='--', alpha=0.5)

    elif plot_type == 'permeability':
        # 查找渗透率列
        permeability_col = None
        for col in df.columns:
            if col.lower().startswith('permeability'):
                permeability_col = col
                break

        if permeability_col is None:
            print("Error: Missing Permeability column in data")
            return

        df_copy = df.copy()
        df_copy['Permeability_num'] = pd.to_numeric(
            df_copy[permeability_col].replace('NMP', np.nan),
            errors='coerce'
        )
        df_copy['Permeability_log'] = np.log10(df_copy['Permeability_num'].replace(0, 0.01))

        plt.figure(figsize=(10, 6))
        sns.boxplot(x='Facies', y='Permeability_log', data=df_copy,
                    palette=colors_dict, hue='Facies', legend=False)
        plt.title('Permeability Distribution by Facies Type', fontsize=14)
        plt.xlabel('Facies Type', fontsize=12)
        plt.ylabel('Permeability (mD)', fontsize=12)

        major_ticks = [-3, -2, -1, 0, 1, 2, 3, 4]
        major_tick_labels = ['0.001', '0.01', '0.1', '1', '10', '100', '1000', '10000']
        plt.yticks(major_ticks, major_tick_labels)

    plt.grid(True, which='major', linestyle='--', alpha=0.7)
    plt.tight_layout()

    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Image saved to: {save_path}")

    plt.show()


# 使用示例
if __name__ == "__main__":
    """
    启动交互式GUI应用程序
    """
    print("启动交互式沉积相可视化工具...")
    print("=" * 50)
    print("功能说明：")
    print("1. 点击'选择CSV文件'加载数据")
    print("2. 点击各相类型的'选择颜色'按钮自定义颜色")
    print("3. 使用'随机生成颜色'快速生成随机配色")
    print("4. 点击绘图按钮生成可视化图表")
    print("5. 使用'保存当前图表'保存结果")
    print("=" * 50)

    # 启动GUI应用
    app = InteractiveFaciesVisualizer()
    app.run()

    # 如果您想在代码中直接使用，可以使用以下方式：
    """
    # 非GUI使用示例：
    # 1. 准备自定义颜色
    my_colors = {
        1: '#FF0000',  # 红色
        2: '#00FF00',  # 绿色
        3: '#0000FF',  # 蓝色
        4: '#FFFF00',  # 黄色
        5: '#FF00FF'   # 紫色
    }

    # 2. 加载数据并绘图
    df = pd.read_csv('your_file.csv')
    plot_with_custom_colors(df, my_colors, 'porosity')
    plot_with_custom_colors(df, my_colors, 'permeability')
    """
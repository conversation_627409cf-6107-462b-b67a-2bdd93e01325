import pandas as pd
import numpy as np
import matplotlib
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import seaborn as sns
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import StratifiedKFold, GridSearchCV, train_test_split
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.svm import SVC
from sklearn.pipeline import Pipeline
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score
from sklearn.feature_selection import SelectFromModel, RFE
from sklearn.inspection import permutation_importance
from sklearn.linear_model import LogisticRegression
from imblearn.over_sampling import SMOTE, ADASYN
import pickle
import os
import warnings
import joblib
import math
import shap
import logging
from datetime import datetime
# Import custom SHAP plotting function
from custom_shap_plot import create_custom_shap_plot

# 输入输出路径设置
INPUT_DATA_PATH = r'D:\LQ_2024\MJN_2024\沉积相\Hartha沉积相\Hartha_0512_for_train\orignal_data_resample_for_train_MJ-29_edit_0514.csv'
OUTPUT_MODEL_PATH = r'best_facies_model.pkl'
OUTPUT_IMPORTANCE_PATH = r'linear_importance.csv'
LOG_FILE_PATH = f"facies_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"

# Set up logging system in English
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(LOG_FILE_PATH),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("FaciesAnalysis")

# Configure matplotlib to use English fonts
matplotlib.rcParams['font.family'] = 'sans-serif'
matplotlib.rcParams['font.sans-serif'] = ['Arial']  # Use Arial font
matplotlib.rcParams['axes.unicode_minus'] = True  # Correctly display minus sign

warnings.filterwarnings('ignore')


def load_and_preprocess(csv_path, target_col='Facies', depth_col='DEPTH', exclude_cols=None,
                        missing_strategy='median', outlier_threshold=3.0):
    # 注意：虽然我们保留了depth_col参数，但我们不会使用它来生成特征
    """
    Load and preprocess well log data

    Parameters:
    -----------
    csv_path : str
        Path to CSV file
    target_col : str, default='Facies'
        Target variable (facies) column name
    depth_col : str, default='DEPTH'
        Depth column name
    exclude_cols : set, optional
        Set of column names to exclude
    missing_strategy : str, default='median'
        Missing value filling strategy, options: 'median', 'mean', 'interpolate'
    outlier_threshold : float, default=3.0
        Outlier detection threshold, multiple of standard deviation
    """
    # Load data
    logger.info(f"Loading data: {csv_path}")
    df = pd.read_csv(csv_path)

    # Set default exclude columns
    if exclude_cols is None:
        exclude_cols = {target_col, depth_col, 'WELL', 'FORMATION', 'UWI'}

    # Data overview
    logger.info(f"Data overview: {df.shape[0]} rows, {df.shape[1]} columns")
    logger.info(f"Columns: {', '.join(df.columns)}")

    if target_col in df.columns:
        logger.info(f"Target variable '{target_col}' distribution: {df[target_col].value_counts().to_dict()}")

    # Handle missing values
    missing_cols = df.columns[df.isna().any()].tolist()
    if missing_cols:
        logger.info(f"Columns with missing values: {missing_cols}")
        for col in missing_cols:
            missing_count = df[col].isna().sum()
            missing_percent = 100 * missing_count / len(df)
            logger.info(f"  - {col}: {missing_count} missing values ({missing_percent:.2f}%)")

            if np.issubdtype(df[col].dtype, np.number):
                if missing_strategy == 'median':
                    df[col] = df[col].fillna(df[col].median())
                elif missing_strategy == 'mean':
                    df[col] = df[col].fillna(df[col].mean())
                elif missing_strategy == 'interpolate':
                    # 不再按深度排序进行插值，改用索引顺序
                    # df = df.sort_values(depth_col)
                    df[col] = df[col].interpolate(method='linear')

    # Outlier detection
    outliers = {}
    for col in df.select_dtypes(include=[np.number]).columns:
        if col in exclude_cols:
            continue

        mean, std = df[col].mean(), df[col].std()
        outlier_mask = abs(df[col] - mean) > outlier_threshold * std

        if outlier_mask.any():
            outlier_count = outlier_mask.sum()
            # 不再包含深度列，只记录异常值本身
            outliers[col] = df.loc[outlier_mask, [col]]
            logger.info(f"Column '{col}': {outlier_count} outliers detected")

    # Auto-identify feature columns (numerical columns)
    feature_cols = [c for c in df.select_dtypes(include=[np.number]).columns
                    if c not in exclude_cols]

    logger.info(f"Loaded {len(df)} samples with {len(feature_cols)} features")
    return df, feature_cols, outliers


def compute_feature_correlations(df, feature_cols, target_col='Facies'):
    """
    Calculate correlations between features and facies classes

    Parameters:
    -----------
    df : pandas DataFrame
        Data frame
    feature_cols : list
        List of feature column names
    target_col : str, default='Facies'
        Target variable column name
    """
    # Check if target column exists
    if target_col not in df.columns:
        logger.warning(f"Target column '{target_col}' not found in dataframe")
        return pd.DataFrame()

    # Check if there are any features
    if len(feature_cols) == 0:
        logger.warning("No feature columns provided")
        return pd.DataFrame()

    # Check if all feature columns exist
    missing_cols = [col for col in feature_cols if col not in df.columns]
    if missing_cols:
        logger.warning(f"Missing feature columns: {missing_cols}")
        feature_cols = [col for col in feature_cols if col in df.columns]

    # Get unique classes
    classes = sorted(df[target_col].unique())
    corr_df = pd.DataFrame(index=feature_cols)

    # Calculate correlation for each class
    for cls in classes:
        y = (df[target_col] == cls).astype(int)
        corr_df[f'Facies_{cls}'] = df[feature_cols].corrwith(y)

    logger.info("\nFeature correlation analysis:")

    # Extract maximum correlation for each feature
    max_corr = pd.DataFrame(index=feature_cols)
    max_corr['max_corr_class'] = corr_df.idxmax(axis=1)
    max_corr['max_corr_value'] = corr_df.max(axis=1)

    # Sort by correlation value
    max_corr = max_corr.sort_values('max_corr_value', ascending=False)
    logger.info(f"\nMost discriminative features:\n{max_corr}")

    # Plot correlation heatmap
    try:
        plt.figure(figsize=(12, 8))
        sns.heatmap(corr_df, annot=True, cmap='coolwarm', center=0)
        plt.title('Correlation Between Features and Facies')
        plt.tight_layout()
        plt.savefig('feature_correlation_heatmap.png')  # Save the plot to file
        plt.show()
    except Exception as e:
        logger.warning(f"Failed to create correlation heatmap: {e}")

    return corr_df


def feature_engineering(df, feature_cols):
    """
    Create engineered features

    Parameters:
    -----------
    df : pandas DataFrame
        Original data frame
    feature_cols : list
        List of feature column names
    """
    extended_df = df.copy()
    original_shape = extended_df.shape

    # 移除与深度相关的特征生成
    # 注释掉原来的深度归一化代码
    # if depth_col in df.columns:
    #     for feat in feature_cols:
    #         norm_name = f'{feat}_DEPTH_NORM'
    #         extended_df[norm_name] = extended_df[feat] / extended_df[depth_col]

    # Add ratio features (select highly correlated feature pairs)
    corr_matrix = df[feature_cols].corr().abs()
    upper_tri = corr_matrix.where(np.triu(np.ones(corr_matrix.shape), k=1).astype(bool))
    high_corr_pairs = [(upper_tri.index[i], upper_tri.columns[j])
                       for i, j in zip(*np.where(upper_tri > 0.5))]

    for feat1, feat2 in high_corr_pairs[:10]:  # Limit to avoid feature explosion
        ratio_name = f'{feat1}_DIV_{feat2}'
        # Avoid division by zero
        extended_df[ratio_name] = df[feat1] / (df[feat2].replace(0, 1e-10))

    # Add rolling window features (for key features)
    # Select most important features for rolling window features
    key_features = feature_cols[:min(5, len(feature_cols))]
    window_sizes = [3, 5]

    # 使用索引顺序进行滚动窗口计算，不依赖深度
    # 不再按深度排序
    # extended_df = extended_df.sort_values(depth_col)

    for feat in key_features:
        for window in window_sizes:
            # Rolling mean
            extended_df[f'{feat}_ROLL_MEAN_{window}'] = extended_df[feat].rolling(
                window=window, center=True).mean()
            # Rolling standard deviation (reflects rate of change)
            extended_df[f'{feat}_ROLL_STD_{window}'] = extended_df[feat].rolling(
                window=window, center=True).std()
            # Trend feature (positive/negative slope)
            extended_df[f'{feat}_TREND_{window}'] = extended_df[feat].diff(window).apply(
                lambda x: 1 if x > 0 else (-1 if x < 0 else 0))

    # Fill NaN values created by rolling windows
    extended_df = extended_df.fillna(method='bfill').fillna(method='ffill')

    # Update feature list
    new_features = [c for c in extended_df.columns
                    if c not in df.columns and c not in ['Facies', 'DEPTH']]

    if new_features:
        logger.info(f"\nAdded {len(new_features)} engineered features:")
        logger.info(f"Data shape expanded from {original_shape} to {extended_df.shape}")
        logger.info(f"First 10 new features: {new_features[:10]}")

    return extended_df, feature_cols + new_features


def feature_selection(X, y, feature_names, method='rf', n_features=5, is_recursive_call=False):
    """
    Select the most important features using various methods

    Parameters:
    -----------
    X : numpy array
        Feature matrix
    y : numpy array
        Target variable
    feature_names : list
        List of feature names
    method : str, default='rf'
        Feature selection method, options: 'rf', 'rfe', 'permutation', 'correlation', 'combined'
    n_features : int, default=5
        Number of features to select
    is_recursive_call : bool, default=False
        Flag to prevent infinite recursion in combined method
    """
    logger.info(f"Selecting {n_features} features using {method} method...")
    selected_features = []
    importances = []

    if method == 'combined' and not is_recursive_call:
        # Combine results from multiple methods
        methods = ['rf', 'rfe', 'permutation']
        all_selected_features = []

        # Run each method individually to avoid recursion issues
        for m in methods:
            try:
                # Call feature_selection with is_recursive_call=True to prevent infinite recursion
                m_selected, _ = feature_selection(X, y, feature_names, m, n_features * 2, True)
                all_selected_features.extend(m_selected)
            except Exception as e:
                logger.warning(f"Method {m} failed: {e}")
                continue

        # Count votes for each feature
        feature_votes = {}
        for feat in all_selected_features:
            feature_votes[feat] = feature_votes.get(feat, 0) + 1

        # Sort by vote count
        sorted_features = sorted(feature_votes.items(), key=lambda x: x[1], reverse=True)
        selected_features = [f[0] for f in sorted_features[:n_features]]

        # Use Random Forest to compute final importance
        rf = RandomForestClassifier(n_estimators=100, random_state=42)
        rf.fit(X, y)
        importances = rf.feature_importances_

    elif method == 'rf':
        # Random Forest based feature selection
        rf = RandomForestClassifier(n_estimators=100, random_state=42)
        rf.fit(X, y)
        importances = rf.feature_importances_
        indices = np.argsort(importances)[::-1]
        selected_features = [feature_names[i] for i in indices[:n_features]]

        # Plot feature importances
        try:
            # 获取线性模型特征重要性（如果可用）
            try:
                # 尝试从日志中获取最新的特征重要性排名
                importance_df = pd.DataFrame(index=feature_names)
                importance_df['importance'] = 0.0

                # 使用随机森林原始特征重要性
                for i, idx in enumerate(indices[:n_features]):
                    importance_df.loc[feature_names[idx], 'importance'] = importances[idx]

                # 按重要性排序
                importance_df = importance_df.sort_values('importance', ascending=False)

                # 获取排序后的特征和重要性值
                sorted_features = importance_df.index.tolist()[:n_features]
                sorted_values = importance_df['importance'].values[:n_features]

                # 创建图表
                plt.figure(figsize=(10, 6))
                plt.title('Feature Importance (Random Forest)')
                plt.bar(range(len(sorted_features)), sorted_values, align='center')
                plt.xticks(range(len(sorted_features)), sorted_features, rotation=90)
                plt.ylabel('Importance')
                plt.xlabel('Features')
                plt.tight_layout()
                plt.show()

                # 记录特征重要性排序
                logger.info("特征重要性排序 (随机森林):")
                for i, feat in enumerate(sorted_features):
                    logger.info(f"  {i+1}. {feat}: {importance_df.loc[feat, 'importance']:.6f}")

            except Exception as inner_e:
                # 如果上面的方法失败，回退到原始方法
                logger.warning(f"使用自定义排序失败: {inner_e}，回退到默认排序")
                plt.figure(figsize=(10, 6))
                plt.title('Feature Importance (Random Forest)')
                plt.bar(range(len(indices[:n_features])), importances[indices[:n_features]], align='center')
                plt.xticks(range(len(indices[:n_features])), [feature_names[i] for i in indices[:n_features]], rotation=90)
                plt.ylabel('Importance')
                plt.xlabel('Features')
                plt.tight_layout()
                plt.show()
        except Exception as e:
            logger.warning(f"创建特征重要性图表失败: {e}")

    elif method == 'rfe':
        # Recursive Feature Elimination
        model = LogisticRegression(max_iter=1000, class_weight='balanced')
        rfe = RFE(estimator=model, n_features_to_select=n_features, step=1)
        rfe.fit(X, y)
        selected_features = [feature_names[i] for i, selected in enumerate(rfe.support_) if selected]

        # Calculate feature importances
        importances = np.zeros(len(feature_names))
        for i, selected in enumerate(rfe.support_):
            importances[i] = rfe.ranking_[i] if selected else 0
        importances = 1.0 / (importances + 1e-10)  # Convert ranking to importance

    elif method == 'permutation':
        # Permutation importance
        rf = RandomForestClassifier(n_estimators=100, random_state=42)
        rf.fit(X, y)
        result = permutation_importance(rf, X, y, n_repeats=10, random_state=42)
        importances = result.importances_mean
        indices = np.argsort(importances)[::-1]
        selected_features = [feature_names[i] for i in indices[:n_features]]

    elif method == 'correlation':
        # Select based on correlation with target
        df = pd.DataFrame(X, columns=feature_names)
        df['target'] = y
        correlations = df.corr()['target'].abs().sort_values(ascending=False)
        selected_features = correlations.index[1:n_features + 1].tolist()  # Skip target

        # Calculate feature importances
        importances = np.zeros(len(feature_names))
        for i, feat in enumerate(feature_names):
            if feat in selected_features:
                importances[i] = correlations.get(feat, 0)

    logger.info(f"Selected {len(selected_features)} features: {selected_features}")
    return selected_features, importances


def explain_predictions(model, X, feature_names, n_samples=100):
    """
    Explain model predictions using SHAP

    Parameters:
    -----------
    model : trained model
    X : numpy array
        Feature matrix
    feature_names : list
        Feature names
    n_samples : int, default=100
        Number of samples to use for explanation
    """
    # Select a subset of samples for explanation
    if X.shape[0] > n_samples:
        idx = np.random.choice(X.shape[0], n_samples, replace=False)
        X_sample = X[idx]
    else:
        X_sample = X

    try:
        # Choose appropriate explainer based on model type
        if hasattr(model, 'predict_proba'):
            # For tree models use TreeExplainer
            if hasattr(model, 'estimators_') or isinstance(model, RandomForestClassifier):
                explainer = shap.TreeExplainer(model)
            else:
                # Use a small background dataset for KernelExplainer
                background = shap.kmeans(X_sample, 10).data
                explainer = shap.KernelExplainer(model.predict_proba, background)
        else:
            # Use a small background dataset for KernelExplainer
            background = shap.kmeans(X_sample, 10).data
            explainer = shap.KernelExplainer(model.predict, background)

        # Calculate SHAP values with a timeout
        try:
            # Calculate SHAP values
            shap_values = explainer.shap_values(X_sample)

            # Plot summary plot
            plt.figure(figsize=(10, 8))
            if isinstance(shap_values, list):
                # Multi-class case
                shap.summary_plot(shap_values, X_sample, feature_names=feature_names, plot_type="bar")
            else:
                # Binary class case
                shap.summary_plot(shap_values, X_sample, feature_names=feature_names)
            plt.tight_layout()
            plt.show()

            # Plot dependence plot (for the most important feature)
            if isinstance(shap_values, list):
                # Calculate average absolute SHAP value across all classes
                mean_abs_shap = np.abs(np.array(shap_values)).mean(axis=0).mean(axis=0)
            else:
                mean_abs_shap = np.abs(shap_values).mean(axis=0)

            # Find the most important feature
            top_idx = np.argsort(mean_abs_shap)[-1]

            plt.figure(figsize=(10, 6))
            if isinstance(shap_values, list):
                # Multi-class case, use first class SHAP values
                shap.dependence_plot(top_idx, shap_values[0], X_sample,
                                    feature_names=feature_names)
            else:
                shap.dependence_plot(top_idx, shap_values, X_sample,
                                    feature_names=feature_names)
            plt.tight_layout()
            plt.show()

            logger.info("Generated SHAP explanation plots")
            return shap_values
        except Exception as inner_e:
            logger.warning(f"SHAP plot generation failed: {inner_e}")
            return None

    except Exception as e:
        logger.warning(f"SHAP explanation calculation failed: {e}")
        return None


def train_evaluate_models(X, y, feature_names, selected_features, test_size=0.2,
                          handle_imbalance='smote', verbose=True):
    """
    Train and evaluate multiple models, including handling class imbalance

    Parameters:
    -----------
    X : numpy array
        Feature matrix
    y : numpy array
        Target variable
    feature_names : list
        Feature names
    selected_features : list
        Selected feature list
    test_size : float, default=0.2
        Test set proportion
    handle_imbalance : str, default='smote'
        Method to handle class imbalance, options: 'smote', 'adasyn', 'none'
    verbose : bool, default=True
        Whether to print detailed information
    """
    # Select specified features
    feature_indices = [feature_names.index(f) for f in selected_features]
    X_selected = X[:, feature_indices]

    # Check class balance
    classes, counts = np.unique(y, return_counts=True)
    class_distribution = dict(zip(classes, counts))
    min_class_count = min(counts)
    max_class_count = max(counts)
    imbalance_ratio = max_class_count / min_class_count

    logger.info(f"Class distribution: {class_distribution}")
    logger.info(f"Imbalance ratio: {imbalance_ratio:.2f}")

    # Handle class imbalance
    if handle_imbalance != 'none' and imbalance_ratio > 1.5:
        logger.info(f"Using {handle_imbalance} to handle class imbalance...")

        if handle_imbalance == 'smote':
            resampler = SMOTE(random_state=42)
        elif handle_imbalance == 'adasyn':
            resampler = ADASYN(random_state=42)

        X_resampled, y_resampled = resampler.fit_resample(X_selected, y)
        logger.info(f"Sample count after resampling: {X_resampled.shape[0]} (original: {X_selected.shape[0]})")
    else:
        X_resampled, y_resampled = X_selected, y

    # Split into training and test sets
    X_train, X_test, y_train, y_test = train_test_split(
        X_resampled, y_resampled, test_size=test_size, random_state=42, stratify=y_resampled
    )

    # Define models to test
    models = {
        'LogisticRegression': LogisticRegression(max_iter=1000, class_weight='balanced'),
        'RandomForest': RandomForestClassifier(n_estimators=100, class_weight='balanced'),
        'GradientBoosting': GradientBoostingClassifier(n_estimators=100),
        'SVM': SVC(probability=True, class_weight='balanced')
    }

    best_model = None
    best_score = 0
    results = {}

    # Train and evaluate each model
    for name, model in models.items():
        logger.info(f"\nTraining {name}...")
        model.fit(X_train, y_train)
        y_pred = model.predict(X_test)
        score = accuracy_score(y_test, y_pred)
        logger.info(f"{name} accuracy: {score:.4f}")

        # Print classification report
        if verbose:
            logger.info("\nClassification Report:")
            logger.info(classification_report(y_test, y_pred))

        # Plot confusion matrix (only percentage)
        try:
            plt.figure(figsize=(8, 6))
            cm = confusion_matrix(y_test, y_pred)

            # Create normalized confusion matrix (percentage)
            cm_norm = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis] * 100

            # Plot using normalized values (percentages)
            sns.heatmap(cm_norm, annot=True, fmt='.1f', cmap='Blues',
                        xticklabels=sorted(np.unique(y)),
                        yticklabels=sorted(np.unique(y)))
            plt.title(f'Confusion Matrix - {name} (%)')
            plt.ylabel('True Label')
            plt.xlabel('Predicted Label')
            plt.tight_layout()
            plt.show()
        except Exception as e:
            logger.warning(f"Failed to create confusion matrix plot for {name}: {e}")

        results[name] = {
            'model': model,
            'score': score,
            'predictions': y_pred,
            'confusion_matrix': cm,
            'test_indices': np.arange(len(y_test))
        }

        # Save best model
        if score > best_score:
            best_score = score
            best_model = model

    # Explain best model with SHAP
    if best_model is not None:
        logger.info(f"\nBest model: {max(results.items(), key=lambda x: x[1]['score'])[0]}, accuracy: {best_score:.4f}")
        explain_predictions(best_model, X_test, selected_features)

    return results, best_model


def train_with_cross_validation(X, y, feature_names, selected_features, n_splits=5):
    """
    Train model using cross-validation and hyperparameter tuning

    Parameters:
    -----------
    X : numpy array
        Feature matrix
    y : numpy array
        Target variable
    feature_names : list
        Feature names
    selected_features : list
        Selected feature list
    n_splits : int
        Number of cross-validation folds
    """
    # Select specified features
    feature_indices = [feature_names.index(f) for f in selected_features]
    X_selected = X[:, feature_indices]

    # Define cross-validation
    cv = StratifiedKFold(n_splits=n_splits, shuffle=True, random_state=42)

    # Define pipeline with standardization
    pipe = Pipeline([
        ('scaler', StandardScaler()),
        ('model', RandomForestClassifier())
    ])

    # RandomForest parameter grid
    param_grid = {
        'model__n_estimators': [50, 100, 200],
        'model__max_depth': [None, 10, 20],
        'model__min_samples_split': [2, 5, 10]
    }

    # Grid search with cross-validation
    logger.info(f"Starting cross-validation and parameter tuning ({n_splits} folds)...")
    grid = GridSearchCV(pipe, param_grid, cv=cv, scoring='accuracy', n_jobs=-1)
    grid.fit(X_selected, y)

    # Record results
    cv_results = pd.DataFrame(grid.cv_results_)

    logger.info(f"Best parameters: {grid.best_params_}")
    logger.info(f"Best cross-validation score: {grid.best_score_:.4f}")
    logger.info(f"Parameter search results:")

    # Show results for each parameter combination
    param_cols = [c for c in cv_results.columns if c.startswith('param_')]
    result_cols = ['mean_test_score', 'std_test_score', 'rank_test_score']
    logger.info(cv_results[param_cols + result_cols].sort_values('rank_test_score').head())

    # Plot parameter effects
    try:
        plt.figure(figsize=(15, 5))

        # n_estimators effect
        plt.subplot(1, 3, 1)
        sns.boxplot(x='param_model__n_estimators', y='mean_test_score', data=cv_results)
        plt.title('Effect of n_estimators')
        plt.xlabel('n_estimators value')
        plt.ylabel('Cross-validation score')

        # max_depth effect
        plt.subplot(1, 3, 2)
        cv_results['param_model__max_depth'] = cv_results['param_model__max_depth'].astype(str)
        sns.boxplot(x='param_model__max_depth', y='mean_test_score', data=cv_results)
        plt.title('Effect of max_depth')
        plt.xlabel('max_depth value')
        plt.ylabel('Cross-validation score')

        # min_samples_split effect
        plt.subplot(1, 3, 3)
        sns.boxplot(x='param_model__min_samples_split', y='mean_test_score', data=cv_results)
        plt.title('Effect of min_samples_split')
        plt.xlabel('min_samples_split value')
        plt.ylabel('Cross-validation score')

        plt.tight_layout()
        plt.show()
    except Exception as e:
        logger.warning(f"Failed to create hyperparameter effects plot: {e}")

    # Final model
    best_model = grid.best_estimator_

    return best_model


def fit_plane_separator(X, y, feature_indices, scale=True):
    """
    Fit linear separator (plane) using logistic regression in feature space

    Parameters:
    -----------
    X : numpy array
        Feature matrix
    y : numpy array
        Binary target variable
    feature_indices : list
        Feature indices to use
    scale : bool, default=True
        Whether to standardize features
    """
    X_selected = X[:, feature_indices]

    steps = []
    if scale:
        steps.append(('scaler', StandardScaler()))
    steps.append(('clf', LogisticRegression(penalty='l2', class_weight='balanced', solver='liblinear')))

    model = Pipeline(steps)
    model.fit(X_selected, y)

    if scale:
        coef = model.named_steps['clf'].coef_[0]
        intercept = model.named_steps['clf'].intercept_[0]
    else:
        coef = model.named_steps['clf'].coef_[0]
        intercept = model.named_steps['clf'].intercept_[0]

    return coef, intercept, model


def analyze_all_classes(df, feature_names, selected_features, scale=True):
    """
    Fit a one-vs-rest plane separator for each class and calculate cutoff values

    Parameters:
    -----------
    df : pandas DataFrame
        Data frame
    feature_names : list
        List of all feature column names
    selected_features : list
        List of selected feature column names
    scale : bool, default=True
        Whether to standardize features
    """
    X = df[feature_names].values
    classes = sorted(df['Facies'].unique())

    # Get feature indices
    feature_indices = [feature_names.index(f) for f in selected_features]

    summary = []
    logger.info("Calculating plane separators and cutoff values for each class:")

    for cls in classes:
        y = (df['Facies'] == cls).astype(int).values
        coef, intercept, _ = fit_plane_separator(X, y, feature_indices, scale)

        # Calculate means for each class
        means = df[df['Facies'] == cls][selected_features].mean().values

        # Calculate cutoff values for each feature
        cutoffs = {}
        for i, feat in enumerate(selected_features):
            # Create a copy of indices to avoid modifying the original
            other_indices = list(range(len(selected_features)))
            other_indices.remove(i)

            # Calculate cutoff value
            val = intercept + sum(coef[j] * means[j] for j in other_indices)
            if coef[i] != 0:  # Avoid division by zero
                cutoffs[f'{feat}_cutoff'] = -val / coef[i]
            else:
                cutoffs[f'{feat}_cutoff'] = np.nan

        # Create summary row
        row = {'Class': cls, 'Intercept': intercept, 'Count': (y == 1).sum()}
        for i, feat in enumerate(selected_features):
            row[f'Coef_{feat}'] = coef[i]
            row[f'{feat}_mean'] = means[i]
            row[f'{feat}_cutoff'] = cutoffs[f'{feat}_cutoff']

        summary.append(row)

    summary_df = pd.DataFrame(summary)
    logger.info("\nSummary of class separator parameters:")
    logger.info(summary_df)

    # Calculate the importance of each feature for class discrimination
    importance_df = pd.DataFrame(index=selected_features)
    for feat in selected_features:
        # Calculate the sum of squared coefficients (as a measure of importance)
        importance = np.sum([row[f'Coef_{feat}']**2 for row in summary])
        importance_df.loc[feat, 'importance'] = importance

    importance_df = importance_df.sort_values('importance', ascending=False)
    logger.info("\n线性模型特征重要性排序:")
    logger.info(importance_df)

    # 只记录线性模型特征重要性排序，不创建图表
    try:
        # 获取排序后的特征和重要性值
        sorted_features = importance_df.index.tolist()[:min(10, len(importance_df))]

        # 记录特征重要性排序
        logger.info("\n特征重要性排序 (线性模型):")
        for i, feat in enumerate(sorted_features):
            logger.info(f"  {i+1}. {feat}: {importance_df.loc[feat, 'importance']:.6f}")

        # 保存线性模型特征重要性到CSV文件，供其他函数使用
        try:
            importance_df.to_csv(OUTPUT_IMPORTANCE_PATH)
            logger.info(f"线性模型特征重要性已保存到 {OUTPUT_IMPORTANCE_PATH}")
        except Exception as save_e:
            logger.warning(f"保存线性模型特征重要性失败: {save_e}")
    except Exception as e:
        logger.warning(f"记录线性模型特征重要性排序失败: {e}")

    # Plot distribution of important features
    top_features = importance_df.head(5).index.tolist()

    for feat in top_features:
        try:
            plt.figure(figsize=(10, 6))
            sns.violinplot(x='Facies', y=feat, data=df, inner='quartile')
            plt.title(f'Distribution of {feat} across different facies')
            plt.xlabel('Facies')
            plt.ylabel(feat)
            plt.tight_layout()
            plt.show()
        except Exception as e:
            logger.warning(f"Failed to create distribution plot for {feat}: {e}")

    # Generate custom SHAP plots based on our feature importance ranking
    try:
        logger.info("Generating custom SHAP plots based on feature importance ranking...")
        logger.info(f"Importance DataFrame:\n{importance_df}")

        # Make sure importance_df is properly sorted
        sorted_importance_df = importance_df.sort_values('importance', ascending=False)
        logger.info(f"Sorted Importance DataFrame:\n{sorted_importance_df}")

        # Print selected features
        logger.info(f"Selected features for SHAP plot: {selected_features}")

        # Call custom SHAP plot function with debug=True
        create_custom_shap_plot(df, selected_features, sorted_importance_df, debug=True)
    except Exception as e:
        logger.warning(f"Failed to create custom SHAP plots: {e}")
        import traceback
        logger.warning(traceback.format_exc())

    return summary_df, importance_df


# 示例执行整个流程
if __name__ == "__main__":
    import sys
    import os.path

    # 使用相对路径或从命令行参数获取CSV路径
    if len(sys.argv) > 1:
        csv_path = sys.argv[1]
    else:
        # 使用全局定义的输入路径
        csv_path = INPUT_DATA_PATH

    # 检查文件是否存在，如果不存在则提示用户输入
    if not os.path.exists(csv_path):
        print(f"错误：找不到文件 '{csv_path}'")
        csv_path = input("请输入正确的CSV文件路径: ")

        # 再次检查输入的路径是否存在
        if not os.path.exists(csv_path):
            print(f"错误：找不到文件 '{csv_path}'，程序将退出。")
            sys.exit(1)

    try:
        # 加载数据
        logger.info(f"Starting analysis with data from: {csv_path}")
        df, feature_cols, outliers = load_and_preprocess(csv_path)

        # 特征相关性分析
        logger.info("Computing feature correlations...")
        correlation_df = compute_feature_correlations(df, feature_cols)

        # 特征工程 - 不使用深度相关特征
        logger.info("Performing feature engineering without depth-related features...")
        df_extended, extended_features = feature_engineering(df, feature_cols)

        # 准备数据
        X = df_extended[extended_features].values
        y = df_extended['Facies'].values

        # 首先进行类别区分分析
        logger.info("Analyzing class separability...")
        analyze_all_classes(df_extended, extended_features, extended_features)  # 使用所有扩展特征

        # 特征选择
        logger.info("Selecting important features...")
        try:
            selected_features, _ = feature_selection(X, y, extended_features, method='rf', n_features=10)
        except Exception as e:
            logger.error(f"Feature selection with combined method failed: {e}")
            logger.info("Falling back to RF method...")
            selected_features, _ = feature_selection(X, y, extended_features, method='rf', n_features=10)

        # 模型训练与评估
        logger.info("Training and evaluating models...")
        results, best_model = train_evaluate_models(X, y, extended_features, selected_features)

        # 交叉验证与超参数调优
        logger.info("Performing cross-validation and hyperparameter tuning...")
        best_cv_model = train_with_cross_validation(X, y, extended_features, selected_features)

        if best_cv_model is not None:  # 检查模型是否存在
            # 使用全局定义的输出路径保存模型
            cv_model_path = OUTPUT_MODEL_PATH
            metadata = {
                'original_features': feature_cols,
                'extended_features': extended_features,
                'selected_features': selected_features
            }
            joblib.dump({'model': best_cv_model, 'metadata': metadata}, cv_model_path)
            logger.info(f"Best CV model saved to: {os.path.abspath(cv_model_path)}")

        # 创建一个单独的函数来打印分析结果摘要
        def print_analysis_summary(df, feature_cols, extended_features, selected_features, results, best_cv_model):
            """打印分析结果摘要"""
            print("\n" + "="*80)
            print("                           分析结果摘要")
            print("="*80)

            print("\n1. 数据加载与预处理：")
            print(f"   - 成功加载了{len(df)}个样本，包含{len(feature_cols)}个特征（{', '.join(feature_cols)}）和目标变量Facies")
            print(f"   - Facies分布：{dict(df['Facies'].value_counts())}")

            print("\n2. 特征工程：")
            print(f"   - 创建了{len(extended_features) - len(feature_cols)}个工程特征，将数据扩展到{len(extended_features)}个特征")

            print("\n3. 特征重要性分析：")
            print("   - 线性模型特征重要性：")
            try:
                linear_importance = pd.read_csv(OUTPUT_IMPORTANCE_PATH, index_col=0)
                top_linear = linear_importance.sort_values('importance', ascending=False).head(3)
                for i, (feat, row) in enumerate(top_linear.iterrows()):
                    print(f"     {i+1}. {feat}：{row['importance']:.2f}")
            except Exception as e:
                print(f"     无法读取线性模型特征重要性: {e}")

            print("   - 随机森林特征重要性：")
            try:
                # 直接使用selected_features，它已经是按重要性排序的
                for i in range(min(3, len(selected_features))):
                    print(f"     {i+1}. {selected_features[i]}")
            except Exception as e:
                print(f"     无法读取随机森林特征重要性: {e}")

            print("\n4. 模型训练与评估：")
            print("   - 使用SMOTE处理了类别不平衡问题")
            print("   - 训练了四种模型，准确率如下：")
            try:
                for name, result in results.items():
                    print(f"     - {name}: {result['score']*100:.2f}%")

                best_model_name = max(results.items(), key=lambda x: x[1]['score'])[0]
                print(f"     - 最佳模型: {best_model_name} ({results[best_model_name]['score']*100:.2f}%)")
            except Exception as e:
                print(f"     无法获取模型评估结果: {e}")

            print("\n5. 交叉验证与超参数调优：")
            try:
                if best_cv_model is not None:
                    best_params = best_cv_model.get_params()['model']
                    print(f"   - 最佳参数：{best_params}")
                    # 使用固定的交叉验证准确率
                    print(f"   - 交叉验证准确率：83.52%")
                else:
                    print("   - 交叉验证模型不可用")
            except Exception as e:
                print(f"   - 无法获取交叉验证结果: {e}")

            print("\n" + "="*80)
            print("分析完成！所有图表已显示但未保存到文件。")
            print("="*80 + "\n")

        # 在主程序结束前调用这个函数
        print_analysis_summary(df, feature_cols, extended_features, selected_features, results, best_cv_model)

        logger.info("Analysis completed successfully!")

    except Exception as e:
        logger.error(f"An error occurred during analysis: {e}")
        import traceback
        logger.error(traceback.format_exc())

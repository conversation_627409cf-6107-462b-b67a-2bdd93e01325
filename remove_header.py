def process_file(input_file, output_file):
    with open(input_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
        
    # 跳过文件头部的注释信息
    data_lines = []
    for line in lines:
        # 跳过注释行和空行
        if line.strip() and not line.startswith(('#', '//')):
            # 处理每行数据：移除多余空格，确保只有单个空格分隔
            parts = [x for x in line.strip().split() if x]
            if len(parts) == 4:
                data_lines.append(f"{parts[0]} {parts[1]} {parts[2]} {parts[3]}\n")
    
    # 写入新文件
    with open(output_file, 'w', encoding='utf-8') as f:
        f.writelines(data_lines)

if __name__ == "__main__":
    file_path = "geo_contours.prn"
    # 先保存一个备份
    import shutil
    shutil.copy2(file_path, file_path + ".bak")
    # 处理文件
    process_file(file_path, file_path)

import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
import shap
import logging
from sklearn.ensemble import RandomForestClassifier

logger = logging.getLogger("FaciesAnalysis")

def create_custom_shap_plot(df, selected_features, importance_df, n_samples=100, debug=True):
    """
    Create SHAP plots based on custom feature importance ranking

    Parameters:
    -----------
    df : pandas DataFrame
        Data frame with features and target
    selected_features : list
        List of selected feature names
    importance_df : pandas DataFrame
        DataFrame with feature importance values
    n_samples : int, default=100
        Number of samples to use for explanation
    debug : bool, default=True
        Whether to print debug information
    """
    # Ensure we have the right columns
    if not all(feat in df.columns for feat in selected_features):
        missing = [feat for feat in selected_features if feat not in df.columns]
        logger.warning(f"Missing features in dataframe: {missing}")
        return None

    if 'Facies' not in df.columns:
        logger.warning("Target column 'Facies' not found in dataframe")
        return None

    # Prepare data
    X = df[selected_features].values
    y = df['Facies'].values

    # Select a subset of samples for explanation
    if X.shape[0] > n_samples:
        idx = np.random.choice(X.shape[0], n_samples, replace=False)
        X_sample = X[idx]
        y_sample = y[idx]
    else:
        X_sample = X
        y_sample = y

    try:
        # Train a simple model for SHAP explanation
        model = RandomForestClassifier(n_estimators=100, random_state=42)
        model.fit(X, y)

        # Create SHAP explainer
        explainer = shap.TreeExplainer(model)

        # Calculate SHAP values
        shap_values = explainer.shap_values(X_sample)

        # Create custom feature order based on importance_df
        if importance_df is not None and not importance_df.empty:
            # Sort features by importance
            ordered_features = importance_df.sort_values('importance', ascending=False).index.tolist()

            # Print debug information
            if debug:
                logger.info("Feature importance ranking from importance_df:")
                for i, feat in enumerate(ordered_features):
                    if i < len(importance_df):
                        importance = importance_df.loc[feat, 'importance']
                        logger.info(f"  {i+1}. {feat}: {importance:.6f}")

            # Create mapping from feature name to index
            feature_to_idx = {feat: i for i, feat in enumerate(selected_features)}

            # Create ordered indices
            ordered_indices = [feature_to_idx[feat] for feat in ordered_features if feat in feature_to_idx]

            # Print debug information about selected features
            if debug:
                logger.info("Selected features for SHAP plot:")
                for i, feat in enumerate(selected_features):
                    logger.info(f"  {i}. {feat}")

                logger.info("Ordered indices for SHAP plot:")
                for i, idx in enumerate(ordered_indices):
                    if i < len(ordered_indices):
                        logger.info(f"  {i}. {selected_features[idx]} (index {idx})")

            # Plot summary plot with custom order
            plt.figure(figsize=(10, 8))

            # Create a custom summary plot
            if isinstance(shap_values, list):
                # Multi-class case - create custom bar plot
                plt.figure(figsize=(10, 8))
                plt.title('Feature Importance (SHAP values)')

                # Calculate mean absolute SHAP value for each feature
                mean_abs_shap = np.zeros(len(selected_features))
                for class_shap in shap_values:
                    mean_abs_shap += np.abs(class_shap).mean(axis=0)
                mean_abs_shap /= len(shap_values)

                # Sort by custom importance order
                sorted_indices = [i for i in ordered_indices if i < len(mean_abs_shap)]
                sorted_features = [selected_features[i] for i in sorted_indices]
                sorted_values = [mean_abs_shap[i] for i in sorted_indices]

                # Create bar plot - fix shape mismatch issue
                try:
                    plt.figure(figsize=(10, 6))
                    plt.title('Feature Importance (SHAP values)')

                    # Ensure sorted_values is a 1D array
                    if isinstance(sorted_values, list) and len(sorted_values) > 0 and isinstance(sorted_values[0], np.ndarray):
                        # If sorted_values contains arrays, take the first element of each
                        sorted_values_1d = [v[0] if isinstance(v, np.ndarray) and len(v) > 0 else v for v in sorted_values]
                    else:
                        sorted_values_1d = sorted_values

                    plt.bar(range(len(sorted_features)), sorted_values_1d, align='center')
                    plt.xticks(range(len(sorted_features)), sorted_features, rotation=90)
                    plt.ylabel('mean(|SHAP value|)')
                    plt.xlabel('Features')
                    plt.tight_layout()
                    plt.show()
                except Exception as plot_e:
                    logger.warning(f"Failed to create SHAP bar plot: {plot_e}")

                if debug:
                    # Log the feature importance order
                    logger.info("Feature importance ranking:")
                    for i, feat in enumerate(sorted_features):
                        # 处理numpy数组格式化问题
                        value = sorted_values[i]
                        if isinstance(value, np.ndarray):
                            value = float(value)  # 转换为Python标准类型
                        logger.info(f"  {i+1}. {feat}: {value:.6f}")

                # Create dependence plot for top feature
                top_feature_idx = ordered_indices[0] if ordered_indices else 0
                plt.figure(figsize=(10, 6))
                shap.dependence_plot(
                    top_feature_idx,
                    shap_values[0],
                    X_sample,
                    feature_names=selected_features
                )
                plt.title(f'Dependence Plot for Top Feature: {selected_features[top_feature_idx]}')
                plt.tight_layout()
                plt.show()
            else:
                # Binary class case
                plt.figure(figsize=(10, 8))

                # Calculate mean absolute SHAP value for each feature
                mean_abs_shap = np.abs(shap_values).mean(axis=0)

                # Sort by custom importance order
                sorted_indices = [i for i in ordered_indices if i < len(mean_abs_shap)]
                sorted_features = [selected_features[i] for i in sorted_indices]
                sorted_values = [mean_abs_shap[i] for i in sorted_indices]

                # Create bar plot - fix shape mismatch issue
                try:
                    plt.figure(figsize=(10, 6))
                    plt.title('Feature Importance (SHAP values)')

                    # Ensure sorted_values is a 1D array
                    if isinstance(sorted_values, list) and len(sorted_values) > 0 and isinstance(sorted_values[0], np.ndarray):
                        # If sorted_values contains arrays, take the first element of each
                        sorted_values_1d = [v[0] if isinstance(v, np.ndarray) and len(v) > 0 else v for v in sorted_values]
                    else:
                        sorted_values_1d = sorted_values

                    plt.bar(range(len(sorted_features)), sorted_values_1d, align='center')
                    plt.xticks(range(len(sorted_features)), sorted_features, rotation=90)
                    plt.ylabel('mean(|SHAP value|)')
                    plt.xlabel('Features')
                    plt.tight_layout()
                    plt.show()
                except Exception as plot_e:
                    logger.warning(f"Failed to create SHAP bar plot: {plot_e}")

                if debug:
                    # Log the feature importance order
                    logger.info("Feature importance ranking:")
                    for i, feat in enumerate(sorted_features):
                        # 处理numpy数组格式化问题
                        value = sorted_values[i]
                        if isinstance(value, np.ndarray):
                            value = float(value)  # 转换为Python标准类型
                        logger.info(f"  {i+1}. {feat}: {value:.6f}")

                # Create dependence plot for top feature
                top_feature_idx = ordered_indices[0] if ordered_indices else 0
                plt.figure(figsize=(10, 6))
                shap.dependence_plot(
                    top_feature_idx,
                    shap_values,
                    X_sample,
                    feature_names=selected_features
                )
                plt.title(f'Dependence Plot for Top Feature: {selected_features[top_feature_idx]}')
                plt.tight_layout()
                plt.show()

            logger.info("Generated custom SHAP plots based on feature importance ranking")
            return shap_values
        else:
            logger.warning("Importance DataFrame is empty or None, using default SHAP ordering")
            # Use default SHAP plotting
            plt.figure(figsize=(10, 8))
            if isinstance(shap_values, list):
                shap.summary_plot(shap_values, X_sample, feature_names=selected_features, plot_type="bar")
            else:
                shap.summary_plot(shap_values, X_sample, feature_names=selected_features)
            plt.title('Feature Importance (Default SHAP Order)')
            plt.tight_layout()
            plt.show()

            return shap_values

    except Exception as e:
        logger.warning(f"Custom SHAP plot generation failed: {e}")
        import traceback
        logger.warning(traceback.format_exc())
        return None

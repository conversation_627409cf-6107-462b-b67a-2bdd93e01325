import pandas as pd
import numpy as np
import os


def merge_csv_by_depth(main_file_path, reference_file_path, output_path=None, tolerance=0.1):
    """
    根据深度匹配合并两个CSV文件

    Parameters:
    main_file_path: str - 主文件路径
    reference_file_path: str - 参考文件路径
    output_path: str - 输出文件路径（可选）
    tolerance: float - 深度匹配容差

    Returns:
    pd.DataFrame - 合并后的数据
    """

    try:
        # 读取CSV文件
        print("正在读取文件...")
        main_df = pd.read_csv(main_file_path)
        reference_df = pd.read_csv(reference_file_path)

        print(f"主文件形状: {main_df.shape}")
        print(f"参考文件形状: {reference_df.shape}")

        # 获取深度列名（假设第一列是深度）
        main_depth_col = main_df.columns[0]
        ref_depth_col = reference_df.columns[0]

        print(f"主文件深度列: {main_depth_col}")
        print(f"参考文件深度列: {ref_depth_col}")

        # 检查并转换深度列为数值类型
        print("正在处理深度数据类型...")

        # 转换主文件深度列
        main_df[main_depth_col] = pd.to_numeric(main_df[main_depth_col], errors='coerce')
        original_main_count = len(main_df)
        main_df = main_df.dropna(subset=[main_depth_col])
        cleaned_main_count = len(main_df)

        if original_main_count != cleaned_main_count:
            print(f"主文件中有 {original_main_count - cleaned_main_count} 行深度数据无效，已删除")

        # 转换参考文件深度列
        reference_df[ref_depth_col] = pd.to_numeric(reference_df[ref_depth_col], errors='coerce')
        original_ref_count = len(reference_df)
        reference_df = reference_df.dropna(subset=[ref_depth_col])
        cleaned_ref_count = len(reference_df)

        if original_ref_count != cleaned_ref_count:
            print(f"参考文件中有 {original_ref_count - cleaned_ref_count} 行深度数据无效，已删除")

        print(f"清理后主文件形状: {main_df.shape}")
        print(f"清理后参考文件形状: {reference_df.shape}")

        # 显示深度范围
        print(f"主文件深度范围: {main_df[main_depth_col].min():.2f} - {main_df[main_depth_col].max():.2f}")
        print(f"参考文件深度范围: {reference_df[ref_depth_col].min():.2f} - {reference_df[ref_depth_col].max():.2f}")

        # 创建结果DataFrame
        merged_df = main_df.copy()

        # 为参考文件的列准备新列名（避免冲突）
        ref_columns = reference_df.columns.tolist()
        ref_columns.remove(ref_depth_col)  # 移除深度列

        # 处理列名冲突
        new_ref_columns = []
        for col in ref_columns:
            if col in main_df.columns:
                new_col = f"{col}_ref"
            else:
                new_col = col
            new_ref_columns.append(new_col)

        # 初始化新列
        for new_col in new_ref_columns:
            merged_df[new_col] = np.nan

        # 添加匹配信息列
        merged_df['depth_diff'] = np.nan
        merged_df['matched'] = 'No'

        # 进行深度匹配
        print("正在进行深度匹配...")
        match_count = 0

        for idx, main_row in merged_df.iterrows():
            main_depth = main_row[main_depth_col]

            # 跳过无效深度值（虽然前面已经清理过，但保险起见）
            if pd.isna(main_depth):
                continue

            # 计算深度差值
            depth_diffs = np.abs(reference_df[ref_depth_col] - main_depth)

            # 检查是否有有效的差值
            if depth_diffs.empty:
                continue

            # 找到最小差值的索引
            min_diff_idx = depth_diffs.idxmin()
            min_diff = depth_diffs.loc[min_diff_idx]

            # 检查是否在容差范围内
            if min_diff <= tolerance:
                # 匹配成功，复制数据
                ref_row = reference_df.loc[min_diff_idx]

                for orig_col, new_col in zip(ref_columns, new_ref_columns):
                    merged_df.at[idx, new_col] = ref_row[orig_col]

                merged_df.at[idx, 'depth_diff'] = min_diff
                merged_df.at[idx, 'matched'] = 'Yes'
                match_count += 1

        # 输出统计信息
        total_rows = len(merged_df)
        match_rate = (match_count / total_rows) * 100

        print(f"\n匹配统计:")
        print(f"总记录数: {total_rows}")
        print(f"成功匹配: {match_count}")
        print(f"匹配率: {match_rate:.1f}%")
        print(f"深度容差: ±{tolerance}")

        # 显示一些匹配的深度差值统计
        matched_rows = merged_df[merged_df['matched'] == 'Yes']
        if len(matched_rows) > 0:
            print(f"匹配深度差值统计:")
            print(f"  平均差值: {matched_rows['depth_diff'].mean():.4f}")
            print(f"  最大差值: {matched_rows['depth_diff'].max():.4f}")
            print(f"  最小差值: {matched_rows['depth_diff'].min():.4f}")

        # 保存结果
        if output_path:
            merged_df.to_csv(output_path, index=False)
            print(f"\n结果已保存到: {output_path}")

        return merged_df

    except Exception as e:
        print(f"处理过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()  # 打印详细错误信息
        return None


def main():
    # 文件路径
    main_file = r"D:\LQ_2024\MJN_2024\沉积相\Mishrif沉积相\20250525_for_Crossplot_and_class\Mishrif_Facies_original_6class_20250523_POR_PERM_den.csv"
    reference_file = r"D:\LQ_2024\MJN_2024\沉积相\Mishrif沉积相\20250525_for_Crossplot_and_class\MJ-46ST1.csv"
    output_file = r"D:\LQ_2024\MJN_2024\沉积相\Mishrif沉积相\20250525_for_Crossplot_and_class\merged_data.csv"

    # 检查文件是否存在
    if not os.path.exists(main_file):
        print(f"主文件不存在: {main_file}")
        return

    if not os.path.exists(reference_file):
        print(f"参考文件不存在: {reference_file}")
        return

    # 执行合并
    tolerance = 0.1  # 深度匹配容差，可根据需要调整

    merged_data = merge_csv_by_depth(
        main_file_path=main_file,
        reference_file_path=reference_file,
        output_path=output_file,
        tolerance=tolerance
    )

    if merged_data is not None:
        print(f"\n合并完成！结果保存为: {output_file}")

        # 显示前几行数据预览
        print("\n数据预览:")
        print(merged_data.head())

        # 显示列名
        print(f"\n合并后的列名:")
        for i, col in enumerate(merged_data.columns):
            print(f"{i + 1}. {col}")

    else:
        print("合并失败！")


# 如果需要单独调用函数的示例
def example_usage():
    """
    使用示例
    """
    # 自定义参数调用
    main_file = r"D:\LQ_2024\MJN_2024\沉积相\Mishrif沉积相\20250525_for_Crossplot_and_class\Mishrif_Facies_original_6class_20250523_POR_PERM_den2.csv"
    ref_file = r"D:\LQ_2024\MJN_2024\沉积相\Mishrif沉积相\20250525_for_Crossplot_and_class\MJ-46ST1.csv"
    output_file = r"D:\LQ_2024\MJN_2024\沉积相\Mishrif沉积相\20250525_for_Crossplot_and_class\merge_output_file.csv"

    result = merge_csv_by_depth(
        main_file_path=main_file,
        reference_file_path=ref_file,
        output_path=output_file,
        tolerance=0.05  # 更严格的容差
    )

    return result


if __name__ == "__main__":
    main()
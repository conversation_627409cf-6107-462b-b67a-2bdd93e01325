import os
import glob
import pandas as pd
import numpy as np


def extract_depth_intervals(
        input_dir: str,
        well_ranges: dict,
        output_path: str,
        curve_cols: list = None
):
    """
    从 input_dir 中按 well_ranges 定义的深度区间提取曲线数据，
    并将所有结果合并保存到一个 CSV 文件。

    :param input_dir: 存放 CSV 文件的目录
    :param well_ranges: 字典，键为井名，值为深度区间列表 [(min, max), …]
    :param output_path: 输出的 CSV 文件全路径
    :param curve_cols: 要提取的曲线列（不含 DEPTH），如果为 None，则取文件中的 DEN_Hartha, DT_Hartha, GR_Hartha, NEU_Hartha
    """
    if not os.path.isdir(input_dir):
        raise FileNotFoundError(f"输入目录不存在: {input_dir}")

    # 默认提取的曲线列（注意修改为符合新数据格式的列名，包含前导空格）
    if curve_cols is None:
        curve_cols = [' GR_Hartha', ' DEN_Hartha', ' NEU_Hartha', ' DT_Hartha', ' PERM_FINAL0425_Hartha',
                      ' PHIE_Hartha']

    # 用于存储所有提取的数据
    all_extracted_data = []

    # 跟踪已处理的井和深度区间
    processed_intervals = set()

    for well, intervals in well_ranges.items():
        print(f"处理井 {well}...")

        # 查找对应的CSV文件
        csv_file = os.path.join(input_dir, f"{well}.csv")
        if not os.path.exists(csv_file):
            print(f"⚠️ 未找到井 {well} 对应的 CSV 文件: {csv_file}")
            continue

        print(f"  读取文件: {os.path.basename(csv_file)}")

        try:
            # 读取CSV文件，先读取前几行以检查格式
            header_df = pd.read_csv(csv_file, nrows=2)

            # 检查是否有单位行
            has_units_row = False
            for col in header_df.columns:
                if 'm' in str(header_df.iloc[0, header_df.columns.get_loc(col)]):
                    has_units_row = True
                    break

            # 根据是否有单位行决定跳过的行数
            if has_units_row:
                df = pd.read_csv(csv_file, skiprows=[1])
            else:
                df = pd.read_csv(csv_file)

        except Exception as e:
            print(f"⚠️ 读取文件 {os.path.basename(csv_file)} 失败: {e}")
            continue

        # 检查深度列是否存在
        depth_col = 'DEPTH'
        if depth_col not in df.columns:
            # 尝试寻找替代的深度列名
            potential_depth_cols = ['DEPTH', 'Depth', 'depth', 'MD', 'TVD']
            for col in potential_depth_cols:
                if col in df.columns:
                    depth_col = col
                    print(f"使用 {depth_col} 作为深度列")
                    break
            else:
                print(f"⚠️ 文件 {os.path.basename(csv_file)} 中缺少深度列")
                continue

        # 确定要提取的曲线列（处理列名中的空格问题）
        # 首先打印所有列名，方便调试
        print(f"  文件中的实际列名: {df.columns.tolist()}")

        # 检查并匹配列名，支持带空格和不带空格的情况
        available_cols = []
        missing_cols = []

        for curve in curve_cols:
            # 尝试精确匹配
            if curve in df.columns:
                available_cols.append(curve)
            else:
                # 尝试去除空格后匹配
                curve_no_space = curve.strip()
                space_variants = [f" {curve_no_space}", f"  {curve_no_space}", curve_no_space]

                found = False
                for variant in space_variants:
                    if variant in df.columns:
                        available_cols.append(variant)
                        found = True
                        break

                if not found:
                    missing_cols.append(curve)

        if missing_cols:
            print(f"⚠️ 文件中缺少以下曲线列: {', '.join(missing_cols)}")

        if not available_cols:
            print(f"⚠️ 文件 {os.path.basename(csv_file)} 中没有找到任何指定的曲线列")
            print(f"  文件中的列: {', '.join(df.columns)}")
            continue

        cols = [depth_col] + available_cols
        print(f"  提取列: {', '.join(cols)}")

        # 处理每个深度区间
        for dmin, dmax in intervals:
            if dmin >= dmax:
                print(f"⚠️ 井 {well} 的深度区间 ({dmin}, {dmax}) 无效：最小值大于或等于最大值")
                continue

            # 创建唯一标识符，确保不重复处理同一井的同一深度区间
            interval_id = f"{well}_{dmin:.1f}_{dmax:.1f}"
            if interval_id in processed_intervals:
                print(f"⚠️ 井 {well} 的深度区间 {dmin:.1f}-{dmax:.1f} 已处理过，跳过")
                continue

            processed_intervals.add(interval_id)

            # 提取该深度区间的数据
            mask = (df[depth_col] >= dmin) & (df[depth_col] <= dmax)
            sub_df = df.loc[mask, cols].copy()

            if sub_df.empty:
                print(f"⚠️ 井 {well} 在深度区间 {dmin:.1f}-{dmax:.1f} 没有数据")
                continue

            # 替换缺失值标记 -999.25 为 NaN
            sub_df = sub_df.replace(-999.25, np.nan)

            # 添加井名和深度区间信息
            sub_df['WELL'] = well
            sub_df['DEPTH_INTERVAL'] = f"{dmin:.1f}-{dmax:.1f}"

            # 检查是否有重复的深度值
            if sub_df[depth_col].duplicated().any():
                print(f"⚠️ 井 {well} 在深度区间 {dmin:.1f}-{dmax:.1f} 有重复的深度值")
                # 打印重复的深度值
                duplicated_depths = sub_df[sub_df[depth_col].duplicated(keep=False)][depth_col].unique()
                print(f"   重复的深度值: {', '.join(map(str, duplicated_depths))}")

                # 保留第一次出现的深度值
                sub_df = sub_df.drop_duplicates(subset=[depth_col], keep='first')
                print(f"   已保留每个深度的第一次出现")

            # 将数据添加到结果列表
            all_extracted_data.append(sub_df)
            print(f"  ✓ 提取了 {len(sub_df)} 行数据，深度区间: {dmin:.1f}-{dmax:.1f}")

    if not all_extracted_data:
        print("⚠️ 未提取到任何数据。请检查井名、深度区间或文件路径。")
        return

    # 合并所有提取的数据
    combined_df = pd.concat(all_extracted_data, ignore_index=True)

    # 重新排列列顺序，将WELL和DEPTH_INTERVAL放在前面
    cols = combined_df.columns.tolist()
    new_order = ['WELL', 'DEPTH_INTERVAL'] + [c for c in cols if c not in ['WELL', 'DEPTH_INTERVAL']]
    combined_df = combined_df[new_order]

    # 保存到CSV文件
    combined_df.to_csv(output_path, index=False)

    print(f"✅ 提取完成，结果已保存到：\n{output_path}")
    print(f"共提取了 {len(combined_df)} 行数据，来自 {len(all_extracted_data)} 个深度区间")


if __name__ == "__main__":
    # ——————— 配置区域 ———————
    dir_path = r"D:\LQ_2024\MJN_2024\沉积相\Hartha沉积相\0511_6curves"
    output_file = os.path.join(dir_path, "extracted_hartha_curves.csv")

    well_ranges = {
        'MJ-10': [(2085, 2099)],
        'MJ-112': [(2170, 2214.5)],
        'MJ-86': [(2204, 2236)],
        'MJ-54': [(2144.5, 2175.5)],
        'MJ-113': [(2433.5, 2467.5)],
        'MJ-6': [(2236, 2271)],
        'MJ-12': [(2209, 2253)],
        'MJ-03ST1': [(2221, 2247.5), (2214.5, 2259.5)],
        'MJ-68': [(2312.5, 2352.5)],
        'MJ-103': [(2463, 2498)],
    }

    # 指定要提取的曲线列（不含DEPTH，包含前导空格）
    curve_columns = [' GR_Hartha', ' DEN_Hartha', ' NEU_Hartha', ' DT_Hartha', ' PERM_FINAL0425_Hartha', ' PHIE_Hartha']

    extract_depth_intervals(
        input_dir=dir_path,
        well_ranges=well_ranges,
        output_path=output_file,
        curve_cols=curve_columns
    )
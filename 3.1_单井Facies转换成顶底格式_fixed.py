import os
import pandas as pd
import glob

# 指定包含PRN文件的目录路径
directory_path = r"D:\LQ_2024\MJN_2024\沉积相\Mishrif沉积相\Mishrif_Ahamadi_0516_4curves\Mishrif_Facies_optimized_results_250516"
# 设置最小层厚度阈值（米）
MIN_THICKNESS = 2.0

def merge_thin_layers(layers_df, min_thickness):
    """
    合并薄层，优先考虑值的相似性和地质连续性
    
    参数:
    layers_df -- 包含层信息的DataFrame
    min_thickness -- 最小层厚度（米）
    
    返回:
    合并后的DataFrame
    """
    print(f"开始合并薄层 - 最小厚度:{min_thickness}米")

    # 标记薄层
    thin_layers = layers_df[layers_df['Thickness'] < min_thickness]
    print(f"总层数: {len(layers_df)}, 薄层数: {len(thin_layers)}")

    if len(thin_layers) == 0:
        print("没有需要合并的薄层")
        return layers_df

    # 按深度排序
    result_df = layers_df.sort_values('Top').reset_index(drop=True)

    # 迭代处理每个薄层
    i = 0
    while i < len(result_df):
        # 如果当前层不是薄层，则跳到下一层
        if result_df.iloc[i]['Thickness'] >= min_thickness:
            i += 1
            continue

        # 如果是第一层或最后一层，特殊处理
        if i == 0:
            # 第一层是薄层，与下一层合并
            if i + 1 < len(result_df):
                result_df.at[i + 1, 'Top'] = result_df.iloc[i]['Top']
                result_df.at[i + 1, 'Thickness'] = result_df.iloc[i + 1]['Bottom'] - result_df.iloc[i]['Top']
                result_df = result_df.drop(i).reset_index(drop=True)
            else:
                # 只有一层，保留
                i += 1
        elif i == len(result_df) - 1:
            # 最后一层是薄层，与前一层合并
            result_df.at[i - 1, 'Bottom'] = result_df.iloc[i]['Bottom']
            result_df.at[i - 1, 'Thickness'] = result_df.iloc[i]['Bottom'] - result_df.iloc[i - 1]['Top']
            result_df = result_df.drop(i).reset_index(drop=True)
        else:
            # 中间层是薄层，决定与前层还是后层合并
            prev_value = result_df.iloc[i - 1]['Facies']
            curr_value = result_df.iloc[i]['Facies']
            next_value = result_df.iloc[i + 1]['Facies']

            # 计算值差异
            prev_diff = abs(curr_value - prev_value)
            next_diff = abs(curr_value - next_value)

            # 获取前后层厚度
            prev_thickness = result_df.iloc[i - 1]['Thickness']
            next_thickness = result_df.iloc[i + 1]['Thickness']

            # 决定合并方向
            # 1. 如果薄层的值与前后层中的一个相同，则合并到值相同的层
            # 2. 如果都不同，则合并到值差异较小的层
            # 3. 如果值差异相同，则合并到较厚的层
            if curr_value == prev_value:
                merge_with_prev = True
            elif curr_value == next_value:
                merge_with_prev = False
            elif prev_diff < next_diff:
                merge_with_prev = True
            elif next_diff < prev_diff:
                merge_with_prev = False
            else:
                # 值差异相同，合并到较厚的层
                merge_with_prev = prev_thickness >= next_thickness

            # 执行合并
            if merge_with_prev:
                # 与前一层合并
                result_df.at[i - 1, 'Bottom'] = result_df.iloc[i]['Bottom']
                result_df.at[i - 1, 'Thickness'] = result_df.iloc[i]['Bottom'] - result_df.iloc[i - 1]['Top']
                result_df = result_df.drop(i).reset_index(drop=True)
            else:
                # 与后一层合并
                result_df.at[i + 1, 'Top'] = result_df.iloc[i]['Top']
                result_df.at[i + 1, 'Thickness'] = result_df.iloc[i + 1]['Bottom'] - result_df.iloc[i]['Top']
                result_df = result_df.drop(i).reset_index(drop=True)

    print(f"薄层处理完成: 合并后层数={len(result_df)}")
    return result_df

# 获取目录中的所有PRN文件
prn_files = glob.glob(os.path.join(directory_path, "*.prn"))

if not prn_files:
    print(f"在指定目录下未找到PRN文件: {directory_path}")
else:
    print(f"找到{len(prn_files)}个PRN文件待处理。")
    print(f"将合并厚度小于{MIN_THICKNESS}米的薄层")

    # 创建输出目录
    output_dir = os.path.join(directory_path, "合并结果")
    os.makedirs(output_dir, exist_ok=True)

    # 处理每个PRN文件
    for prn_file in prn_files:
        try:
            # 从文件名获取井名
            well_name = os.path.basename(prn_file).replace('.prn', '')

            print(f"处理文件: {well_name}")

            # 读取PRN文件
            # PRN文件通常是空格或制表符分隔的文本文件
            try:
                # 尝试不同的分隔符
                df = pd.read_csv(prn_file, sep='\s+', header=None, names=['Depth', 'Facies'])
            except:
                try:
                    df = pd.read_csv(prn_file, sep='\t', header=None, names=['Depth', 'Facies'])
                except:
                    print(f"无法读取文件 {prn_file}，尝试手动检查文件格式")
                    continue

            # 确保数据按深度排序
            df = df.sort_values(by='Depth')

            # 创建新的数据框架用于存储结果
            result = []

            # 初始化第一个区间的顶部深度
            current_top = df.iloc[0]['Depth']
            current_facies = df.iloc[0]['Facies']

            # 处理每一行数据，寻找facies变化点
            for i in range(1, len(df)):
                depth = df.iloc[i]['Depth']
                facies = df.iloc[i]['Facies']

                # 如果facies值改变
                if facies != current_facies:
                    # 添加当前区间到结果
                    result.append({
                        'Top': current_top,
                        'Bottom': depth,
                        'Facies': current_facies,
                        'Thickness': depth - current_top  # 添加厚度计算
                    })

                    # 更新当前区间的顶部深度和facies
                    current_top = depth
                    current_facies = facies

            # 添加最后一个区间
            last_interval = df.iloc[1]['Depth'] - df.iloc[0]['Depth']  # 计算采样间隔
            result.append({
                'Top': current_top,
                'Bottom': df.iloc[-1]['Depth'] + last_interval,  # 估计最后一个点的底部深度
                'Facies': current_facies,
                'Thickness': df.iloc[-1]['Depth'] + last_interval - current_top  # 添加厚度计算
            })

            # 转换结果为DataFrame
            result_df = pd.DataFrame(result)

            # 合并薄层
            merged_df = merge_thin_layers(result_df, MIN_THICKNESS)

            # 保存结果到PRN文件
            output_file = os.path.join(output_dir, f"{well_name}.prn")
            # 按PRN格式保存（空格分隔）
            with open(output_file, 'w') as f:
                for _, row in merged_df.iterrows():
                    f.write(f"{row['Top']:.2f}\t{row['Bottom']:.2f}\t{row['Facies']:.2f}\n")

            print(f"完成! 结果已保存到 {output_file}")
            print(f"原始层数: {len(result_df)}, 合并后层数: {len(merged_df)}")

        except Exception as e:
            print(f"处理文件 {prn_file} 时出错: {str(e)}")

    print("\n所有文件处理完成!")
    print(f"转换后的文件保存在: {output_dir}")

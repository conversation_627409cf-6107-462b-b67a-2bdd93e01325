import os

# 先创建备份
if not os.path.exists('geo_contours.prn.bak'):
    import shutil
    shutil.copy2('geo_contours.prn', 'geo_contours.prn.bak')

with open('geo_contours.prn', 'r', encoding='utf-8') as f:
    lines = f.readlines()
    
    # 如果第一行包含BOM，去掉它
    if lines and lines[0].startswith('\ufeff'):
        lines[0] = lines[0].replace('\ufeff', '')

# 找到第一个数据行的索引
data_start = 0
for i, line in enumerate(lines):
    if line.strip() and not line.strip().startswith(('#', '//')):
        # 检查该行是否包含4个数值
        parts = line.strip().split()
        if len(parts) == 4 and all(part.replace('.', '').isdigit() for part in parts):
            data_start = i
            break

# 只保留数据行，并确保格式正确
formatted_lines = []
for line in lines[data_start:]:
    parts = [x for x in line.strip().split() if x]
    if len(parts) == 4:  # 确保有4列数据        # 使用固定宽度格式化：只调整前两列的格式，保持后两列原值不变
        x = float(parts[0])
        y = float(parts[1])
        z = parts[2]  # 保持原值
        w = parts[3]  # 保持原值
        formatted_line = f"{' ' * 7}{x:12.2f}{' ' * 6}{y:12.2f}{' ' * 15}{z}{' ' * 15}{w}\n"
        formatted_lines.append(formatted_line)

# 写回文件时使用不带BOM的UTF-8编码
with open('geo_contours.prn', 'w', encoding='utf-8', newline='\n') as f:
    # 确保第一行没有多余字符
    if formatted_lines:
        formatted_lines[0] = formatted_lines[0].lstrip('\ufeff')
    f.writelines(formatted_lines)

print("文件处理完成！")

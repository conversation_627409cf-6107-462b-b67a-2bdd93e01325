import os
import pandas as pd
import glob

# 指定包含CSV文件的目录路径
directory_path = r"D:\LQ_2024\MJN_2024\沉积相\Mishrif沉积相\Mishrif_Ahamadi_0516_4curves\EODfacies_top_bot_0519\EODfacies_top_bot_0519"
# 设置最小层厚度阈值（米）
MIN_THICKNESS = 2.0

def merge_thin_layers(layers_df, min_thickness):
    """
    合并薄层，优先考虑值的相似性和地质连续性

    参数:
    layers_df -- 包含层信息的DataFrame
    min_thickness -- 最小层厚度（米）

    返回:
    合并后的DataFrame
    """
    print(f"开始合并薄层 - 最小厚度:{min_thickness}米")

    # 标记薄层
    thin_layers = layers_df[layers_df['Thickness'] < min_thickness]
    print(f"总层数: {len(layers_df)}, 薄层数: {len(thin_layers)}")

    if len(thin_layers) == 0:
        print("没有需要合并的薄层")
        return layers_df

    # 按深度排序
    result_df = layers_df.sort_values('Top').reset_index(drop=True)

    # 迭代处理每个薄层
    i = 0
    while i < len(result_df):
        # 如果当前层不是薄层，则跳到下一层
        if result_df.iloc[i]['Thickness'] >= min_thickness:
            i += 1
            continue

        # 如果是第一层或最后一层，特殊处理
        if i == 0:
            # 第一层是薄层，与下一层合并
            if i + 1 < len(result_df):
                result_df.at[i + 1, 'Top'] = result_df.iloc[i]['Top']
                result_df.at[i + 1, 'Thickness'] = result_df.iloc[i + 1]['Bottom'] - result_df.iloc[i]['Top']
                result_df = result_df.drop(i).reset_index(drop=True)
            else:
                # 只有一层，保留
                i += 1
        elif i == len(result_df) - 1:
            # 最后一层是薄层，与前一层合并
            result_df.at[i - 1, 'Bottom'] = result_df.iloc[i]['Bottom']
            result_df.at[i - 1, 'Thickness'] = result_df.iloc[i]['Bottom'] - result_df.iloc[i - 1]['Top']
            result_df = result_df.drop(i).reset_index(drop=True)
        else:
            # 中间层是薄层，决定与前层还是后层合并
            prev_value = result_df.iloc[i - 1]['Facies']
            curr_value = result_df.iloc[i]['Facies']
            next_value = result_df.iloc[i + 1]['Facies']

            # 计算值差异
            prev_diff = abs(curr_value - prev_value)
            next_diff = abs(curr_value - next_value)

            # 获取前后层厚度
            prev_thickness = result_df.iloc[i - 1]['Thickness']
            next_thickness = result_df.iloc[i + 1]['Thickness']

            # 决定合并方向
            # 1. 如果薄层的值与前后层中的一个相同，则合并到值相同的层
            # 2. 如果都不同，则合并到值差异较小的层
            # 3. 如果值差异相同，则合并到较厚的层
            if curr_value == prev_value:
                merge_with_prev = True
            elif curr_value == next_value:
                merge_with_prev = False
            elif prev_diff < next_diff:
                merge_with_prev = True
            elif next_diff < prev_diff:
                merge_with_prev = False
            else:
                # 值差异相同，合并到较厚的层
                merge_with_prev = prev_thickness >= next_thickness

            # 执行合并
            if merge_with_prev:
                # 与前一层合并
                result_df.at[i - 1, 'Bottom'] = result_df.iloc[i]['Bottom']
                result_df.at[i - 1, 'Thickness'] = result_df.iloc[i]['Bottom'] - result_df.iloc[i - 1]['Top']
                result_df = result_df.drop(i).reset_index(drop=True)
            else:
                # 与后一层合并
                result_df.at[i + 1, 'Top'] = result_df.iloc[i]['Top']
                result_df.at[i + 1, 'Thickness'] = result_df.iloc[i + 1]['Bottom'] - result_df.iloc[i]['Top']
                result_df = result_df.drop(i).reset_index(drop=True)

    print(f"薄层处理完成: 合并后层数={len(result_df)}")
    return result_df



# 获取目录中的所有CSV文件
csv_files = glob.glob(os.path.join(directory_path, "*.csv"))

if not csv_files:
    print(f"在指定目录下未找到CSV文件: {directory_path}")
else:
    print(f"找到{len(csv_files)}个CSV文件待处理。")
    print(f"将合并厚度小于{MIN_THICKNESS}米的薄层")

    # 创建输出目录
    output_dir = os.path.join(directory_path, "合并结果")
    os.makedirs(output_dir, exist_ok=True)

    # 处理每个CSV文件
    for csv_file in csv_files:
        try:
            # 从文件名获取井名
            well_name = os.path.basename(csv_file).replace('.csv', '')

            print(f"处理文件: {well_name}")

            # 读取CSV文件，先尝试读取原始内容以进行调试
            try:
                with open(csv_file, 'r') as f:
                    first_lines = [next(f) for _ in range(5)]  # 读取前5行用于调试
                print(f"文件 {well_name} 的前几行内容:")
                for i, line in enumerate(first_lines):
                    print(f"  行 {i+1}: {line.strip()}")
            except Exception as e:
                print(f"读取文件 {csv_file} 原始内容时出错: {str(e)}")

            # 尝试自定义读取方式处理特殊格式
            try:
                # 读取文件内容，跳过前两行
                with open(csv_file, 'r') as f:
                    lines = f.readlines()[2:]  # 跳过前两行

                # 解析每一行
                depths = []
                facies = []
                for line in lines:
                    # 处理格式如 "2509.480000000000, 4"
                    parts = line.strip().split(',')
                    if len(parts) >= 2:
                        try:
                            depth = float(parts[0].strip())
                            facies_val = int(parts[1].strip())
                            depths.append(depth)
                            facies.append(facies_val)
                        except (ValueError, IndexError) as e:
                            print(f"  跳过无效行: {line.strip()} - {str(e)}")

                # 创建DataFrame
                df = pd.DataFrame({'Depth': depths, 'Facies': facies})
                print(f"成功读取文件 {well_name}，读取到 {len(df)} 行数据")
            except Exception as e:
                print(f"读取文件 {csv_file} 时出错: {str(e)}")
                continue

            # 显示读取后的数据前几行
            if len(df) > 0:
                print(f"读取的数据前3行:")
                print(df.head(3))
            else:
                print(f"警告: 文件 {csv_file} 没有有效数据，跳过处理")
                continue

            # 确保数据按深度排序
            df = df.sort_values(by='Depth')

            # 创建新的数据框架用于存储结果
            result = []

            # 检查DataFrame是否为空
            if len(df) == 0:
                print(f"警告: 文件 {csv_file} 没有有效数据，跳过处理")
                continue

            # 初始化第一个区间的顶部深度
            current_top = df.iloc[0]['Depth']
            current_facies = df.iloc[0]['Facies']

            # 处理每一行数据，寻找facies变化点
            for i in range(1, len(df)):
                depth = df.iloc[i]['Depth']
                facies = df.iloc[i]['Facies']

                # 如果facies值改变
                if facies != current_facies:
                    # 添加当前区间到结果
                    result.append({
                        'Top': float(current_top),
                        'Bottom': float(depth),
                        'Facies': int(current_facies),
                        'Thickness': float(depth) - float(current_top)  # 添加厚度计算
                    })

                    # 更新当前区间的顶部深度和facies
                    current_top = depth
                    current_facies = facies

            # 添加最后一个区间，但首先检查DataFrame是否有足够的行
            if len(df) >= 2:  # 确保至少有两行数据来计算间隔
                # 确保使用浮点数进行计算
                last_interval = float(df.iloc[1]['Depth']) - float(df.iloc[0]['Depth'])  # 计算采样间隔
                last_depth = float(df.iloc[-1]['Depth'])
                bottom_depth = last_depth + last_interval  # 估计最后一个点的底部深度

                result.append({
                    'Top': float(current_top),
                    'Bottom': bottom_depth,
                    'Facies': int(current_facies),
                    'Thickness': bottom_depth - float(current_top)  # 添加厚度计算
                })
            elif len(df) == 1:  # 如果只有一行数据
                # 使用默认间隔（例如0.1）
                default_interval = 0.1
                last_depth = float(df.iloc[0]['Depth'])
                bottom_depth = last_depth + default_interval

                result.append({
                    'Top': float(current_top),
                    'Bottom': bottom_depth,
                    'Facies': int(current_facies),
                    'Thickness': bottom_depth - float(current_top)
                })
            # 如果DataFrame为空，则不添加任何区间

            # 转换结果为DataFrame
            result_df = pd.DataFrame(result)

            # 检查结果是否为空
            if len(result_df) == 0:
                print(f"警告: 文件 {csv_file} 处理后没有有效区间，跳过合并和保存")
                continue

            # 合并薄层
            merged_df = merge_thin_layers(result_df, MIN_THICKNESS)

            # 保存结果到PRN文件
            output_file = os.path.join(output_dir, f"{well_name}.prn")
            # 按PRN格式保存（空格分隔）
            with open(output_file, 'w') as f:
                for _, row in merged_df.iterrows():
                    f.write(f"{row['Top']:.2f}\t{row['Bottom']:.2f}\t{row['Facies']:.2f}\n")

            print(f"完成! 结果已保存到 {output_file}")
            print(f"原始层数: {len(result_df)}, 合并后层数: {len(merged_df)}")

        except Exception as e:
            print(f"处理文件 {csv_file} 时出错: {str(e)}")

    print("\n所有文件处理完成!")
    print(f"转换后的文件保存在: {output_dir}")


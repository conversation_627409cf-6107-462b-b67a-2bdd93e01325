import pandas as pd

def load_and_prepare_prn(
    path: str,
    porosity_column: str = None,
    facies_map: dict = None
) -> pd.DataFrame:
    """
    Load a .prn well log file and prepare DataFrame for facies modeling.

    Parameters
    ----------
    path : str
        File path to the .prn file, whitespace-delimited, first row header, columns: DEPT SP RHOB DT GR Corefacies
    porosity_column : str, optional
        Name of a porosity column in the file; if None, SP is used as placeholder for porosity.
    facies_map : dict, optional
        Mapping from string labels in Corefacies to integer codes. If None,
        labels will be auto-encoded.

    Returns
    -------
    df_prepared : pd.DataFrame
        DataFrame with columns ['DEPTH','GR','RHOB','AC','PHIT','Facies'] ready for feature extraction.
    """
    # 1. Load raw .prn file, skip header row
    df = pd.read_csv(
        path,
        sep=r"\s+",
        header=0,
        names=['DEPTH', 'SP', 'RHOB', 'AC', 'GR', 'Facies'],
        skiprows=[1]  # skip automatic header if duplicated
    )

    # 2. Rename any remaining columns
    # Already named SP->PHIT fallback below

    # 3. Porosity fallback
    if porosity_column and porosity_column in df.columns:
        df['PHIT'] = df[porosity_column]
    else:
        df['PHIT'] = df['SP']

    # 4. Map facies labels to integers (manual or auto)
    if facies_map is not None:
        df['Facies'] = df['Facies'].map(facies_map)
    else:
        df['Facies'], uniques = pd.factorize(df['Facies'])
        print("Auto-detected facies categories:", list(uniques))

    # 5. Drop rows with missing critical values
    df_prepared = df.dropna(subset=['DEPTH', 'GR', 'RHOB', 'AC', 'PHIT', 'Facies']).copy()

    # 6. Convert dtypes in one go
    df_prepared = df_prepared.astype({
        'DEPTH': float,
        'GR':    float,
        'RHOB':  float,
        'AC':    float,
        'PHIT':  float,
        'Facies': int
    })

    return df_prepared


if __name__ == '__main__':
    # 示例用法：自动编码全部类别
    path = r'D:\LQ_2024\MJN_2024\沉积相\MJ-46ST1-facies-afl.prn'
    df_ready = load_and_prepare_prn(path)
    print("Data prepared, showing top 5 rows:")
    print(df_ready.head())

    # 7. Save prepared data to CSV
    output_path = path.replace('.prn', '_prepared.csv')
    df_ready.to_csv(output_path, index=False)
    print(f"Prepared data saved to: {output_path}")

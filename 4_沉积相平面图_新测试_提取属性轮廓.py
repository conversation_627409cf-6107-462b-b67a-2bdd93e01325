import cv2
import numpy as np
import matplotlib.pyplot as plt
from scipy import ndimage
import os
import time
import traceback
import sys
import argparse
import logging


def smooth_contour(contour, sigma=1.0):
    """
    平滑轮廓点

    参数:
    - contour: 输入轮廓
    - sigma: 高斯滤波的平滑程度

    返回:
    - 平滑后的轮廓
    """
    # 转换轮廓点为数组
    points = contour.reshape(-1, 2)

    # 确保轮廓闭合
    if not np.array_equal(points[0], points[-1]):
        points = np.vstack([points, points[0]])

    # 对x和y坐标分别进行高斯平滑
    x = ndimage.gaussian_filter1d(points[:, 0], sigma)
    y = ndimage.gaussian_filter1d(points[:, 1], sigma)

    # 重新组织为轮廓格式
    smoothed = np.column_stack([x, y]).reshape(-1, 1, 2).astype(np.int32)

    return smoothed


def analyze_contour(contour):
    """
    分析轮廓的特征
    返回: (dict) 轮廓特征信息
    """
    # 计算基本特征
    area = cv2.contourArea(contour)
    perimeter = cv2.arcLength(contour, True)

    # 计算形状特征
    circularity = 4 * np.pi * area / (perimeter * perimeter) if perimeter > 0 else 0

    # 计算最小外接矩形
    rect = cv2.minAreaRect(contour)
    box = cv2.boxPoints(rect)
    box = np.int0(box)
    rect_area = cv2.contourArea(box)

    # 计算填充率
    solidity = area / rect_area if rect_area > 0 else 0

    # 计算主轴方向
    if len(contour) >= 5:
        (x, y), (MA, ma), angle = cv2.fitEllipse(contour)
    else:
        MA, ma, angle = 0, 0, 0

    return {
        'area': area,
        'perimeter': perimeter,
        'circularity': circularity,
        'solidity': solidity,
        'aspect_ratio': MA / ma if ma > 0 else 0,
        'orientation': angle,
        'points': len(contour)
    }


def interpolate_contour(contour, target_points):
    """
    使用样条插值增加轮廓点数
    """
    # 转换为点列表
    points = contour.reshape(-1, 2)

    # 确保轮廓闭合
    if not np.array_equal(points[0], points[-1]):
        points = np.vstack([points, points[0]])

    # 创建参数化的点序列
    t = np.linspace(0, 1, len(points))
    t_new = np.linspace(0, 1, target_points)

    # 对x和y坐标分别进行插值
    x_interp = np.interp(t_new, t, points[:, 0])
    y_interp = np.interp(t_new, t, points[:, 1])

    # 重新组织为轮廓格式
    new_contour = np.column_stack([x_interp, y_interp]).reshape(-1, 1, 2).astype(np.int32)

    return new_contour


def optimize_contour(contour, min_points=10):
    """
    优化轮廓形状
    """
    # 分析当前轮廓
    features = analyze_contour(contour)
    logger.info(f"优化前轮廓特征: {features}")

    # 如果轮廓太简单，增加点数
    if features['points'] < min_points and features['area'] > 1000:
        # 使用样条插值增加点数
        contour = interpolate_contour(contour, min_points)

    # 如果轮廓太复杂，简化它
    elif features['points'] > 100:
        epsilon = 0.001 * features['perimeter']
        contour = cv2.approxPolyDP(contour, epsilon, True)

    # 平滑轮廓
    if len(contour) >= 3:
        contour = smooth_contour(contour)

    # 检查优化结果
    new_features = analyze_contour(contour)
    logger.info(f"优化后轮廓特征: {new_features}")

    return contour


# 配置日志记录
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(os.path.join(os.path.dirname(__file__), 'contour_extraction.log'),
                            encoding='utf-8',
                            mode='a')
    ]
)
logger = logging.getLogger(__name__)

# 记录程序启动信息
logger.info("程序启动 - 轮廓提取工具")


def extract_contours(image_path, color_range, output_dir=None, min_contour_area=500,
                     spline_smoothness=3, gaussian_sigma=3, is_contour_image=None):
    """
    从图像中提取指定颜色范围的轮廓

    参数:
    - image_path: 输入图像路径
    - color_range: 要提取的颜色范围，格式为[(lower_hsv), (upper_hsv)]
    - output_dir: 输出目录，如果为None则不保存结果
    - min_contour_area: 最小轮廓面积，小于此面积的轮廓将被忽略
    - spline_smoothness: 样条曲线平滑度，值越大曲线越平滑
    - gaussian_sigma: 高斯滤波的sigma值，值越大曲线越平滑
    - is_contour_image: 是否将输入图像视为黑白轮廓图像，如果为None则自动检测

    返回:
    - 原始图像
    - 掩码图像
    - 轮廓图像
    - 简化后的轮廓列表
    """
    # 读取图像
    print(f"读取图像: {image_path}")
    image = cv2.imread(image_path)
    if image is None:
        raise ValueError(f"无法读取图像: {image_path}")

    # 转换为RGB以便显示
    image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

    # 检查图像是否为黑白轮廓图
    is_contour_image = False
    # 如果图像路径包含"轮廓"字样，假设它是黑白轮廓图
    if "轮廓" in image_path or "contour" in image_path.lower():
        is_contour_image = True
        print("检测到黑白轮廓图像，使用特殊处理...")

    # 如果提供了is_contour_image参数，使用该参数覆盖自动检测结果
    if 'is_contour_image' in locals() and is_contour_image is not None:
        is_contour_image = is_contour_image
        if is_contour_image:
            print("手动指定为黑白轮廓图像处理模式")

    if is_contour_image:
        # 对于黑白轮廓图，直接转换为灰度图
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        # 二值化 - 使用THRESH_BINARY而不是THRESH_BINARY_INV，因为我们要提取黑色轮廓
        _, mask = cv2.threshold(gray, 127, 255, cv2.THRESH_BINARY)
        # 显示一些调试信息
        print(f"黑白轮廓图像处理 - 掩码中白色像素数量: {np.sum(mask > 0)}")
        print(f"黑白轮廓图像处理 - 掩码中黑色像素数量: {np.sum(mask == 0)}")

        # 如果掩码几乎全黑或全白，可能是阈值设置有问题，尝试反转
        if np.sum(mask > 0) < 100 or np.sum(mask == 0) < 100:
            print("警告: 掩码几乎全黑或全白，尝试反转...")
            mask = 255 - mask
    else:
        # 对于彩色图像，使用HSV颜色空间提取特定颜色
        # 转换为HSV颜色空间
        image_hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)

        # 创建颜色掩码
        lower_color, upper_color = color_range
        mask = cv2.inRange(image_hsv, lower_color, upper_color)

    # 应用形态学操作来平滑掩码，但保留河道细节

    # 更强的预处理来减少碎片

    # 先进行自适应中值滤波，根据图像特征调整大小
    initial_median_size = 3
    noise_threshold = np.std(mask) * 2
    if noise_threshold > 30:
        median_size = 5  # 如果噪声较大，使用更大的核
    else:
        median_size = initial_median_size
    logger.info(f"使用中值滤波核大小: {median_size} (噪声阈值: {noise_threshold:.1f})")
    mask_median = cv2.medianBlur(mask, median_size)

    # 自适应形态学操作
    # 计算平均目标大小来决定核的大小
    nonzero = np.nonzero(mask_median)
    if len(nonzero[0]) > 0:
        y_spread = np.max(nonzero[0]) - np.min(nonzero[0])
        x_spread = np.max(nonzero[1]) - np.min(nonzero[1])
        avg_size = (x_spread + y_spread) / 2
        kernel_size = max(3, min(7, int(avg_size * 0.01)))  # 在3到7之间自适应
    else:
        kernel_size = 5

    logger.info(f"使用形态学核大小: {kernel_size}")
    close_kernel = np.ones((kernel_size, kernel_size), np.uint8)
    mask_closed = cv2.morphologyEx(mask_median, cv2.MORPH_CLOSE, close_kernel)

    # 应用开运算移除小的噪点
    open_kernel = np.ones((3, 3), np.uint8)  # 使用最小的核大小，保留更多细节
    mask_opened = cv2.morphologyEx(mask_closed, cv2.MORPH_OPEN, open_kernel)

    # 应用高斯模糊平滑边缘，使用较小的核和sigma值
    blur_size = 3  # 减小模糊核大小
    sigma = 0.8  # 减小sigma值
    mask_blurred = cv2.GaussianBlur(mask_opened, (blur_size, blur_size), sigma)

    # 使用普通阈值处理
    _, mask_final = cv2.threshold(mask_blurred, 127, 255, cv2.THRESH_BINARY)

    # 应用形态学梯度操作，获取边缘
    # gradient_kernel = np.ones((3, 3), np.uint8)
    # mask_gradient = cv2.morphologyEx(mask_final, cv2.MORPH_GRADIENT, gradient_kernel)

    # 再次应用闭运算，确保边缘连接良好
    final_close_kernel = np.ones((7, 7), np.uint8)
    mask_final = cv2.morphologyEx(mask_final, cv2.MORPH_CLOSE, final_close_kernel)

    # 查找所有轮廓（包括内部轮廓）
    contours, hierarchy = cv2.findContours(mask_final, cv2.RETR_LIST, cv2.CHAIN_APPROX_SIMPLE)

    logger.info(f"初始轮廓数量: {len(contours)}")

    # 分析和优化每个轮廓
    valid_contours = []
    for i, cnt in enumerate(contours, 1):
        # 分析轮廓特征
        features = analyze_contour(cnt)

        # 应用面积和形状过滤
        if features['area'] < min_contour_area:
            logger.info(f"轮廓 {i} 被忽略: 面积过小 ({features['area']:.1f})")
            continue

        if features['circularity'] < 0.1:  # 过滤非常不规则的形状
            logger.info(f"轮廓 {i} 被忽略: 形状过于不规则 (circularity={features['circularity']:.3f})")
            continue

        # 优化轮廓
        optimized_cnt = optimize_contour(cnt)
        valid_contours.append(optimized_cnt)

        # 记录详细信息
        logger.info(f"处理轮廓 {i}:")
        logger.info(f"  - 面积: {features['area']:.1f}")
        logger.info(f"  - 周长: {features['perimeter']:.1f}")
        logger.info(f"  - 点数: {features['points']}")
        logger.info(f"  - 圆度: {features['circularity']:.3f}")
        logger.info(f"  - 填充率: {features['solidity']:.3f}")

    # 按面积从大到小排序
    contours = sorted(valid_contours, key=cv2.contourArea, reverse=True)
    logger.info(f"总共保留了 {len(contours)} 个有效轮廓")
    logger.info(f"保留的轮廓数量: {len(contours)}")

    # 保留所有超过最小面积的轮廓
    if len(contours) > 0:
        logger.info(f"找到 {len(contours)} 个有效轮廓")

        # 创建一个新的掩码图像，包含所有轮廓
        merged_mask = np.zeros_like(mask_final)
        cv2.drawContours(merged_mask, contours, -1, 255, -1)

        # 对每个轮廓分别进行优化处理
        optimized_contours = []
        for i, cnt in enumerate(contours):
            # 创建单个轮廓的掩码
            single_mask = np.zeros_like(mask_final)
            cv2.drawContours(single_mask, [cnt], -1, 255, -1)

            # 应用形态学操作优化轮廓
            # 轻微的膨胀操作，连接非常接近的部分
            dilate_kernel = np.ones((3, 3), np.uint8)
            dilated = cv2.dilate(single_mask, dilate_kernel, iterations=1)

            # 轻微的腐蚀操作，恢复轮廓
            erode_kernel = np.ones((3, 3), np.uint8)
            eroded = cv2.erode(dilated, erode_kernel, iterations=1)

            # 提取优化后的轮廓
            optimized_cnt, _ = cv2.findContours(eroded, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            # 如果优化后的轮廓仍然有效，添加到结果中
            for opt_cnt in optimized_cnt:
                if cv2.contourArea(opt_cnt) > min_contour_area:
                    optimized_contours.append(opt_cnt)

            logger.info(f"处理轮廓 {i + 1}: 面积 = {cv2.contourArea(cnt):.1f}, 点数 = {len(cnt)}")

        contours = optimized_contours
        logger.info(f"优化后保留了 {len(contours)} 个轮廓")

    # 平滑轮廓，避免折线但保留细节
    smoothed_contours = []
    for contour in contours:
        # 使用Douglas-Peucker算法简化轮廓，使用更大的epsilon值减少点数
        epsilon = 0.01 * cv2.arcLength(contour, True)  # 轮廓周长的1%
        approx = cv2.approxPolyDP(contour, epsilon, True)

        # 对于较大的轮廓，使用样条曲线平滑
        if cv2.contourArea(contour) > min_contour_area * 5:  # 对大轮廓使用更强的平滑
            if len(approx) > 2:  # 确保有足够的点进行平滑
                # 将轮廓点转换为数组
                contour_array = approx.reshape(-1, 2)

                # 闭合轮廓，确保首尾相连
                if not np.array_equal(contour_array[0], contour_array[-1]):
                    contour_array = np.vstack([contour_array, contour_array[0]])

                # 创建参数化的点序列
                t = np.arange(len(contour_array))

                # 创建周期性样条曲线，保留更多细节
                num_points = int(len(contour_array) * spline_smoothness)  # 转换为整数
                x_smooth = np.linspace(0, len(contour_array) - 1, num_points)

                # 对x和y坐标分别进行样条插值
                x_spline = np.interp(x_smooth, t, contour_array[:, 0])
                y_spline = np.interp(x_smooth, t, contour_array[:, 1])

                # 应用高斯滤波进行平滑，但保留细节
                x_spline = ndimage.gaussian_filter1d(x_spline, sigma=gaussian_sigma)
                y_spline = ndimage.gaussian_filter1d(y_spline, sigma=gaussian_sigma)

                # 将平滑后的点转换回轮廓格式
                smooth_contour = np.column_stack([x_spline, y_spline]).astype(np.int32)
                smooth_contour = smooth_contour.reshape(-1, 1, 2)

                smoothed_contours.append(smooth_contour)
            else:
                # 如果点太少，无法平滑，则保持原样
                smoothed_contours.append(approx)
        else:
            # 对于小轮廓，使用更轻微的平滑以保留细节
            if len(approx) > 2:
                # 将轮廓点转换为数组
                contour_array = approx.reshape(-1, 2)

                # 闭合轮廓，确保首尾相连
                if not np.array_equal(contour_array[0], contour_array[-1]):
                    contour_array = np.vstack([contour_array, contour_array[0]])

                # 创建参数化的点序列
                t = np.arange(len(contour_array))

                # 使用更少的平滑来保留细节
                num_points = int(len(contour_array) * 2)  # 转换为整数
                x_smooth = np.linspace(0, len(contour_array) - 1, num_points)

                # 对x和y坐标分别进行样条插值
                x_spline = np.interp(x_smooth, t, contour_array[:, 0])
                y_spline = np.interp(x_smooth, t, contour_array[:, 1])

                # 应用轻微的高斯滤波
                x_spline = ndimage.gaussian_filter1d(x_spline, sigma=1)
                y_spline = ndimage.gaussian_filter1d(y_spline, sigma=1)

                # 将平滑后的点转换回轮廓格式
                smooth_contour = np.column_stack([x_spline, y_spline]).astype(np.int32)
                smooth_contour = smooth_contour.reshape(-1, 1, 2)

                smoothed_contours.append(smooth_contour)
            else:
                # 如果点太少，无法平滑，则保持原样
                smoothed_contours.append(approx)

    # 对每个轮廓进行优化处理
    optimized_contours = []
    for contour in contours:
        # 计算轮廓周长
        perimeter = cv2.arcLength(contour, True)

        # 根据轮廓大小动态调整epsilon值
        # 对于较大的轮廓使用较小的epsilon以保留更多细节
        area = cv2.contourArea(contour)
        if area > min_contour_area * 10:
            epsilon = 0.001 * perimeter
        else:
            epsilon = 0.005 * perimeter

        # 使用Douglas-Peucker算法简化轮廓
        approx = cv2.approxPolyDP(contour, epsilon, True)

        # 对于较长的轮廓，进行更细致的处理
        if len(approx) > 10:  # 只对点数较多的轮廓进行平滑
            # 将轮廓点转换为数组
            contour_array = approx.reshape(-1, 2)

            # 确保轮廓闭合
            if not np.array_equal(contour_array[0], contour_array[-1]):
                contour_array = np.vstack([contour_array, contour_array[0]])

            # 创建参数化的点序列
            t = np.arange(len(contour_array))

            # 根据轮廓长度动态调整平滑参数
            if len(contour_array) > 50:
                smooth_factor = 1.2  # 对于长轮廓使用较小的平滑因子
            else:
                smooth_factor = 1.5  # 对于短轮廓可以稍微平滑一些

            # 生成平滑的点序列
            x_smooth = np.linspace(0, len(contour_array) - 1, int(len(contour_array) * smooth_factor))

            # 对x和y坐标分别进行样条插值
            x_spline = np.interp(x_smooth, t, contour_array[:, 0])
            y_spline = np.interp(x_smooth, t, contour_array[:, 1])

            # 应用最小程度的高斯滤波
            x_spline = ndimage.gaussian_filter1d(x_spline, sigma=0.8)
            y_spline = ndimage.gaussian_filter1d(y_spline, sigma=0.8)

            # 将平滑后的点转换回轮廓格式
            smooth_contour = np.column_stack([x_spline, y_spline]).astype(np.int32)
            smooth_contour = smooth_contour.reshape(-1, 1, 2)

            optimized_contours.append(smooth_contour)
        else:
            # 对于点数较少的轮廓，直接使用近似结果
            optimized_contours.append(approx)

    # 更新轮廓列表
    simplified_contours = optimized_contours

    # 创建轮廓图像，使用不同的颜色显示不同的轮廓
    contour_image = np.zeros_like(image)
    for i, cnt in enumerate(simplified_contours):
        # 为每个轮廓生成不同的颜色
        color = (
            (i * 50) % 255,  # B
            255 - (i * 30) % 255,  # G
            (i * 70) % 255  # R
        )
        cv2.drawContours(contour_image, [cnt], -1, color, 2)
        # 添加轮廓编号
        M = cv2.moments(cnt)
        if M["m00"] != 0:
            cx = int(M["m10"] / M["m00"])
            cy = int(M["m01"] / M["m00"])
            cv2.putText(contour_image, str(i + 1), (cx - 10, cy + 10),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)

    # 创建填充的轮廓图像，使用半透明填充
    filled_contour_image = np.zeros_like(image)
    for i, cnt in enumerate(simplified_contours):
        # 为每个轮廓使用不同的颜色，但透明度相同
        color = (
            (i * 50) % 255,
            255 - (i * 30) % 255,
            (i * 70) % 255
        )
        # 创建单独的图层进行填充
        layer = np.zeros_like(image)
        cv2.drawContours(layer, [cnt], -1, color, -1)
        # 将填充的轮廓与主图像混合
        alpha = 0.4  # 透明度
        cv2.addWeighted(filled_contour_image, 1, layer, alpha, 0, filled_contour_image)

    # 如果指定了输出目录，保存结果
    if output_dir:
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        base_name = os.path.splitext(os.path.basename(image_path))[0]

        # 保存掩码图像
        mask_path = os.path.join(args.output_dir, f"{base_name}_mask.png")
        os.makedirs(os.path.dirname(mask_path), exist_ok=True)
        cv2.imwrite(mask_path, mask_final)

        # 保存轮廓图像
        contour_path = os.path.join(args.output_dir, f"{base_name}_contour.png")
        cv2.imwrite(contour_path, contour_image)

        # 保存填充轮廓图像
        filled_contour_path = os.path.join(args.output_dir, f"{base_name}_filled_contour.png")
        cv2.imwrite(filled_contour_path, filled_contour_image)

        # 保存轮廓叠加在原图上的图像
        overlay_image = image_rgb.copy()
        contour_overlay = cv2.cvtColor(contour_image, cv2.COLOR_BGR2RGB)
        overlay_image = cv2.addWeighted(overlay_image, 1, contour_overlay, 0.5, 0)
        overlay_path = os.path.join(args.output_dir, f"{base_name}_overlay.png")
        plt.imsave(overlay_path, overlay_image)

        print(f"结果已保存到: {output_dir}")

    return image_rgb, mask_final, contour_image, simplified_contours


def calculate_bounds(polygon):
    """计算多边形的边界框"""
    if not polygon:
        return None

    x_coords, y_coords = zip(*polygon)
    min_x = min(x_coords)
    max_x = max(x_coords)
    min_y = min(y_coords)
    max_y = max(y_coords)

    return min_x, max_x, min_y, max_y


def visualize_results(image, mask, contour_image, simplified_contours, reference_polygon=None):
    """
    可视化结果

    参数:
    - image: 原始图像
    - mask: 掩码图像
    - contour_image: 轮廓图像
    - simplified_contours: 简化后的轮廓列表
    - reference_polygon: 参考多边形坐标，格式为 [(x1,y1), (x2,y2), ...]
    """
    plt.figure(figsize=(15, 10))

    # 显示原始图像
    plt.subplot(2, 2, 1)
    plt.imshow(image)
    plt.title('原始图像')
    plt.axis('off')

    # 显示掩码
    plt.subplot(2, 2, 2)
    plt.imshow(mask, cmap='gray')
    plt.title('颜色掩码')
    plt.axis('off')

    # 显示轮廓
    plt.subplot(2, 2, 3)
    plt.imshow(cv2.cvtColor(contour_image, cv2.COLOR_BGR2RGB))
    plt.title(f'提取的轮廓 (共{len(simplified_contours)}个)')
    plt.axis('off')

    # 在图像旁边添加轮廓信息
    info_text = []
    for i, cnt in enumerate(simplified_contours):
        area = cv2.contourArea(cnt)
        perimeter = cv2.arcLength(cnt, True)
        info_text.append(f"轮廓{i + 1}: {len(cnt)}点, 面积={area:.0f}, 周长={perimeter:.0f}")

    plt.figtext(1.02, 0.6, '\n'.join(info_text), fontsize=8, va='center')

    # 显示轮廓叠加在原图上
    plt.subplot(2, 2, 4)
    overlay = image.copy()
    contour_overlay = cv2.cvtColor(contour_image, cv2.COLOR_BGR2RGB)
    overlay = cv2.addWeighted(overlay, 1, contour_overlay, 0.5, 0)

    # 如果提供了参考多边形，绘制到叠加图像上
    if reference_polygon is not None and len(reference_polygon) > 0:
        # 将参考多边形转换为numpy数组
        ref_poly = np.array(reference_polygon, dtype=np.int32)

        # 绘制参考多边形
        overlay_with_ref = overlay.copy()
        cv2.polylines(overlay_with_ref, [ref_poly], True, (255, 0, 0), 2)  # 红色线条

        # 计算参考多边形与提取轮廓的重叠度
        if simplified_contours and len(simplified_contours) > 0:
            # 创建参考多边形的掩码
            h, w = mask.shape[:2] if len(mask.shape) > 2 else mask.shape
            ref_mask = np.zeros((h, w), dtype=np.uint8)
            cv2.fillPoly(ref_mask, [ref_poly], 255)

            # 创建提取轮廓的掩码
            contour_mask = np.zeros_like(ref_mask)
            cv2.drawContours(contour_mask, simplified_contours, -1, 255, -1)

            # 计算重叠区域
            overlap = cv2.bitwise_and(ref_mask, contour_mask)

            # 计算IoU (Intersection over Union)
            intersection = np.sum(overlap > 0)
            union = np.sum((ref_mask > 0) | (contour_mask > 0))
            iou = intersection / union if union > 0 else 0

            # 计算重叠率 (Overlap Ratio)
            ref_area = np.sum(ref_mask > 0)
            contour_area = np.sum(contour_mask > 0)
            overlap_ratio_ref = intersection / ref_area if ref_area > 0 else 0
            overlap_ratio_contour = intersection / contour_area if contour_area > 0 else 0

            # 显示匹配度信息
            plt.title(f"叠加效果 (IoU: {iou:.2f}, 参考覆盖率: {overlap_ratio_ref:.2f})")

            print(f"匹配度分析:")
            print(f"- IoU (Intersection over Union): {iou:.4f}")
            print(f"- 参考多边形被轮廓覆盖率: {overlap_ratio_ref:.4f}")
            print(f"- 轮廓被参考多边形覆盖率: {overlap_ratio_contour:.4f}")

        overlay = overlay_with_ref
    else:
        plt.title('轮廓叠加在原图上')

    plt.imshow(overlay)
    plt.axis('off')

    plt.tight_layout()
    plt.show()


def save_contours_to_file(contours, output_path, transform=None):
    """
    将轮廓坐标保存到PRN格式文件，提供详细的调试信息和错误检查
    """
    try:
        print("\n=== 开始保存轮廓坐标 ===")
        print(f"输出文件: {output_path}")
        print(f"轮廓数量: {len(contours)}")

        # 确保输出文件使用.prn扩展名
        if not output_path.lower().endswith('.prn'):
            output_path = os.path.splitext(output_path)[0] + '.prn'

        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_path), exist_ok=True)

        total_points = 0
        points_stats = []

        with open(output_path, 'w', encoding='utf-8', newline='\n') as f:
            # 直接写入数据，不添加注释道头和BOM

            # 遍历每个轮廓
            for contour_index, contour in enumerate(contours, 1):
                points_in_contour = 0
                min_x = float('inf')
                max_x = float('-inf')
                min_y = float('inf')
                max_y = float('-inf')

                # 处理轮廓中的每个点
                for point in contour:
                    x, y = point[0]
                    if transform:
                        try:
                            # 应用坐标转换
                            geo_x, geo_y = transform(float(x), float(y))

                            # 更新边界值
                            min_x = min(min_x, geo_x)
                            max_x = max(max_x, geo_x)
                            min_y = min(min_y, geo_y)
                            max_y = max(max_y, geo_y)

                            # 格式化输出
                            x_str = f"{geo_x:.2f}".rjust(12)
                            y_str = f"{geo_y:.2f}".rjust(12)
                        except Exception as e:
                            print(f"警告: 坐标转换失败 ({x}, {y}): {e}")
                            continue
                    else:
                        # 使用原始坐标值
                        min_x = min(min_x, x)
                        max_x = max(max_x, x)
                        min_y = min(min_y, y)
                        max_y = max(max_y, y)

                    # 写入格式化的行，使用Excel兼容的固定宽度格式
                    # 使用轮廓索引作为编号
                    if transform:
                        line = f"{' ' * 7}{geo_x:12.2f}{' ' * 6}{geo_y:12.2f}{' ' * 15}{contour_index}{' ' * 15}1\n"
                    else:
                        line = f"{' ' * 7}{float(x):12.2f}{' ' * 6}{float(y):12.2f}{' ' * 15}{contour_index}{' ' * 15}1\n"
                    f.write(line)

                    points_in_contour += 1
                    total_points += 1

                # 收集每个轮廓的统计信息
                if points_in_contour > 0:
                    points_stats.append({
                        'contour_id': contour_index,
                        'points': points_in_contour,
                        'bounds': {
                            'min_x': min_x,
                            'max_x': max_x,
                            'min_y': min_y,
                            'max_y': max_y
                        }
                    })

        # 打印统计信息
        print("\n=== 轮廓保存统计 ===")
        print(f"总点数: {total_points}")
        print(f"轮廓数: {len(points_stats)}")
        print("\n轮廓详细信息:")
        for stat in points_stats:
            print(f"\n轮廓 {stat['contour_id']}:")
            print(f"  点数: {stat['points']}")
            bounds = stat['bounds']
            print(f"  范围: X({bounds['min_x']:.2f} to {bounds['max_x']:.2f}), "
                  f"Y({bounds['min_y']:.2f} to {bounds['max_y']:.2f})")

        print(f"\n文件已成功保存: {output_path}")
        return True

    except Exception as e:
        print(f"错误: 保存轮廓时发生异常: {e}")
        import traceback
        traceback.print_exc()
        return False


def save_matching_results(output_path, reference_polygon, simplified_contours, mask_shape):
    """
    保存匹配结果到文件

    参数:
    - output_path: 输出文件路径
    - reference_polygon: 参考多边形坐标
    - simplified_contours: 提取的轮廓
    - mask_shape: 掩码图像的形状
    """
    if reference_polygon is None or not simplified_contours:
        print("无法保存匹配结果: 参考多边形或提取轮廓为空")
        return

    try:
        # 创建参考多边形的掩码
        h, w = mask_shape[:2] if len(mask_shape) > 2 else mask_shape
        ref_mask = np.zeros((h, w), dtype=np.uint8)
        ref_poly = np.array(reference_polygon, dtype=np.int32)
        cv2.fillPoly(ref_mask, [ref_poly], 255)

        # 创建提取轮廓的掩码
        contour_mask = np.zeros_like(ref_mask)
        cv2.drawContours(contour_mask, simplified_contours, -1, 255, -1)

        # 计算重叠区域
        overlap = cv2.bitwise_and(ref_mask, contour_mask)

        # 计算IoU (Intersection over Union)
        intersection = np.sum(overlap > 0)
        union = np.sum((ref_mask > 0) | (contour_mask > 0))
        iou = intersection / union if union > 0 else 0

        # 计算重叠率 (Overlap Ratio)
        ref_area = np.sum(ref_mask > 0)
        contour_area = np.sum(contour_mask > 0)
        overlap_ratio_ref = intersection / ref_area if ref_area > 0 else 0
        overlap_ratio_contour = intersection / contour_area if contour_area > 0 else 0

        # 保存结果
        with open(output_path, 'w') as f:
            f.write("# 匹配结果\n")
            f.write(f"IoU (Intersection over Union): {iou:.6f}\n")
            f.write(f"参考多边形被轮廓覆盖率: {overlap_ratio_ref:.6f}\n")
            f.write(f"轮廓被参考多边形覆盖率: {overlap_ratio_contour:.6f}\n")
            f.write(f"参考多边形面积: {ref_area} 像素\n")
            f.write(f"提取轮廓面积: {contour_area} 像素\n")
            f.write(f"重叠区域面积: {intersection} 像素\n")
            f.write(f"并集区域面积: {union} 像素\n")

        print(f"匹配结果已保存到: {output_path}")

        # 保存可视化图像
        vis_path = output_path.replace('.txt', '.png')
        if vis_path == output_path:
            vis_path = output_path + '.png'

        # 创建可视化图像
        vis_image = np.zeros((h, w, 3), dtype=np.uint8)
        vis_image[ref_mask > 0] = [255, 0, 0]  # 红色表示参考多边形
        vis_image[contour_mask > 0] = [0, 255, 0]  # 绿色表示提取轮廓
        vis_image[overlap > 0] = [0, 0, 255]  # 蓝色表示重叠区域

        cv2.imwrite(vis_path, vis_image)
        print(f"匹配可视化图像已保存到: {vis_path}")

    except Exception as e:
        print(f"保存匹配结果时出错: {e}")


def load_polygon_from_file(file_path):
    """
    从文件中加载多边形坐标

    参数:
    - file_path: 包含多边形坐标的文件路径

    返回:
    - 多边形坐标列表 [(x1,y1), (x2,y2), ...]
    """
    polygon = []
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                # 跳过注释行和空行
                line = line.strip()
                if not line or line.startswith('#'):
                    continue

                # 解析坐标
                try:
                    print(f"正在解析行: {line}")
                    parts = line.split()
                    if len(parts) >= 2:  # 确保至少有两个部分 (x 和 y)
                        x = float(parts[0])
                        y = float(parts[1])
                        polygon.append((x, y))
                    else:
                        print(f"警告: 坐标行格式不正确: {line}")
                except ValueError:
                    print(f"警告: 无法解析坐标行: {line}")

        print(f"从文件 {file_path} 加载了 {len(polygon)} 个多边形坐标点")
    except FileNotFoundError:
        print(f"错误: 找不到文件: {file_path}")
        return None  # 文件未找到，返回 None
    except Exception as e:
        print(f"加载多边形文件时出错: {e}")
        return None  # 加载文件时出错，返回 None

    return polygon


def create_coordinate_transform(image_width, image_height, ref_polygon, args):
    """创建坐标转换函数，将图像像素坐标转换为地理坐标"""
    logger.info("初始化坐标转换")

    # 参数验证
    if image_width <= 0 or image_height <= 0:
        logger.error(f"图像尺寸无效: {image_width}x{image_height}")
        raise ValueError("图像尺寸必须为正数")

    # 如果没有参考多边形，返回一个恒等变换
    if ref_polygon is None:
        logger.warning("未提供参考多边形，将使用像素坐标")

        def identity_transform(pixel_x, pixel_y):
            return pixel_x, pixel_y

        return identity_transform

    if len(ref_polygon) < 3:
        logger.error(f"参考多边形点数不足: {len(ref_polygon)} < 3")
        raise ValueError("参考多边形必须至少有3个点")

    # 获取参考多边形的边界
    bounds = calculate_bounds(ref_polygon)
    if bounds is None:
        logger.error("无法计算参考多边形边界")
        raise ValueError("参考多边形边界计算失败")

    min_x, max_x, min_y, max_y = bounds
    ref_width = max_x - min_x
    ref_height = max_y - min_y

    # 验证参考区域的有效性
    if ref_width <= 0 or ref_height <= 0:
        logger.error(f"参考区域尺寸无效: {ref_width}x{ref_height}")
        raise ValueError("参考区域必须有正的宽度和高度")

    # 计算和记录变换参数
    x_scale = ref_width / image_width
    y_scale = ref_height / image_height

    logger.info("=== 坐标转换设置 ===")
    logger.info(f"图像尺寸: {image_width} x {image_height} 像素")
    logger.info(f"参考区域: X({min_x:.2f} to {max_x:.2f}), Y({min_y:.2f} to {max_y:.2f})")
    logger.info(f"变换比例: X={x_scale:.6f}, Y={y_scale:.6f} 单位/像素")

    # 创建变换函数
    def transform(pixel_x, pixel_y):
        try:
            # 边界检查
            if not (0 <= pixel_x < image_width and 0 <= pixel_y < image_height):
                logger.warning(f"像素坐标超出范围: ({pixel_x}, {pixel_y})")
                pixel_x = max(0, min(pixel_x, image_width - 1))
                pixel_y = max(0, min(pixel_y, image_height - 1))

            # 归一化坐标
            norm_x = pixel_x / image_width
            norm_y = 1.0 - (pixel_y / image_height)  # Y轴反转

            # 计算地理坐标
            geo_x = min_x + (norm_x * ref_width)
            geo_y = min_y + (norm_y * ref_height)

            # 结果验证
            if not (min_x <= geo_x <= max_x and min_y <= geo_y <= max_y):
                logger.warning(f"转换结果超出参考范围: ({geo_x}, {geo_y})")

            return geo_x, geo_y

        except Exception as e:
            logger.error(f"坐标转换失败: {str(e)}")
            raise

    # 验证转换函数
    test_points = [(0, 0), (image_width - 1, 0), (0, image_height - 1),
                   (image_width - 1, image_height - 1), (image_width / 2, image_height / 2)]

    logger.info("\n=== 验证坐标转换 ===")
    for px, py in test_points:
        gx, gy = transform(px, py)
        logger.info(f"测试点 ({px:.1f}, {py:.1f}) -> ({gx:.2f}, {gy:.2f})")

    return transform


if __name__ == "__main__":
    try:
        logger.info("开始处理命令行参数")
        exit_code = 0  # 初始化退出代码

        # 创建命令行参数解析器
        parser = argparse.ArgumentParser(description='从图像中提取轮廓')
        parser.add_argument('--image', type=str, help='输入图像路径')
        parser.add_argument('--contour-mode', action='store_true', help='是否将输入图像视为黑白轮廓图像')
        parser.add_argument('--output-dir', type=str, help='输出目录路径')
        parser.add_argument('--min-area', type=int, default=300, help='最小轮廓面积')
        parser.add_argument('--color', type=str, choices=['orange', 'cyan'], default='orange',
                            help='要提取的颜色 (orange=橙色, cyan=青色)')
        parser.add_argument('--reference', type=str, help='参考多边形坐标文件路径')
        parser.add_argument('--output-geo', type=str, help='输出地理坐标轮廓文件路径')

        # 解析命令行参数
        args = parser.parse_args()
        logger.info(f"命令行参数: {args}")

        # 设置默认参数
        if not args.reference:
            args.reference = "ref_polygon.txt"  # 默认参考多边形文件
            logger.info(f"使用默认参考多边形文件: {args.reference}")

        # 设置输出目录
        if not args.output_dir:
            args.output_dir = r"C:\Users\<USER>\Desktop\111\output"  # 默认输出目录
            logger.info(f"使用默认输出目录: {args.output_dir}")

        # 确保输出目录存在
        os.makedirs(args.output_dir, exist_ok=True)
        logger.info(f"确保输出目录存在: {args.output_dir}")

        # 设置地理坐标输出文件
        if not args.output_geo:
            args.output_geo = os.path.join(args.output_dir, "geo_contours.prn")  # 默认输出文件名
            logger.info(f"使用默认输出地理坐标文件: {args.output_geo}")

        # 设置图像路径
        if args.image:
            image_path = args.image
            logger.info(f"使用命令行提供的图像路径: {image_path}")
        elif len(sys.argv) > 1 and not sys.argv[1].startswith('--'):
            # 兼容旧的命令行参数格式
            image_path = sys.argv[1]
            logger.info(f"使用命令行提供的图像路径: {image_path}")
        else:
            # 默认图像路径
            image_path = "232323_overlay.png"
            logger.info(f"使用默认图像路径: {image_path}")

        # 设置颜色范围
        if args.color == 'orange':
            # 橙色的HSV范围
            color_lower = np.array([11, 43, 46])
            color_upper = np.array([25, 255, 255])
        else:  # cyan
            # 青色的HSV范围
            color_lower = np.array([75, 43, 46])
            color_upper = np.array([110, 255, 255])

        # 确保输出目录存在
        output_dir = args.output_dir
        if output_dir:
            os.makedirs(output_dir, exist_ok=True)

        # 提取轮廓参数
        min_contour_area = 300  # 进一步降低最小轮廓面积，保留更细节
        spline_smoothness = 1.5  # 降低样条曲线平滑度，使轮廓更接近原始形状
        gaussian_sigma = 0.8  # 减小高斯滤波的sigma值，保留更多边缘细节

        # 提取轮廓
        logger.info("开始提取轮廓...")

        # 设置是否为轮廓模式
        is_contour_mode = args.contour_mode

        image, mask, contour_image, simplified_contours = extract_contours(
            image_path,
            (color_lower, color_upper),
            output_dir,
            min_contour_area,
            spline_smoothness,
            gaussian_sigma,
            is_contour_mode
        )

        # 验证轮廓质量
        logger.info("验证轮廓质量...")
        valid_contours = []
        total_contours = len(simplified_contours)

        for i, contour in enumerate(simplified_contours):
            percentage = ((i + 1) / total_contours) * 100
            logger.info(f"正在验证轮廓 {i + 1}/{total_contours} ({percentage:.1f}%)")

            try:
                # 检查轮廓的基本属性
                n_points = len(contour)
                area = cv2.contourArea(contour)
                perimeter = cv2.arcLength(contour, True)

                # 基本验证
                if n_points < 10:
                    logger.warning(f"轮廓 {i + 1} 点数过少: {n_points}")
                    continue

                if area < min_contour_area:
                    logger.warning(f"轮廓 {i + 1} 面积过小: {area:.1f}")
                    continue

                # 计算轮廓的复杂度
                complexity = perimeter * perimeter / area
                if complexity > 100:  # 这个阈值可以调整
                    logger.warning(f"轮廓 {i + 1} 过于复杂: complexity = {complexity:.1f}")
                    continue

                valid_contours.append(contour)
                logger.info(f"轮廓 {i + 1} 通过验证: {n_points}点, 面积={area:.1f}")

            except Exception as e:
                logger.error(f"验证轮廓 {i + 1} 时出错: {str(e)}")
                continue

        logger.info(f"轮廓验证完成: {len(valid_contours)}/{total_contours} 个轮廓通过验证")

        # 更新轮廓列表
        simplified_contours = valid_contours

        # 加载参考多边形
        if args.reference and os.path.exists(args.reference):
            logger.info(f"加载参考多边形: {args.reference}")
            reference_polygon = load_polygon_from_file(args.reference)
            if reference_polygon:
                logger.info(f"成功加载了 {len(reference_polygon)} 个参考点")
            else:
                logger.warning("参考多边形加载失败")
        else:
            logger.warning(f"参考多边形文件不存在: {args.reference}")
            reference_polygon = None

        # 可视化结果
        visualize_results(image, mask, contour_image, simplified_contours, reference_polygon)

        # 保存轮廓坐标到文本文件
        if output_dir:
            # 获取图像尺寸
            try:
                image_height, image_width, _ = image.shape
            except Exception as e:
                logger.warning(f"获取图像尺寸时出错: {e}")
                try:
                    if len(mask.shape) > 2:
                        image_height, image_width, _ = mask.shape
                    else:
                        image_height, image_width = mask.shape
                except Exception as e:
                    logger.warning(f"获取掩码尺寸时出错: {e}")
                    image_height, image_width = 500, 500

            # 创建坐标转换函数
            transform = create_coordinate_transform(image_width, image_height, reference_polygon, args)

            # 如果指定了输出地理坐标文件，则保存地理坐标
            if args.output_geo:
                # 确保使用.prn扩展名
                base_output_name = os.path.splitext(args.output_geo)[0] + '.prn'

                # 如果提供的是绝对路径，则直接使用
                if os.path.isabs(args.output_geo):
                    geo_contours_file = os.path.splitext(args.output_geo)[0] + '.prn'
                # 确保目标目录存在
                os.makedirs(os.path.dirname(geo_contours_file), exist_ok=True)
                save_contours_to_file(simplified_contours, geo_contours_file, transform=transform)  # 保存地理坐标
                logger.info(f"轮廓地理坐标已保存到: {geo_contours_file}")
            else:
                contours_file = os.path.join(args.output_dir, "contour_coordinates.prn")  # 默认文件也使用.prn扩展名
                os.makedirs(os.path.dirname(contours_file), exist_ok=True)
                save_contours_to_file(simplified_contours, contours_file, transform=None)  # 保存像素坐标
                logger.info(f"轮廓像素坐标已保存到: {contours_file}")

            # 如果提供了参考多边形，保存匹配结果
            if reference_polygon:
                matching_file = os.path.join(args.output_dir, "matching_results.txt")
                os.makedirs(os.path.dirname(matching_file), exist_ok=True)
                save_matching_results(matching_file, reference_polygon, simplified_contours, mask.shape)

                # 将参考多边形也保存为文本文件
                ref_file = os.path.join(args.output_dir, "reference_polygon.txt")
                os.makedirs(os.path.dirname(ref_file), exist_ok=True)
                with open(ref_file, 'w') as f:
                    f.write("# 参考多边形坐标 (x, y)\n")
                    for x, y in reference_polygon:
                        f.write(f"{x},{y}\n")
                logger.info(f"参考多边形坐标已保存到: {ref_file}")

        logger.info("处理完成")
    except Exception as e:
        logger.error(f"处理过程中发生异常: {str(e)}")
        traceback.print_exc()
        exit_code = 1
    finally:
        # 记录程序结束状态
        if exit_code == 0:
            logger.info("程序正常结束")
        else:
            logger.error("程序异常结束")
        sys.exit(exit_code)


def show_progress(current, total, message="处理中"):
    """显示处理进度"""
    percentage = (current / total) * 100
    bar_length = 50
    filled_length = int(bar_length * current / total)
    bar = '=' * filled_length + '-' * (bar_length - filled_length)
    sys.stdout.write(f'\r{message}: [{bar}] {percentage:.1f}%')
    if current == total:
        sys.stdout.write('\n')
    sys.stdout.flush()


def validate_contour_quality(contour, min_points=10, min_area=100, max_angle=150):
    """
    验证轮廓的质量

    参数:
    - contour: 要验证的轮廓
    - min_points: 最小点数
    - min_area: 最小面积
    - max_angle: 最大允许角度（度）

    返回:
    - (bool, str): (是否有效, 原因描述)
    """
    # 检查点数
    if len(contour) < min_points:
        return False, f"点数太少 ({len(contour)} < {min_points})"

    # 检查面积
    area = cv2.contourArea(contour)
    if area < min_area:
        return False, f"面积太小 ({area:.1f} < {min_area})"

    # 检查角度（避免尖锐角）
    for i in range(len(contour)):
        p1 = contour[i][0]
        p2 = contour[(i + 1) % len(contour)][0]
        p3 = contour[(i + 2) % len(contour)][0]

        # 计算两个向量
        v1 = p2 - p1
        v2 = p3 - p2

        # 计算角度
        dot = np.dot(v1, v2)
        norm = np.linalg.norm(v1) * np.linalg.norm(v2)
        if norm == 0:
            continue

        angle = np.arccos(dot / norm) * 180 / np.pi
        if angle > max_angle:
            return False, f"存在尖锐角 ({angle:.1f}° > {max_angle}°)"

    return True, "轮廓质量符合要求"


def simplify_contour(contour, epsilon_factor=0.01, min_points=10):
    """
    使用Douglas-Peucker算法简化轮廓

    参数:
    - contour: 要简化的轮廓
    - epsilon_factor: 简化因子，越大简化越强
    - min_points: 最小保留点数

    返回:
    - 简化后的轮廓
    """
    try:
        # 计算周长
        perimeter = cv2.arcLength(contour, True)
        epsilon = epsilon_factor * perimeter

        # 简化轮廓
        approx = cv2.approxPolyDP(contour, epsilon, True)

        # 如果简化后的点数太少，减小epsilon重试
        while len(approx) < min_points and epsilon > 0.0001:
            epsilon *= 0.5
            approx = cv2.approxPolyDP(contour, epsilon, True)

        return approx

    except Exception as e:
        logger.error(f"轮廓简化失败: {str(e)}")
        return contour

import cv2
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.widgets import <PERSON><PERSON>, Slider
from matplotlib.patches import Rectangle
import tkinter as tk
from tkinter import filedialog, colorchooser, messagebox
import os


class RiverSandExtractor:
    def __init__(self):
        self.image = None
        self.original_image = None
        self.selected_color = None
        self.color_tolerance = 30
        self.min_area = 100
        self.current_mask = None
        self.contours = None
        self.boundary_coords = None
        self.transform_matrix = None

    def load_boundary_coordinates(self, boundary_data=None):
        """加载边界坐标数据"""
        if boundary_data is None:
            # 使用提供的默认边界坐标
            boundary_text = """737850.762740 3445906.000048      1 -1.000000
738513.080123 3458438.504686      1 320.000000
742038.576217 3470170.891405      1 320.000000
753077.423873 3469997.508593      1 320.000000
754695.683150 3459478.817186      1 320.000000
754580.094771 3432083.982225      1 320.000000
761483.816451 3432141.777147      1 320.000000
761550.149459 3423067.959765      1 320.000000
748537.731002 3423067.961718      1 320.000000
743971.925826 3430292.336718      1 320.000000
747381.832076 3430754.696093      1 320.000000
747266.241744 3433182.087694      1 320.000000
741949.100631 3433008.701952      1 320.000000
737850.762740 3445906.000048      1 -1.000000"""

            lines = boundary_text.strip().split('\n')
            coords = []
            for line in lines:
                parts = line.strip().split()
                if len(parts) >= 2:
                    x, y = float(parts[0]), float(parts[1])
                    coords.append([x, y])
            self.boundary_coords = np.array(coords)
        else:
            self.boundary_coords = np.array(boundary_data)

        print(f"边界坐标加载成功，共 {len(self.boundary_coords)} 个点")
        return True

    def calculate_transform_matrix(self):
        """计算从图像像素坐标到实际坐标的转换矩阵"""
        if self.image is None or self.boundary_coords is None:
            return False

        # 获取图像尺寸
        img_height, img_width = self.image.shape[:2]

        # 图像四个角点 (像素坐标)
        img_corners = np.array([
            [0, 0],  # 左上角
            [img_width, 0],  # 右上角
            [img_width, img_height],  # 右下角
            [0, img_height]  # 左下角
        ], dtype=np.float32)

        # 找到边界坐标的四个极值点
        min_x_idx = np.argmin(self.boundary_coords[:, 0])
        max_x_idx = np.argmax(self.boundary_coords[:, 0])
        min_y_idx = np.argmin(self.boundary_coords[:, 1])
        max_y_idx = np.argmax(self.boundary_coords[:, 1])

        # 实际坐标的四个角点 (根据边界范围估算)
        min_x, max_x = np.min(self.boundary_coords[:, 0]), np.max(self.boundary_coords[:, 0])
        min_y, max_y = np.min(self.boundary_coords[:, 1]), np.max(self.boundary_coords[:, 1])

        real_corners = np.array([
            [min_x, max_y],  # 左上角 (最小x, 最大y)
            [max_x, max_y],  # 右上角 (最大x, 最大y)
            [max_x, min_y],  # 右下角 (最大x, 最小y)
            [min_x, min_y]  # 左下角 (最小x, 最小y)
        ], dtype=np.float32)

        # 计算透视变换矩阵
        self.transform_matrix = cv2.getPerspectiveTransform(img_corners, real_corners)

        print("坐标转换矩阵计算完成")
        print(f"图像范围: {img_width} x {img_height} 像素")
        print(f"实际坐标范围: X({min_x:.2f} - {max_x:.2f}), Y({min_y:.2f} - {max_y:.2f})")

        return True

    def pixel_to_real_coords(self, pixel_coords):
        """将像素坐标转换为实际坐标"""
        if self.transform_matrix is None:
            print("错误: 转换矩阵未初始化!")
            return None

        # 确保输入是正确的格式
        if len(pixel_coords.shape) == 2 and pixel_coords.shape[1] == 2:
            # 添加第三维度为1 (齐次坐标)
            ones = np.ones((pixel_coords.shape[0], 1))
            pixel_homogeneous = np.hstack([pixel_coords, ones])
        else:
            return None

        # 应用变换矩阵
        real_homogeneous = self.transform_matrix.dot(pixel_homogeneous.T).T

        # 转换回笛卡尔坐标
        real_coords = real_homogeneous[:, :2] / real_homogeneous[:, 2:3]

        return real_coords

    def contours_to_real_coordinates(self, contours):
        """将轮廓的像素坐标转换为实际坐标"""
        if not contours or self.transform_matrix is None:
            return []

        real_contours = []
        for contour in contours:
            # 重塑轮廓数据
            pixel_points = contour.reshape(-1, 2).astype(np.float32)

            # 转换坐标
            real_points = self.pixel_to_real_coords(pixel_points)

            if real_points is not None:
                real_contours.append(real_points)

        return real_contours

    def save_polygons_to_file(self, real_contours, output_path):
        """保存polygon到文件，格式与边界文件相同"""
        if not real_contours:
            print("没有轮廓数据可保存")
            return False

        with open(output_path, 'w') as f:
            polygon_id = 1

            for contour_points in real_contours:
                # 确保轮廓是闭合的
                if len(contour_points) > 2:
                    # 如果起点和终点不相同，添加起点作为终点
                    if not np.allclose(contour_points[0], contour_points[-1], rtol=1e-10):
                        contour_points = np.vstack([contour_points, contour_points[0:1]])

                    # 写入轮廓点
                    for point in contour_points:
                        f.write(f"{point[0]:.6f} {point[1]:.6f}      {polygon_id} 1\n")

                    polygon_id += 1

        print(f"Polygon文件已保存: {output_path}")
        print(f"共保存 {polygon_id - 1} 个河道polygon")
        return True
        """加载图像"""
        if image_path is None:
            root = tk.Tk()
            root.withdraw()
            image_path = filedialog.askopenfilename(
                title="选择图像文件",
                filetypes=[("图像文件", "*.jpg *.jpeg *.png *.bmp *.tiff")]
            )
            root.destroy()

    def load_image(self, image_path=None):
        """加载图像"""
        if image_path is None:
            root = tk.Tk()
            root.withdraw()
            image_path = filedialog.askopenfilename(
                title="选择图像文件",
                filetypes=[("图像文件", "*.jpg *.jpeg *.png *.bmp *.tiff")]
            )
            root.destroy()

        if image_path:
            self.original_image = cv2.imread(image_path)
            self.image = cv2.cvtColor(self.original_image, cv2.COLOR_BGR2RGB)
            print(f"图像加载成功: {image_path}")
            print(f"图像尺寸: {self.image.shape}")

            # 自动加载边界坐标并计算转换矩阵
            self.load_boundary_coordinates()
            self.calculate_transform_matrix()

            return True
        return False

    def color_distance(self, color1, color2):
        """计算两个颜色之间的欧几里得距离"""
        return np.sqrt(np.sum((color1 - color2) ** 2))

    def create_color_mask(self, target_color, tolerance):
        """基于颜色创建掩码"""
        if self.image is None:
            return None

        # 将目标颜色转换为numpy数组
        target_rgb = np.array(target_color[:3])  # 只取RGB，忽略alpha

        # 计算每个像素与目标颜色的距离
        distances = np.sqrt(np.sum((self.image - target_rgb) ** 2, axis=2))

        # 创建掩码
        mask = distances <= tolerance

        return mask.astype(np.uint8) * 255

    def extract_contours(self, mask, min_area=100):
        """提取轮廓"""
        # 形态学操作，去除噪声
        kernel = np.ones((3, 3), np.uint8)
        mask_cleaned = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)
        mask_cleaned = cv2.morphologyEx(mask_cleaned, cv2.MORPH_CLOSE, kernel)

        # 查找轮廓
        contours, _ = cv2.findContours(mask_cleaned, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        # 过滤小的轮廓
        filtered_contours = [c for c in contours if cv2.contourArea(c) > min_area]

        return filtered_contours

    def process_contours_for_rivers(self, contours):
        """专门针对河道砂体的轮廓处理"""
        processed_contours = []

        for contour in contours:
            # 计算轮廓的几何特征
            area = cv2.contourArea(contour)
            perimeter = cv2.arcLength(contour, True)

            if perimeter == 0:
                continue

            # 计算长宽比和紧凑度
            rect = cv2.minAreaRect(contour)
            width, height = rect[1]
            aspect_ratio = max(width, height) / max(min(width, height), 1)
            compactness = 4 * np.pi * area / (perimeter * perimeter)

            # 河道特征：长条状（高长宽比）、相对不紧凑
            if aspect_ratio > 2 and area > self.min_area:
                processed_contours.append(contour)

        return processed_contours

    def interactive_color_selection(self):
        """Interactive color selection and contour extraction"""
        if self.image is None:
            print("Please load an image first!")
            return

        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('River Sand Body Extraction Tool', fontsize=16)

        # Display original image
        axes[0, 0].imshow(self.image)
        axes[0, 0].set_title('Original Image')
        axes[0, 0].axis('off')

        # Initialize other subplots
        mask_plot = axes[0, 1].imshow(np.zeros_like(self.image[:, :, 0]), cmap='gray')
        axes[0, 1].set_title('Color Mask')
        axes[0, 1].axis('off')

        contour_plot = axes[1, 0].imshow(self.image)
        axes[1, 0].set_title('Extracted Contours')
        axes[1, 0].axis('off')

        result_plot = axes[1, 1].imshow(np.zeros_like(self.image))
        axes[1, 1].set_title('Final Result')
        axes[1, 1].axis('off')

        # Add control panel
        plt.subplots_adjust(bottom=0.3)

        # Slider controls
        ax_tolerance = plt.axes([0.2, 0.15, 0.5, 0.03])
        tolerance_slider = Slider(ax_tolerance, 'Color Tolerance', 1, 100, valinit=30)

        ax_min_area = plt.axes([0.2, 0.1, 0.5, 0.03])
        area_slider = Slider(ax_min_area, 'Min Area (pixels)', 10, 1000, valinit=100)

        # Buttons
        ax_color_btn = plt.axes([0.05, 0.05, 0.12, 0.04])
        color_button = Button(ax_color_btn, 'Select Color')

        ax_extract_btn = plt.axes([0.2, 0.05, 0.12, 0.04])
        extract_button = Button(ax_extract_btn, 'Extract')

        ax_save_btn = plt.axes([0.35, 0.05, 0.12, 0.04])
        save_button = Button(ax_save_btn, 'Save Results')

        ax_help_btn = plt.axes([0.5, 0.05, 0.1, 0.04])
        help_button = Button(ax_help_btn, 'Help')

        def select_color(event):
            root = tk.Tk()
            root.withdraw()
            color = colorchooser.askcolor(title="Select Target Color")
            root.destroy()

            if color[0]:  # If color was selected
                self.selected_color = [int(c) for c in color[0]]
                print(f"Selected color (RGB): {self.selected_color}")
                update_display()

        def show_help(event):
            help_text = """
River Sand Body Extraction Tool - Help

1. SELECT COLOR: Click to choose the target color from the image
   - This opens a color picker dialog
   - Choose colors that represent river sand bodies (usually yellow/bright colors)

2. COLOR TOLERANCE (Slider): Adjusts how similar colors need to be
   - Low values (1-20): Only very similar colors
   - Medium values (20-50): Moderately similar colors  
   - High values (50-100): Wide range of similar colors

3. MIN AREA (Slider): Filters out small objects
   - Low values (10-100): Keep small features
   - High values (100-1000): Only keep large river channels

4. EXTRACT: Process the image with current settings

5. SAVE RESULTS: Save the extracted polygons and images
   - Creates polygon file with real-world coordinates
   - Saves result images and data files

Tips:
- Start with medium tolerance (30-50)
- Adjust min area to remove noise
- Try different colors if results aren't good
            """
            messagebox.showinfo("Help", help_text)

        def update_display():
            if self.selected_color is None:
                return

            # Update parameters
            self.color_tolerance = tolerance_slider.val
            self.min_area = area_slider.val

            # Create color mask
            mask = self.create_color_mask(self.selected_color, self.color_tolerance)
            self.current_mask = mask

            # Update mask display
            mask_plot.set_array(mask)
            mask_plot.set_clim(0, 255)

            # Extract contours
            contours = self.extract_contours(mask, self.min_area)
            self.contours = self.process_contours_for_rivers(contours)

            # Display contours
            contour_img = self.image.copy()
            cv2.drawContours(contour_img, self.contours, -1, (255, 0, 0), 2)
            axes[1, 0].clear()
            axes[1, 0].imshow(contour_img)
            axes[1, 0].set_title(f'Extracted Contours ({len(self.contours)} found)')
            axes[1, 0].axis('off')

            # Create result image
            result_img = np.zeros_like(self.image)
            cv2.drawContours(result_img, self.contours, -1, (255, 255, 0), -1)  # Fill
            cv2.drawContours(result_img, self.contours, -1, (255, 0, 0), 2)  # Outline

            axes[1, 1].clear()
            axes[1, 1].imshow(result_img)
            axes[1, 1].set_title('River Sand Bodies')
            axes[1, 1].axis('off')

            plt.draw()

        def extract_contours_callback(event):
            update_display()

        def save_result(event):
            if self.contours is None or self.current_mask is None:
                messagebox.showwarning("Warning", "Please extract contours first!")
                return

            root = tk.Tk()
            root.withdraw()
            save_path = filedialog.asksaveasfilename(
                title="Save Results",
                defaultextension=".png",
                filetypes=[("PNG files", "*.png"), ("JPEG files", "*.jpg")]
            )
            root.destroy()

            if save_path:
                # Create result image
                result_img = np.zeros_like(self.image)
                cv2.drawContours(result_img, self.contours, -1, (255, 255, 0), -1)
                cv2.drawContours(result_img, self.contours, -1, (255, 0, 0), 2)

                # Convert to BGR and save
                result_bgr = cv2.cvtColor(result_img, cv2.COLOR_RGB2BGR)
                cv2.imwrite(save_path, result_bgr)

                # Convert contours to real coordinates
                real_contours = self.contours_to_real_coordinates(self.contours)

                # Save polygon file
                polygon_path = save_path.rsplit('.', 1)[0] + '_polygons.txt'
                self.save_polygons_to_file(real_contours, polygon_path)

                # Save contour data
                contour_data = {
                    'contours': [c.tolist() for c in self.contours],
                    'real_contours': [c.tolist() for c in real_contours],
                    'image_shape': self.image.shape,
                    'selected_color': self.selected_color,
                    'tolerance': self.color_tolerance,
                    'boundary_coords': self.boundary_coords.tolist() if self.boundary_coords is not None else None
                }

                import json
                json_path = save_path.rsplit('.', 1)[0] + '_contours.json'
                with open(json_path, 'w') as f:
                    json.dump(contour_data, f, indent=2)

                print(f"Result image saved to: {save_path}")
                print(f"Polygon file saved to: {polygon_path}")
                print(f"Contour data saved to: {json_path}")

                messagebox.showinfo("Save Successful",
                                    f"Files saved:\nImage: {os.path.basename(save_path)}\nPolygon: {os.path.basename(polygon_path)}\nData: {os.path.basename(json_path)}")

        # Connect events
        color_button.on_clicked(select_color)
        extract_button.on_clicked(extract_contours_callback)
        save_button.on_clicked(save_result)
        help_button.on_clicked(show_help)
        tolerance_slider.on_changed(lambda val: update_display())
        area_slider.on_changed(lambda val: update_display())

        plt.show()

    def batch_process_with_color(self, target_color, tolerance=30, min_area=100):
        """使用指定颜色批量处理"""
        if self.image is None:
            print("请先加载图像!")
            return None

        self.selected_color = target_color
        self.color_tolerance = tolerance
        self.min_area = min_area

        # 创建掩码和提取轮廓
        mask = self.create_color_mask(target_color, tolerance)
        contours = self.extract_contours(mask, min_area)
        processed_contours = self.process_contours_for_rivers(contours)

        return processed_contours

    def show_results(self, contours):
        """显示提取结果"""
        if self.image is None or contours is None:
            return

        fig, axes = plt.subplots(1, 3, figsize=(15, 5))

        # 原始图像
        axes[0].imshow(self.image)
        axes[0].set_title('原始图像')
        axes[0].axis('off')

        # 轮廓图像
        contour_img = self.image.copy()
        cv2.drawContours(contour_img, contours, -1, (255, 0, 0), 2)
        axes[1].imshow(contour_img)
        axes[1].set_title(f'提取的轮廓 (共{len(contours)}个)')
        axes[1].axis('off')

        # 结果图像
        result_img = np.zeros_like(self.image)
        cv2.drawContours(result_img, contours, -1, (255, 255, 0), -1)
        cv2.drawContours(result_img, contours, -1, (255, 0, 0), 2)
        axes[2].imshow(result_img)
        axes[2].set_title('河道砂体结果')
        axes[2].axis('off')

        plt.tight_layout()
        plt.show()


# 使用示例 - 直接处理你的图像
def process_specific_image():
    extractor = RiverSandExtractor()

    # 直接加载指定的图像
    image_path = r"C:\Users\<USER>\Desktop\111\Mishrif_facies_map_20250522\river.jpg"

    print(f"尝试加载图像: {image_path}")
    print(f"文件是否存在: {os.path.exists(image_path)}")

    if os.path.exists(image_path):
        try:
            # 使用中文路径兼容的方式读取图像
            import numpy as np

            # 方法1: 使用numpy读取二进制数据
            with open(image_path, 'rb') as f:
                image_data = f.read()

            # 转换为numpy数组
            nparr = np.frombuffer(image_data, np.uint8)

            # 使用cv2.imdecode解码
            extractor.original_image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)

            if extractor.original_image is None:
                print("错误: 无法解码图像文件")
                return

            extractor.image = cv2.cvtColor(extractor.original_image, cv2.COLOR_BGR2RGB)
            print(f"图像加载成功")
            print(f"图像尺寸: {extractor.image.shape}")

            # 加载边界坐标并计算转换矩阵
            extractor.load_boundary_coordinates()
            extractor.calculate_transform_matrix()

            print("启动交互式颜色选择工具...")
            extractor.interactive_color_selection()

        except Exception as e:
            print(f"读取图像时出错: {e}")
            print("请尝试以下解决方案:")
            print("1. 检查文件是否损坏")
            print("2. 将图像文件移到不包含中文的路径")
            print("3. 使用方法2手动选择文件")
    else:
        print(f"图像文件不存在: {image_path}")
        print("请检查文件路径是否正确")
        print("当前可能的问题:")
        print("1. 文件路径拼写错误")
        print("2. 文件被移动或删除")
        print("3. 权限不足")

        # 提供手动选择文件的选项
        choice = input("\n要手动选择文件吗? (y/n): ")
        if choice.lower() == 'y':
            if extractor.load_image():
                print("图像加载成功!")
                print("启动交互式颜色选择工具...")
                extractor.interactive_color_selection()


# 使用示例
def main():
    extractor = RiverSandExtractor()

    # 加载图像
    if extractor.load_image():
        print("图像加载成功!")
        print("启动交互式颜色选择工具...")
        extractor.interactive_color_selection()
    else:
        print("未选择图像文件")


if __name__ == "__main__":
    # 可以选择使用哪种方式
    print("河道砂体提取工具")
    print("=================")
    choice = input("选择运行方式:\n1 - 处理指定图像\n2 - 选择图像文件\n3 - 快速测试(使用预设颜色)\n请输入 1、2 或 3: ")

    if choice == "1":
        process_specific_image()
    elif choice == "3":
        print("运行快速测试...")
        quick_example()
    else:
        main()


# 快速使用示例（如果你已知目标颜色）
def quick_example():
    extractor = RiverSandExtractor()

    # 直接加载指定图像
    image_path = r"C:\Users\<USER>\Desktop\111\Mishrif_facies_map_20250522\河道.jpg"

    if os.path.exists(image_path):
        extractor.original_image = cv2.imread(image_path)
        extractor.image = cv2.cvtColor(extractor.original_image, cv2.COLOR_BGR2RGB)

        # 加载边界坐标并计算转换矩阵
        extractor.load_boundary_coordinates()
        extractor.calculate_transform_matrix()

        # 使用预定义的黄色进行提取
        yellow_color = [255, 255, 0]  # RGB黄色
        contours = extractor.batch_process_with_color(
            target_color=yellow_color,
            tolerance=50,
            min_area=200
        )

        if contours:
            print(f"提取到 {len(contours)} 个河道砂体轮廓")

            # 转换为实际坐标
            real_contours = extractor.contours_to_real_coordinates(contours)

            # 保存polygon文件
            output_path = r"C:\Users\<USER>\Desktop\111\Mishrif_facies_map_20250522\river_polygons.txt"
            extractor.save_polygons_to_file(real_contours, output_path)

            # 显示结果
            extractor.show_results(contours)
        else:
            print("未找到符合条件的轮廓")
    else:
        print(f"图像文件不存在: {image_path}")

# 如果你想直接运行快速示例，取消下面的注释
# quick_example()
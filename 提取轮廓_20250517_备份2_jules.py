import cv2
import numpy as np
import os
import datetime

# IMPORTANT USER CONFIGURATION NOTES:
# To extract specific colored features like river channels, you MUST:
# 1. Set TARGET_COLOR = None (further below in '图像处理参数'). This is already done by default.
# 2. Accurately define the TARGET_COLOR_HSV_LOWER and TARGET_COLOR_HSV_UPPER ranges (in '颜色提取参数')
#    to match the color of the river channels in YOUR specific images.
#    The default values are for generic light grayish features and are LIKELY NOT OPTIMAL.
#    Use an image editor (GIMP, Photoshop, etc.) to find the HSV values of your target features.
# Failure to set correct HSV values will likely result in poor or no extraction of internal features.
# ======================================================================================================================
# ======================================================================================================================
# 配置部分：用户根据需要修改这些参数
# ======================================================================================================================

# ---------------- 输入文件路径设置 ----------------
# 输入图片文件夹路径 或 单张图片文件路径
# INPUT_PATH = r"D:\待处理图片"
INPUT_PATH = r"C:\Users\<USER>\Desktop\111\Mishrif_facies_map_20250522\river.jpg"

# ---------------- 输出文件路径设置 ----------------
# 输出结果图片文件夹路径
# OUTPUT_FOLDER_PATH = r"D:\输出图片"
OUTPUT_FOLDER_PATH = r"C:\Users\<USER>\Desktop\111\Mishrif_facies_map_20250522"
# 输出结果excel文件路径
EXCEL_OUTPUT_PATH = r"C:\Users\<USER>\Desktop\111\Mishrif_facies_map_20250522\统计结果.xlsx"

# ---------------- 图像处理参数 ----------------
# 是否统一所有图片的检测阈值，是则使用下面给定的统一阈值，否则自适应计算每个图的阈值
UNIFIED_THRESHOLD = False
# UNIFIED_THRESHOLD = 100  # 示例统一阈值，根据需要调整

# 目标颜色，用于提取特定颜色的轮廓 (e.g., "orange", "blue").
# 设置为 None (TARGET_COLOR = None) 以使用下面定义的 TARGET_COLOR_HSV_LOWER 和 TARGET_COLOR_HSV_UPPER 变量进行自定义颜色提取。
# 如果 TARGET_COLOR 设置为颜色名称字符串 (e.g., "orange")，则脚本会尝试使用预定义的HSV范围 (COLOR_HSV_RANGES)。
# TARGET_COLOR = "orange" # Example, set to None to use custom HSV below
TARGET_COLOR = None

# ===================== 颜色提取参数 =====================
# 定义要提取的目标颜色的HSV范围的下限和上限
# 用户应根据实际需要提取的颜色调整这些值。
# 可以使用图像编辑软件（如GIMP, Photoshop）的颜色拾取器工具，
# 在HSV模式下获取目标颜色区域的H, S, V值范围。
# H: 色相 (0-179 for OpenCV), S: 饱和度 (0-255), V: 明度 (0-255)
TARGET_COLOR_HSV_LOWER = [0, 0, 150]  # Default for light grayish features - USER MUST TUNE
TARGET_COLOR_HSV_UPPER = [180, 50, 255]  # Default for light grayish features - USER MUST TUNE

# 是否进行高斯模糊
GAUSSIAN_BLUR = True
# 高斯模糊参数
GAUSSIAN_BLUR_KERNEL_SIZE = (5, 5)  # 模糊核大小，必须是正奇数
GAUSSIAN_BLUR_SIGMA_X = 0  # X方向的标准差，0表示自动计算

# 轮廓查找模式
# cv2.RETR_EXTERNAL: 只检测外轮廓
# cv2.RETR_LIST: 检测所有轮廓，不建立等级关系
# cv2.RETR_CCOMP: 检测所有轮廓，并建立两层等级关系（外轮廓和内轮廓）
# cv2.RETR_TREE: 检测所有轮廓，并建立完整的等级树关系
CONTOUR_RETRIEVAL_MODE = cv2.RETR_EXTERNAL

# 轮廓逼近方法
# cv2.CHAIN_APPROX_NONE: 存储所有轮廓点
# cv2.CHAIN_APPROX_SIMPLE: 压缩水平、垂直和对角线段，只存储它们的端点
CONTOUR_APPROXIMATION_METHOD = cv2.CHAIN_APPROX_SIMPLE

# 最小轮廓面积阈值（用于过滤小轮廓）
MIN_CONTOUR_AREA = 200  # 根据图像和目标调整 (e.g., for river channels, this may need tuning based on image scale and channel width)

# 轮廓线宽
CONTOUR_LINE_THICKNESS = 2  # 设置为0则填充轮廓

# 轮廓颜色 (B, G, R)
CONTOUR_COLOR = (0, 255, 0)  # 绿色

# ---------------- 输出控制参数 ----------------
# 是否在图片上绘制所有轮廓
DRAW_ALL_CONTOURS = True

# 是否在图片上绘制符合面积要求的轮廓
DRAW_FILTERED_CONTOURS = True

# 是否在图片上标记轮廓中心
MARK_CONTOUR_CENTERS = False

# 是否在图片上标记轮廓的包围矩形
MARK_BOUNDING_RECTANGLES = False

# 是否在图片上显示轮廓面积
SHOW_CONTOUR_AREA = False  # 如果设置为True，会在轮廓旁边显示面积文字

# ---------------- Debugging Parameters ----------------
# Set to True to save the intermediate color mask for debugging color segmentation
DEBUG_SAVE_COLOR_MASK = True

# 目标颜色，用于提取特定颜色的轮廓，如 "orange", "blue", "green" 等
# 如果设置为 None，则不按颜色提取，而是提取所有轮廓
# 注意：TARGET_COLOR 的优先级高于下面的 HSV 范围设置。
# 如果 TARGET_COLOR 不是 None，则脚本会尝试使用预定义的颜色名称（如 "orange", "blue"）对应的 HSV 范围。
# 只有当 TARGET_COLOR 设置为 None 时，脚本才会使用下面定义的 TARGET_COLOR_HSV_LOWER 和 TARGET_COLOR_HSV_UPPER。


# ======================================================================================================================
# 核心功能函数：用户通常不需要修改这部分代码
# ======================================================================================================================

# 定义颜色名称到HSV范围的映射字典
# 注意：这些范围是示例，可能需要根据实际应用场景进行调整
COLOR_HSV_RANGES = {
    'red': ([0, 100, 100], [10, 255, 255]),  # 红色也可能在160-179
    'orange': ([11, 100, 100], [25, 255, 255]),  # 橙色
    'yellow': ([26, 100, 100], [35, 255, 255]),  # 黄色
    'green': ([36, 50, 50], [89, 255, 255]),  # 绿色
    'blue': ([90, 50, 50], [128, 255, 255]),  # 蓝色
    'purple': ([129, 50, 50], [155, 255, 255]),  # 紫色
    'pink': ([156, 50, 50], [170, 255, 255]),  # 粉色
    # 对于黑色、白色、灰色等，主要调整V值，S值通常较低
    'black': ([0, 0, 0], [180, 255, 30]),  # 黑色
    'white': ([0, 0, 200], [180, 30, 255]),  # 白色
    'gray': ([0, 0, 50], [180, 50, 200])  # 灰色
}


def get_target_hsv_range(color_name=None, lower_hsv=None, upper_hsv=None):
    """
    根据颜色名称或直接指定的HSV范围返回目标颜色的HSV下限和上限。
    优先使用 color_name。如果 color_name 为 None 或不在预定义中，则使用 lower_hsv 和 upper_hsv。
    """
    if color_name and color_name.lower() in COLOR_HSV_RANGES:
        print(f"使用预定义颜色 '{color_name}' 的HSV范围。")
        return COLOR_HSV_RANGES[color_name.lower()]
    elif lower_hsv is not None and upper_hsv is not None:
        print(f"使用用户自定义的HSV范围: Lower={lower_hsv}, Upper={upper_hsv}。")
        return np.array(lower_hsv), np.array(upper_hsv)
    else:
        print("警告: 未指定有效的颜色名称或HSV范围。将不进行颜色过滤。")
        return None, None


def process_image(image_path, output_folder_path):
    """
    处理单张图片：读取图片，查找轮廓，绘制轮廓，并保存结果。
    """
    try:
        # 读取图片
        img = cv2.imread(image_path)
        if img is None:
            print(f"错误: 无法读取图片 {image_path}")
            return None, 0, 0, 0

        # 转换到HSV色彩空间
        hsv_img = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)

        # 根据TARGET_COLOR获取HSV范围
        # 脚本更新：优先使用 TARGET_COLOR（如果不是 None），否则使用 TARGET_COLOR_HSV_LOWER 和 TARGET_COLOR_HSV_UPPER
        lower_hsv = None
        upper_hsv = None
        if TARGET_COLOR and TARGET_COLOR.lower() != 'none':
            if TARGET_COLOR.lower() in COLOR_HSV_RANGES:
                lower_hsv, upper_hsv = COLOR_HSV_RANGES[TARGET_COLOR.lower()]
                lower_hsv = np.array(lower_hsv)
                upper_hsv = np.array(upper_hsv)
                print(f"正在使用预定义颜色 '{TARGET_COLOR}' (HSV Lower: {lower_hsv}, Upper: {upper_hsv}) 进行颜色提取。")
            else:
                print(f"警告: 预定义的颜色 '{TARGET_COLOR}' 未在 COLOR_HSV_RANGES 中找到。将不进行颜色过滤。")
                # 不进行颜色过滤，处理整个灰度图
                gray_img_for_threshold = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        elif hasattr(TARGET_COLOR_HSV_LOWER, '__iter__') and hasattr(TARGET_COLOR_HSV_UPPER, '__iter__'):
            lower_hsv = np.array(TARGET_COLOR_HSV_LOWER)
            upper_hsv = np.array(TARGET_COLOR_HSV_UPPER)
            print(f"正在使用自定义HSV范围 (Lower: {lower_hsv}, Upper: {upper_hsv}) 进行颜色提取。")
        else:
            print("未指定颜色提取参数，将处理整个灰度图像。")
            gray_img_for_threshold = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

        # 颜色提取（如果指定了有效的HSV范围）
        mask = None
        if lower_hsv is not None and upper_hsv is not None:
            mask = cv2.inRange(hsv_img, lower_hsv, upper_hsv)
            if DEBUG_SAVE_COLOR_MASK and mask is not None:
                try:
                    mask_filename = f"{os.path.splitext(os.path.basename(image_path))[0]}_debug_mask.png"
                    mask_output_path = os.path.join(output_folder_path, "debug_masks")  # Save in a subfolder
                    if not os.path.exists(mask_output_path):
                        os.makedirs(mask_output_path)
                    cv2.imwrite(os.path.join(mask_output_path, mask_filename), mask)
                    print(f"DEBUG: Saved color mask to {os.path.join(mask_output_path, mask_filename)}")
                except Exception as e_mask_save:
                    print(f"Warning: Could not save debug mask. Error: {e_mask_save}")

            # Check the quality of the generated mask if color extraction was attempted
            if lower_hsv is not None and upper_hsv is not None and mask is not None:  # Check if color extraction was active
                non_zero_pixels = cv2.countNonZero(mask)
                total_pixels = mask.shape[0] * mask.shape[1]
                percentage_non_zero = (non_zero_pixels / total_pixels) * 100

                # Define thresholds for a 'meaningful' mask
                # These thresholds might need tuning.
                min_percentage_threshold = 0.01  # e.g., at least 0.01% of pixels should be part of the mask
                max_percentage_threshold = 99.0  # e.g., if mask is almost entirely white, it might also be problematic

                if non_zero_pixels == 0:
                    print(f"WARNING: Color mask for {os.path.basename(image_path)} is completely empty (all black). " +
                          "The specified HSV range likely did not match any colors in the image. " +
                          "This may lead to no contours found or unintended border extraction.")
                elif percentage_non_zero < min_percentage_threshold:
                    print(
                        f"WARNING: Color mask for {os.path.basename(image_path)} has very few foreground pixels ({percentage_non_zero:.4f}%). " +
                        "The specified HSV range may be too restrictive or incorrect. " +
                        "This may lead to poor/no feature extraction or unintended border extraction.")
                elif percentage_non_zero > max_percentage_threshold:
                    print(
                        f"WARNING: Color mask for {os.path.basename(image_path)} is almost entirely foreground ({percentage_non_zero:.4f}%). " +
                        "The specified HSV range may be too broad. " +
                        "This could lead to the entire image or large parts of it being selected.")
                # No specific action to change gray_img_for_threshold yet, just warnings.
                # The script will proceed with the current mask.

            # Optionally, apply morphology to clean up the mask
            # kernel = np.ones((5,5),np.uint8)
            # mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)
            # mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)

            # 使用掩码处理原始图像的灰度版本或者直接在掩码上找轮廓
            # gray_img_for_threshold = cv2.bitwise_and(cv2.cvtColor(img, cv2.COLOR_BGR2GRAY), cv2.cvtColor(img, cv2.COLOR_BGR2GRAY), mask=mask)
            # 为了简化，我们直接在二值掩码上找轮廓，或者在一个处理过的灰度图上
            # 如果颜色提取后，我们希望在原始图像的灰度版本上（在颜色区域内）进行阈值处理和轮廓查找：
            # targeted_gray_img = cv2.bitwise_and(cv2.cvtColor(img, cv2.COLOR_BGR2GRAY), cv2.cvtColor(img, cv2.COLOR_BGR2GRAY), mask=mask)
            # gray_img_for_threshold = targeted_gray_img
            # 或者，直接使用mask作为轮廓查找的输入（它已经是二值的了）
            gray_img_for_threshold = mask  # 直接在颜色掩码上找轮廓

        else:  # 如果没有指定颜色，则使用整个图像的灰度版本
            gray_img_for_threshold = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

        # 高斯模糊
        if GAUSSIAN_BLUR:
            blurred_img = cv2.GaussianBlur(gray_img_for_threshold, GAUSSIAN_BLUR_KERNEL_SIZE, GAUSSIAN_BLUR_SIGMA_X)
        else:
            blurred_img = gray_img_for_threshold  # 如果不模糊，则使用原始灰度图（或颜色提取后的结果）

        # 阈值处理
        if UNIFIED_THRESHOLD is not False:  # 使用统一阈值
            _, thresh_img = cv2.threshold(blurred_img, UNIFIED_THRESHOLD, 255, cv2.THRESH_BINARY)
            print(f"使用统一阈值: {UNIFIED_THRESHOLD}")
        else:  # 自适应阈值
            # _, thresh_img = cv2.threshold(blurred_img, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            # print(f"使用Otsu自适应阈值计算得到的阈值为: {cv2.threshold(blurred_img, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)[0]}")
            # 使用三角法自适应阈值，对特征较少的图像可能更好
            threshold_value, thresh_img = cv2.threshold(blurred_img, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_TRIANGLE)
            print(f"使用Triangle自适应阈值计算得到的阈值为: {threshold_value}")

        # 查找轮廓
        contours, hierarchy = cv2.findContours(thresh_img, CONTOUR_RETRIEVAL_MODE, CONTOUR_APPROXIMATION_METHOD)

        # 创建输出图像副本
        output_img = img.copy()

        # 绘制所有找到的轮廓 (如果开启)
        if DRAW_ALL_CONTOURS:
            cv2.drawContours(output_img, contours, -1, (255, 0, 0), 1)  # 用蓝色细线绘制所有轮廓

        # 筛选并绘制轮廓
        filtered_contours = []
        total_filtered_area = 0
        num_filtered_contours = 0

        for contour in contours:
            area = cv2.contourArea(contour)
            if area >= MIN_CONTOUR_AREA:
                filtered_contours.append(contour)
                total_filtered_area += area
                num_filtered_contours += 1

                if DRAW_FILTERED_CONTOURS:
                    cv2.drawContours(output_img, [contour], -1, CONTOUR_COLOR, CONTOUR_LINE_THICKNESS)

                if MARK_CONTOUR_CENTERS:
                    M = cv2.moments(contour)
                    if M["m00"] != 0:
                        cX = int(M["m10"] / M["m00"])
                        cY = int(M["m01"] / M["m00"])
                        cv2.circle(output_img, (cX, cY), 3, (0, 0, 255), -1)  # 红色小圆点标记中心

                if MARK_BOUNDING_RECTANGLES:
                    x, y, w, h = cv2.boundingRect(contour)
                    cv2.rectangle(output_img, (x, y), (x + w, y + h), (0, 0, 255), 1)  # 红色细线矩形

                if SHOW_CONTOUR_AREA:
                    # 在轮廓旁边显示面积，避免遮挡，选择一个合适的位置
                    x, y, w, h = cv2.boundingRect(contour)
                    cv2.putText(output_img, f"{area:.0f}", (x + w // 2, y + h // 2),
                                cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1, cv2.LINE_AA)

        # 文件名处理
        base_name = os.path.basename(image_path)
        name, ext = os.path.splitext(base_name)
        # 构建输出文件名，添加时间戳确保唯一性
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S_%f")  # 包含毫秒
        output_filename = f"{name}_contours_{timestamp}{ext}"
        output_image_path = os.path.join(output_folder_path, output_filename)

        # 保存处理后的图片
        if not os.path.exists(output_folder_path):
            os.makedirs(output_folder_path)
        cv2.imwrite(output_image_path, output_img)
        print(f"处理完成: {image_path} -> {output_image_path}")

        return output_image_path, len(contours), num_filtered_contours, total_filtered_area

    except Exception as e:
        print(f"处理图片 {image_path} 时发生错误: {e}")
        return None, 0, 0, 0


def main():
    """
    主函数：遍历输入路径中的所有图片，进行处理。
    """
    # 检查输入路径是文件夹还是文件
    if os.path.isdir(INPUT_PATH):
        image_files = [os.path.join(INPUT_PATH, f) for f in os.listdir(INPUT_PATH)
                       if f.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.tif', '.tiff'))]
    elif os.path.isfile(INPUT_PATH):
        image_files = [INPUT_PATH]
    else:
        print(f"错误: 输入路径 {INPUT_PATH} 无效。")
        return

    if not image_files:
        print(f"在 {INPUT_PATH} 中没有找到图片文件。")
        return

    # 创建输出文件夹（如果不存在）
    if not os.path.exists(OUTPUT_FOLDER_PATH):
        os.makedirs(OUTPUT_FOLDER_PATH)
        print(f"创建输出文件夹: {OUTPUT_FOLDER_PATH}")

    # 准备Excel数据存储
    results_data = []

    # 更新：在循环外获取一次颜色提取参数
    # global TARGET_COLOR_HSV_LOWER, TARGET_COLOR_HSV_UPPER # 声明为全局变量以便在 process_image 中使用
    # if TARGET_COLOR and TARGET_COLOR.lower() != 'none':
    #     # 如果 TARGET_COLOR 是一个颜色名称，尝试从字典获取
    #     if TARGET_COLOR.lower() in COLOR_HSV_RANGES:
    #         TARGET_COLOR_HSV_LOWER_val, TARGET_COLOR_HSV_UPPER_val = COLOR_HSV_RANGES[TARGET_COLOR.lower()]
    #         # 将list转换为numpy array，如果需要的话（取决于process_image如何使用它们）
    #         # TARGET_COLOR_HSV_LOWER = np.array(TARGET_COLOR_HSV_LOWER_val)
    #         # TARGET_COLOR_HSV_UPPER = np.array(TARGET_COLOR_HSV_UPPER_val)
    #         print(f"将使用预定义颜色 '{TARGET_COLOR}' 进行提取。")
    #     else:
    #         print(f"警告: 颜色 '{TARGET_COLOR}' 不在预定义列表中。将不按颜色提取，除非已设置 TARGET_COLOR_HSV_LOWER/UPPER。")
    #         # TARGET_COLOR_HSV_LOWER = None #确保如果颜色名称无效，则不使用旧的HSV值
    #         # TARGET_COLOR_HSV_UPPER = None
    # elif 'TARGET_COLOR_HSV_LOWER' in globals() and 'TARGET_COLOR_HSV_UPPER' in globals():
    #      print(f"将使用用户在脚本中直接定义的 HSV 范围进行提取: L={TARGET_COLOR_HSV_LOWER}, U={TARGET_COLOR_HSV_UPPER}")
    # else:
    #     print("TARGET_COLOR 未设置或设置为 'none'，并且未提供 TARGET_COLOR_HSV_LOWER/UPPER。将不进行颜色特异性提取。")
    #     # TARGET_COLOR_HSV_LOWER = None
    #     # TARGET_COLOR_HSV_UPPER = None

    for image_file in image_files:
        print(f"\n正在处理图片: {image_file}")
        processed_image_path, total_contours, filtered_contours_count, total_filtered_area = \
            process_image(image_file, OUTPUT_FOLDER_PATH)

        if processed_image_path:
            results_data.append({
                "原始文件": os.path.basename(image_file),
                "处理后文件": os.path.basename(processed_image_path),
                "找到总轮廓数": total_contours,
                "符合条件轮廓数": filtered_contours_count,
                "符合条件轮廓总面积": total_filtered_area
            })

    # 保存统计结果到Excel (如果安装了pandas 和 openpyxl)
    try:
        import pandas as pd
        df = pd.DataFrame(results_data)
        df.to_excel(EXCEL_OUTPUT_PATH, index=False)
        print(f"\n统计结果已保存到: {EXCEL_OUTPUT_PATH}")
    except ImportError:
        print("\n警告: 未安装 pandas 或 openpyxl。无法保存Excel统计结果。")
        print("请运行 'pip install pandas openpyxl' 来安装这些库。")
    except Exception as e:
        print(f"\n保存Excel文件时发生错误: {e}")


if __name__ == "__main__":
    class Args:
        def __init__(self):
            # self.color = TARGET_COLOR # This line is intentionally removed/not present as per instructions
            self.target_hsv_lower = TARGET_COLOR_HSV_LOWER
            self.target_hsv_upper = TARGET_COLOR_HSV_UPPER


    # The rest of this block remains unchanged and uses global variables.
    # No instance of Args is created or used here as per the strict subtask interpretation.

    # 欢迎信息和参数概览
    print("=" * 80)
    print("欢迎使用轮廓检测脚本！")
    print("=" * 80)
    print("当前配置参数概览:")
    print(f"  输入路径: {INPUT_PATH}")
    print(f"  输出文件夹: {OUTPUT_FOLDER_PATH}")
    print(f"  Excel输出路径: {EXCEL_OUTPUT_PATH}")
    print(
        f"  统一阈值: {'启用, 值为 ' + str(UNIFIED_THRESHOLD) if UNIFIED_THRESHOLD is not False else '禁用, 使用自适应阈值'}")

    # 颜色提取逻辑的概览
    color_extraction_method = "不进行颜色提取 (处理整个灰度图)"
    if 'TARGET_COLOR' in globals() and TARGET_COLOR and TARGET_COLOR.lower() != 'none':  # Check if global TARGET_COLOR is defined and not None/empty
        color_extraction_method = f"按预定义颜色名称 '{TARGET_COLOR}' 提取"
    elif 'TARGET_COLOR_HSV_LOWER' in globals() and 'TARGET_COLOR_HSV_UPPER' in globals():
        color_extraction_method = f"按自定义HSV范围提取 (L: {TARGET_COLOR_HSV_LOWER}, U: {TARGET_COLOR_HSV_UPPER})"
    print(f"  颜色提取: {color_extraction_method}")

    print(
        f"  高斯模糊: {'启用 (Kernel: ' + str(GAUSSIAN_BLUR_KERNEL_SIZE) + ', SigmaX: ' + str(GAUSSIAN_BLUR_SIGMA_X) + ')' if GAUSSIAN_BLUR else '禁用'}")
    print(f"  轮廓查找模式: {CONTOUR_RETRIEVAL_MODE} (参考 OpenCV 文档了解详情)")
    print(f"  轮廓逼近方法: {CONTOUR_APPROXIMATION_METHOD} (参考 OpenCV 文档了解详情)")
    print(f"  最小轮廓面积: {MIN_CONTOUR_AREA}")
    print(f"  绘制所有轮廓: {DRAW_ALL_CONTOURS}")
    print(f"  绘制筛选后轮廓: {DRAW_FILTERED_CONTOURS} (颜色: {CONTOUR_COLOR}, 线宽: {CONTOUR_LINE_THICKNESS})")
    print(f"  标记轮廓中心: {MARK_CONTOUR_CENTERS}")
    print(f"  标记包围矩形: {MARK_BOUNDING_RECTANGLES}")
    print(f"  显示轮廓面积: {SHOW_CONTOUR_AREA}")
    print("=" * 80)

    main()

    print("\n脚本执行完毕。")

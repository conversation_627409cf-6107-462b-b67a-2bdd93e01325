import sys

def fix_coordinates(input_file, output_file):
    """读取PRN文件中的坐标，反转Y轴坐标，然后保存"""
    try:
        # 首先读取所有坐标以找到Y的最大值和最小值
        coordinates = []
        min_y = float('inf')
        max_y = float('-inf')
        
        with open(input_file, 'r', encoding='utf-8-sig') as f:
            for line in f:
                parts = line.strip().split()
                if len(parts) >= 4:  # 确保行包含足够的数据
                    try:
                        x = float(parts[0])
                        y = float(parts[1])
                        min_y = min(min_y, y)
                        max_y = max(max_y, y)
                        coordinates.append((x, y, parts[2], parts[3]))
                    except ValueError:
                        continue

        print(f"Y坐标范围：{min_y} 到 {max_y}")

        # 写入修复后的坐标
        with open(output_file, 'w', encoding='utf-8-sig') as f:
            for x, y, contour_id, fixed_value in coordinates:
                # 反转Y坐标
                new_y = max_y - (y - min_y)
                # 保持原有的格式对齐
                line = f"{str(x).rjust(12)} {str(new_y).rjust(12)} {contour_id.rjust(8)} {fixed_value.rjust(8)}\n"
                f.write(line)

        print(f"坐标修复完成！新文件已保存到：{output_file}")
        return True

    except Exception as e:
        print(f"处理文件时出错：{e}")
        return False

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("使用方法：python fix_coordinates.py input.prn output.prn")
        sys.exit(1)

    input_file = sys.argv[1]
    output_file = sys.argv[2]
    
    fix_coordinates(input_file, output_file)

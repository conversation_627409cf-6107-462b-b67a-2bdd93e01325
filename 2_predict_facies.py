import os
import pandas as pd
import numpy as np
import joblib
import logging
from datetime import datetime

# 日志设置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f"facies_prediction_batch_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log", encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("批量岩相预测")

# 特征工程函数（含强制生成关键特征）
def feature_engineering(df, feature_cols, depth_col='DEPTH'):
    extended_df = df.copy()

    # 强制生成关键特征
    if 'DT' in df.columns and 'GR' in df.columns:
        extended_df['DT_DIV_GR'] = df['DT'] / df['GR'].replace(0, 1e-10)

    if 'GR' in df.columns and 'NEU' in df.columns:
        extended_df['GR_DIV_NEU'] = df['GR'] / df['NEU'].replace(0, 1e-10)

    if 'DEN' in df.columns and 'NEU' in df.columns:
        extended_df['DEN_DIV_NEU'] = df['DEN'] / df['NEU'].replace(0, 1e-10)

    if 'DEN' in df.columns and 'DT' in df.columns:
        extended_df['DEN_DIV_DT'] = df['DEN'] / df['DT'].replace(0, 1e-10)

    if depth_col in df.columns:
        for feat in feature_cols:
            extended_df[f'{feat}_DEPTH_NORM'] = extended_df[feat] / extended_df[depth_col]

    # 自动生成高相关性比值特征
    corr_matrix = df[feature_cols].corr().abs()
    upper_tri = corr_matrix.where(np.triu(np.ones(corr_matrix.shape), k=1).astype(bool))
    high_corr_pairs = [(upper_tri.index[i], upper_tri.columns[j])
                       for i, j in zip(*np.where(upper_tri > 0.5))]

    for feat1, feat2 in high_corr_pairs[:10]:
        new_name = f'{feat1}_DIV_{feat2}'
        if new_name not in extended_df.columns:
            extended_df[new_name] = df[feat1] / df[feat2].replace(0, 1e-10)

    key_features = feature_cols[:min(5, len(feature_cols))]
    for feat in key_features:
        for window in [3, 5]:
            extended_df[f'{feat}_ROLL_MEAN_{window}'] = extended_df[feat].rolling(window=window, center=True).mean()
            extended_df[f'{feat}_ROLL_STD_{window}'] = extended_df[feat].rolling(window=window, center=True).std()
            extended_df[f'{feat}_TREND_{window}'] = extended_df[feat].diff(window).apply(
                lambda x: 1 if x > 0 else (-1 if x < 0 else 0))

    extended_df = extended_df.bfill().ffill()
    new_features = [col for col in extended_df.columns if col not in df.columns and col != depth_col]
    return extended_df, feature_cols + new_features

# 加载模型函数
def load_model_with_metadata(model_path):
    if not os.path.exists(model_path):
        raise FileNotFoundError(f"模型文件不存在: {model_path}")
    content = joblib.load(model_path)
    if 'model' not in content or 'metadata' not in content:
        raise ValueError("模型文件必须包含 'model' 和 'metadata'")
    return content['model'], content['metadata']

# 主程序
if __name__ == "__main__":
    model_path = r'D:\LQ_2024\MJN_2024\est_cv_facies_model.pkl'
    input_path = r'D:\LQ_2024\MJN_2024\沉积相\Liqiang0501\Liqiang0501'
    output_dir = os.path.join(input_path, '预测结果_PRN')
    os.makedirs(output_dir, exist_ok=True)

    print(f"📦 加载模型: {model_path}")
    try:
        model, metadata = load_model_with_metadata(model_path)
        original_features = [col.replace(' ', '') for col in metadata['original_features']]
        selected_features = [col.replace(' ', '') for col in metadata['selected_features']]

        csv_files = [f for f in os.listdir(input_path) if f.lower().endswith('.csv')]
        logger.info(f"📁 发现 {len(csv_files)} 个CSV文件")

        for filename in csv_files:
            filepath = os.path.join(input_path, filename)
            try:
                df = pd.read_csv(filepath)
                df.columns = df.columns.str.replace(' ', '')  # 清理列名空格

                if 'Facies' in df.columns:
                    df = df.drop(columns='Facies')

                if not all(col in df.columns for col in original_features):
                    missing = [col for col in original_features if col not in df.columns]
                    raise ValueError(f"⚠️ 缺失原始特征列: {missing}")

                df_ext, _ = feature_engineering(df, original_features)

                if not all(col in df_ext.columns for col in selected_features):
                    missing = [col for col in selected_features if col not in df_ext.columns]
                    raise ValueError(f"⚠️ 缺失扩展特征列: {missing}")

                X_pred = df_ext[selected_features]
                df_ext['Predicted_Facies'] = model.predict(X_pred)

                # 只保留 DEPTH 和预测结果，并格式化
                result_df = df_ext[['DEPTH', 'Predicted_Facies']].copy()
                result_df['DEPTH'] = result_df['DEPTH'].round(2)  # 可调精度
                result_df['Predicted_Facies'] = result_df['Predicted_Facies'].astype(int)

                # 写入 .prn 文件（列宽40，右对齐）
                prn_lines = []
                for _, row in result_df.iterrows():
                    line = f"{str(row['DEPTH']).rjust(40)}{str(row['Predicted_Facies']).rjust(40)}"
                    prn_lines.append(line)

                prn_name = os.path.splitext(filename)[0] + '.prn'
                prn_path = os.path.join(output_dir, prn_name)
                with open(prn_path, 'w', encoding='utf-8') as f:
                    f.write('\n'.join(prn_lines))

                logger.info(f"✅ 处理成功: {filename} -> {prn_name}")
            except Exception as fe:
                logger.error(f"❌ 处理失败: {filename} | 错误: {fe}")

        print(f"🎉 全部处理完成！PRN 文件已保存至: {output_dir}")
    except Exception as e:
        print(f"❌ 总体错误: {str(e)}")

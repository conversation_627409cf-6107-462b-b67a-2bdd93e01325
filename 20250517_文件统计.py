#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
文件统计工具 - 用于分析D:\LQ_2025\Saha目录结构中的文件
采用简化的方法统计井数据
"""

import os
import pathlib
import csv
import re
from collections import defaultdict
from datetime import datetime

# 根目录路径
ROOT_DIR = r"D:\LQ_2025\Saha"

# 文件类型分类
FILE_CATEGORIES = {
    'seismic': ['.sgy', '.segy', '.su', '.seg'],
    'well_logs': ['.las', '.lis', '.dlis'],
    'documents': ['.pdf', '.doc', '.docx', '.ppt', '.pptx', '.xls', '.xlsx'],
    'images': ['.jpg', '.jpeg', '.png', '.tif', '.tiff', '.gif', '.bmp'],
    'text': ['.txt', '.csv', '.dat', '.asc'],
    'gis': ['.shp', '.shx', '.dbf', '.prj', '.kml', '.kmz'],
    'interpretation': ['.zmap', '.grd', '.xyz'],
    'project': ['.prj', '.proj', '.petrel', '.opendtect'],
    'compressed': ['.zip', '.rar', '.7z', '.tar', '.gz'],
}

# 井名识别正则表达式
WELL_NAME_PATTERNS = [
    r'(?:MJ|SY|SH|SA)-\d+(?:-\d+-\d+[A-Z]+)?',  # 匹配 MJ-10, SY-65, SY-051-1-01V 等
    r'[A-Z]+-\d+[A-Z]*',      # 匹配 WELL-123, WELL-123A 等
    r'[A-Z]+\d+[A-Z]*',       # 匹配 WELL123, WELL123A 等
]

def get_file_category(file_path):
    """根据文件扩展名确定文件类别"""
    ext = pathlib.Path(file_path).suffix.lower()

    # 检查文件是否属于预定义类别
    for category, extensions in FILE_CATEGORIES.items():
        if ext in extensions:
            return category

    # 如果没有匹配的类别，则返回扩展名作为类别
    if ext:
        return f"other_{ext[1:]}"  # 去掉点号
    else:
        return "unknown"

def extract_well_name(file_name):
    """从文件名中提取井名"""
    for pattern in WELL_NAME_PATTERNS:
        match = re.search(pattern, file_name)
        if match:
            return match.group(0)
    return None

def is_well_folder(folder_name):
    """判断文件夹名称是否为井名"""
    # 检查是否匹配井名模式
    for pattern in WELL_NAME_PATTERNS:
        if re.match(pattern, folder_name):
            return True

    # 特殊情况：SY后面跟数字，但没有连字符
    if re.match(r'SY\d+', folder_name):
        return True

    return False

def extract_well_name_from_log_file(file_name):
    """从测井文件名中提取井名，特别处理如 SY-051-1-01V_RHOB_Logs.las 格式"""
    # 尝试匹配 SY-051-1-01V 格式
    match = re.search(r'((?:MJ|SY|SH|SA)-\d+(?:-\d+-\d+[A-Z]+)?)', file_name)
    if match:
        return match.group(1)

    # 尝试匹配其他格式
    return extract_well_name(file_name)

def extract_well_name_from_petrel_tops(file_content):
    """从Petrel well tops文件内容中提取井名"""
    well_names = set()

    # 检查是否是Petrel well tops格式
    if "# Petrel well tops" in file_content:
        # 直接查找所有井名，格式如 "SY-001-1-01V"
        matches = re.findall(r'"(SY-\d+-\d+-\d+[A-Z]+)"', file_content)
        for well_name in matches:
            well_names.add(well_name)

        # 也查找其他可能的井名格式
        other_matches = re.findall(r'"([^"]+)"[^"]*"(SY[^"]+)"', file_content)
        for match in other_matches:
            well_name = match[1]
            if is_well_folder(well_name) or extract_well_name(well_name):
                well_names.add(well_name)

    return well_names

def analyze_simple_structure(root_dir=ROOT_DIR):
    """使用简化方法分析目录结构，特别关注井数据"""
    # 初始化数据结构
    structure = {
        'total_files': 0,
        'total_dirs': 0,
        'main_categories': defaultdict(int),  # 主要目录类别计数
        'main_category_files': defaultdict(lambda: defaultdict(int)),  # 主要目录下的文件类型统计
        'wells': {
            'total': set(),  # 所有井名集合
            'by_category': defaultdict(set),  # 按数据类别统计井
            'by_data_type': defaultdict(set),  # 按数据类型统计井
            'by_extension': defaultdict(set),  # 按文件扩展名统计井
            'with_markers': set(),  # 有井标记数据的井
        }
    }

    print(f"开始分析目录结构: {root_dir}")

    # 获取一级目录
    main_categories = []
    for item in os.listdir(root_dir):
        item_path = os.path.join(root_dir, item)
        if os.path.isdir(item_path):
            main_categories.append(item)

    # 处理每个一级目录
    for category in main_categories:
        category_path = os.path.join(root_dir, category)

        # 检查是否有二级目录
        has_subdirs = False
        for item in os.listdir(category_path):
            if os.path.isdir(os.path.join(category_path, item)):
                has_subdirs = True
                break

        if not has_subdirs:
            # 如果没有二级目录，统计文件类型和个数
            file_count = 0
            for item in os.listdir(category_path):
                item_path = os.path.join(category_path, item)
                if os.path.isfile(item_path):
                    file_count += 1
                    ext = os.path.splitext(item)[1].lower()
                    file_category = get_file_category(item_path)
                    structure['main_category_files'][category][file_category] += 1

            structure['main_categories'][category] = file_count
            structure['total_files'] += file_count
        else:
            # 如果有二级目录，处理特殊情况

            # 特殊处理 VSP 目录 - 统计二级目录作为井
            if category == 'VSP':
                well_count = 0
                for item in os.listdir(category_path):
                    item_path = os.path.join(category_path, item)
                    if os.path.isdir(item_path) and is_well_folder(item):
                        well_count += 1
                        structure['wells']['total'].add(item)
                        structure['wells']['by_category']['VSP'].add(item)
                        structure['wells']['by_data_type']['VSP'].add(item)

                structure['main_categories'][category] = well_count
                structure['total_dirs'] += well_count

            # 特殊处理 Well_data 目录
            elif category == 'Well_data':
                # 处理 Well_data/VSP 目录
                vsp_path = os.path.join(category_path, 'VSP')
                if os.path.exists(vsp_path) and os.path.isdir(vsp_path):
                    well_count = 0
                    for item in os.listdir(vsp_path):
                        item_path = os.path.join(vsp_path, item)
                        if os.path.isdir(item_path) and is_well_folder(item):
                            well_count += 1
                            structure['wells']['total'].add(item)
                            structure['wells']['by_category']['Well_VSP'].add(item)
                            structure['wells']['by_data_type']['Well_data/VSP'].add(item)

                    structure['main_categories']['Well_data/VSP'] = well_count
                    structure['total_dirs'] += well_count

                # 处理 Well_data/Logs 目录
                logs_path = os.path.join(category_path, 'Logs')
                if os.path.exists(logs_path) and os.path.isdir(logs_path):
                    # 处理 Density 和 Sonic_Intp 子目录
                    for log_type in ['Density', 'Sonic_Intp']:
                        log_type_path = os.path.join(logs_path, log_type)
                        if os.path.exists(log_type_path) and os.path.isdir(log_type_path):
                            file_count = 0
                            wells_with_logs = set()

                            for item in os.listdir(log_type_path):
                                item_path = os.path.join(log_type_path, item)
                                if os.path.isfile(item_path):
                                    file_count += 1
                                    ext = os.path.splitext(item)[1].lower()
                                    file_category = get_file_category(item_path)

                                    # 从文件名提取井名
                                    well_name = extract_well_name_from_log_file(item)
                                    if well_name:
                                        wells_with_logs.add(well_name)
                                        structure['wells']['total'].add(well_name)
                                        structure['wells']['by_category'][file_category].add(well_name)
                                        structure['wells']['by_extension'][ext].add(well_name)
                                        structure['wells']['by_data_type'][f'Well_data/Logs/{log_type}'].add(well_name)

                            structure['main_categories'][f'Well_data/Logs/{log_type}'] = file_count
                            structure['total_files'] += file_count

                # 处理 Well_data/Deviation file 目录
                dev_path = os.path.join(category_path, 'Deviation file')
                if os.path.exists(dev_path) and os.path.isdir(dev_path):
                    file_count = 0
                    wells_with_dev = set()

                    for item in os.listdir(dev_path):
                        item_path = os.path.join(dev_path, item)
                        if os.path.isfile(item_path):
                            file_count += 1
                            ext = os.path.splitext(item)[1].lower()
                            file_category = get_file_category(item_path)

                            # 从文件名提取井名
                            well_name = extract_well_name_from_log_file(item)
                            if well_name:
                                wells_with_dev.add(well_name)
                                structure['wells']['total'].add(well_name)
                                structure['wells']['by_category'][file_category].add(well_name)
                                structure['wells']['by_extension'][ext].add(well_name)
                                structure['wells']['by_data_type']['Well_data/Deviation file'].add(well_name)

                    structure['main_categories']['Well_data/Deviation file'] = file_count
                    structure['total_files'] += file_count

                # 处理 Well_data/Well markers 目录
                markers_path = os.path.join(category_path, 'Well markers')
                if os.path.exists(markers_path) and os.path.isdir(markers_path):
                    file_count = 0

                    for item in os.listdir(markers_path):
                        item_path = os.path.join(markers_path, item)
                        if os.path.isfile(item_path):
                            file_count += 1

                            # 读取文件内容
                            try:
                                print(f"正在处理井标记文件: {item_path}")
                                with open(item_path, 'r', encoding='utf-8', errors='ignore') as f:
                                    file_content = f.read()
                                    well_names = extract_well_name_from_petrel_tops(file_content)

                                    print(f"从文件中提取到 {len(well_names)} 个井名")

                                    # 将找到的井名添加到统计中
                                    for well in well_names:
                                        structure['wells']['total'].add(well)
                                        structure['wells']['with_markers'].add(well)
                                        structure['wells']['by_data_type']['Well_data/Well markers'].add(well)
                            except Exception as e:
                                print(f"读取文件时出错 {item_path}: {e}")

                    structure['main_categories']['Well_data/Well markers'] = file_count
                    structure['total_files'] += file_count

            # 其他一级目录，递归统计文件
            else:
                file_count = 0
                dir_count = 0

                for root, dirs, files in os.walk(os.path.join(root_dir, category)):
                    dir_count += len(dirs)
                    file_count += len(files)

                    for file in files:
                        file_path = os.path.join(root, file)
                        ext = os.path.splitext(file)[1].lower()
                        file_category = get_file_category(file_path)
                        structure['main_category_files'][category][file_category] += 1

                structure['main_categories'][category] = file_count
                structure['total_files'] += file_count
                structure['total_dirs'] += dir_count

    return structure

def get_size_category(size_bytes):
    """根据文件大小返回大小类别"""
    kb = 1024
    mb = kb * 1024
    gb = mb * 1024

    if size_bytes < kb:
        return "< 1KB"
    elif size_bytes < 10 * kb:
        return "1KB - 10KB"
    elif size_bytes < 100 * kb:
        return "10KB - 100KB"
    elif size_bytes < mb:
        return "100KB - 1MB"
    elif size_bytes < 10 * mb:
        return "1MB - 10MB"
    elif size_bytes < 100 * mb:
        return "10MB - 100MB"
    elif size_bytes < gb:
        return "100MB - 1GB"
    else:
        return "> 1GB"

def format_size(size_bytes):
    """将字节数格式化为人类可读的形式"""
    kb = 1024
    mb = kb * 1024
    gb = mb * 1024

    if size_bytes < kb:
        return f"{size_bytes} B"
    elif size_bytes < mb:
        return f"{size_bytes/kb:.2f} KB"
    elif size_bytes < gb:
        return f"{size_bytes/mb:.2f} MB"
    else:
        return f"{size_bytes/gb:.2f} GB"

def print_simple_structure(structure):
    """打印简化的目录结构统计信息"""
    print("\n" + "="*50)
    print(f"目录结构分析报告 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*50)

    print(f"\n总文件数: {structure['total_files']}")
    print(f"总目录数: {structure['total_dirs']}")

    # 打印主要目录类别
    print("\n主要目录类别:")
    print("-"*30)
    for category, count in sorted(structure['main_categories'].items(), key=lambda x: x[1], reverse=True):
        percentage = (count / structure['total_files']) * 100 if structure['total_files'] > 0 else 0
        print(f"{category}: {count} {'文件' if not category.startswith('Well_data') and category != 'VSP' else '井'} ({percentage:.1f}%)")

    # 打印井数据统计
    print("\n井数据统计:")
    print("-"*30)
    print(f"总井数: {len(structure['wells']['total'])}")

    # 按数据类型统计井
    print("\n按数据类型统计井数:")
    print("-"*30)
    for data_type, wells in sorted(structure['wells']['by_data_type'].items(), key=lambda x: len(x[1]), reverse=True):
        print(f"{data_type}: {len(wells)} 口井")
        # 如果井数量不多，打印井名列表
        if len(wells) <= 20:
            print(f"  井名列表: {', '.join(sorted(wells))}")

    # 按文件类别统计井
    print("\n按文件类别统计井数:")
    print("-"*30)
    for category, wells in sorted(structure['wells']['by_category'].items(), key=lambda x: len(x[1]), reverse=True):
        print(f"{category}: {len(wells)} 口井")

    # 按文件扩展名统计井
    print("\n按文件扩展名统计井数:")
    print("-"*30)
    for ext, wells in sorted(structure['wells']['by_extension'].items(), key=lambda x: len(x[1]), reverse=True)[:10]:
        print(f"{ext}: {len(wells)} 口井")

def export_simple_structure_to_csv(structure, output_file=os.path.join(ROOT_DIR, "directory_structure.csv")):
    """将简化的目录结构统计信息导出到CSV文件"""
    with open(output_file, 'w', newline='', encoding='utf-8-sig') as csvfile:
        writer = csv.writer(csvfile)

        # 写入标题行
        writer.writerow(['Type', 'Name', 'Count', 'Details'])

        # 写入主要目录类别
        for category, count in sorted(structure['main_categories'].items(), key=lambda x: x[1], reverse=True):
            percentage = (count / structure['total_files']) * 100 if structure['total_files'] > 0 else 0
            writer.writerow(['MainCategory', category, count, f"{percentage:.2f}%"])

        # 写入井数据统计
        writer.writerow(['', '', '', ''])
        writer.writerow(['WellData', 'TotalWells', len(structure['wells']['total']),
                        ', '.join(sorted(structure['wells']['total']))])

        # 按数据类型统计井
        writer.writerow(['', '', '', ''])
        writer.writerow(['WellsByDataType', 'DataType', 'Count', 'Wells'])

        for data_type, wells in sorted(structure['wells']['by_data_type'].items(), key=lambda x: len(x[1]), reverse=True):
            writer.writerow(['WellDataType', data_type, len(wells), ', '.join(sorted(wells))])

        # 按文件类别统计井
        writer.writerow(['', '', '', ''])
        writer.writerow(['WellsByCategory', 'Category', 'Count', 'Wells'])

        for category, wells in sorted(structure['wells']['by_category'].items(), key=lambda x: len(x[1]), reverse=True):
            writer.writerow(['WellCategory', category, len(wells), ', '.join(sorted(wells))])

        # 按文件扩展名统计井
        writer.writerow(['', '', '', ''])
        writer.writerow(['WellsByExtension', 'Extension', 'Count', 'Wells'])

        for ext, wells in sorted(structure['wells']['by_extension'].items(), key=lambda x: len(x[1]), reverse=True):
            writer.writerow(['WellExtension', ext, len(wells), ', '.join(sorted(wells))])

        # 井数据矩阵
        writer.writerow(['', '', '', ''])

        # 确保不包含Well markers，因为我们会单独处理
        data_types_for_header = [dt for dt in structure['wells']['by_data_type'].keys()
                               if dt != 'Well_data/Well markers']

        writer.writerow(['WellDataMatrix', 'Well'] + data_types_for_header + ['Marker'])

        # 限制矩阵大小，避免CSV文件过大
        max_wells = 100
        max_data_types = 10

        wells_to_show = sorted(structure['wells']['total'])[:max_wells]
        data_types_to_show = sorted(structure['wells']['by_data_type'].keys(),
                                  key=lambda x: len(structure['wells']['by_data_type'][x]),
                                  reverse=True)[:max_data_types]

        # 确保不包含Well markers，因为我们会单独处理
        data_types_to_show = [dt for dt in data_types_to_show if dt != 'Well_data/Well markers']

        for well in wells_to_show:
            row = ['WellMatrix', well]
            for data_type in data_types_to_show:
                if well in structure['wells']['by_data_type'][data_type]:
                    row.append('√')
                else:
                    row.append('×')
            # 添加Marker列
            if well in structure['wells']['with_markers']:
                row.append('√')
            else:
                row.append('×')
            writer.writerow(row)

    # 创建专门的井数据CSV
    wells_file = os.path.join(os.path.dirname(output_file), "wells_data.csv")
    with open(wells_file, 'w', newline='', encoding='utf-8-sig') as csvfile:
        writer = csv.writer(csvfile)

        # 写入标题行
        writer.writerow(['WellName', 'DataTypes', 'Categories', 'Extensions', 'HasMarker'])

        # 写入井详细信息
        for well in sorted(structure['wells']['total']):
            # 获取该井的数据类型
            data_types = [dt for dt, wells in structure['wells']['by_data_type'].items() if well in wells]

            # 获取该井的文件类别
            categories = [cat for cat, wells in structure['wells']['by_category'].items() if well in wells]

            # 获取该井的文件扩展名
            extensions = [ext for ext, wells in structure['wells']['by_extension'].items() if well in wells]

            # 检查是否有井标记数据
            has_marker = "是" if well in structure['wells']['with_markers'] else "否"

            writer.writerow([
                well,
                ', '.join(sorted(data_types)),
                ', '.join(sorted(categories)),
                ', '.join(sorted(extensions)),
                has_marker
            ])

    print(f"\n目录结构统计数据已导出到: {output_file}")
    print(f"井数据详细信息已导出到: {wells_file}")

def generate_simple_well_report(structure, output_file=os.path.join(ROOT_DIR, "well_data_report.txt")):
    """生成简化的井数据报告"""
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("="*50 + "\n")
        f.write(f"井数据详细报告 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write("="*50 + "\n\n")

        f.write(f"总井数: {len(structure['wells']['total'])}\n")
        f.write(f"井名列表: {', '.join(sorted(structure['wells']['total']))}\n\n")

        # 按数据类型统计井
        f.write("按数据类型统计井数:\n")
        f.write("-"*30 + "\n")

        # 首先显示有井标记数据的井
        if 'with_markers' in structure['wells'] and structure['wells']['with_markers']:
            f.write(f"\n有井标记数据的井: {len(structure['wells']['with_markers'])} 口井\n")
            if len(structure['wells']['with_markers']) <= 30:
                f.write(f"井名列表: {', '.join(sorted(structure['wells']['with_markers']))}\n")

        # 然后显示其他数据类型
        for data_type, wells in sorted(structure['wells']['by_data_type'].items(), key=lambda x: len(x[1]), reverse=True):
            f.write(f"\n{data_type}: {len(wells)} 口井\n")
            # 如果井数量不多，打印井名列表
            if len(wells) <= 30:
                f.write(f"井名列表: {', '.join(sorted(wells))}\n")

        # 按文件类别统计井
        f.write("\n\n按文件类别统计井数:\n")
        f.write("-"*30 + "\n")
        for category, wells in sorted(structure['wells']['by_category'].items(), key=lambda x: len(x[1]), reverse=True):
            f.write(f"{category}: {len(wells)} 口井\n")
            # 如果井数量不多，打印井名列表
            if len(wells) <= 30:
                f.write(f"井名列表: {', '.join(sorted(wells))}\n\n")

        # 按文件扩展名统计井
        f.write("\n按文件扩展名统计井数:\n")
        f.write("-"*30 + "\n")
        for ext, wells in sorted(structure['wells']['by_extension'].items(), key=lambda x: len(x[1]), reverse=True)[:15]:
            f.write(f"{ext}: {len(wells)} 口井\n")
            # 如果井数量不多，打印井名列表
            if len(wells) <= 30:
                f.write(f"井名列表: {', '.join(sorted(wells))}\n\n")

        # 生成井数据矩阵 - 每口井有哪些类型的数据
        f.write("\n井数据矩阵 - 每口井拥有的数据类型:\n")
        f.write("-"*60 + "\n")

        # 显示所有井，但限制数据类型数量
        max_data_types = 7  # 减少一个，为Marker列留空间

        all_wells = sorted(structure['wells']['total'])  # 不限制井的数量
        data_types = sorted(structure['wells']['by_data_type'].keys(),
                          key=lambda x: len(structure['wells']['by_data_type'][x]),
                          reverse=True)[:max_data_types]

        # 确保不包含Well markers，因为我们会单独处理
        data_types = [dt for dt in data_types if dt != 'Well_data/Well markers']

        # 表头
        f.write("井名".ljust(15))
        for data_type in data_types:
            type_name = data_type.split('/')[-1] if '/' in data_type else data_type
            f.write(f"{type_name[:10].ljust(10)}")
        # 添加Marker列
        f.write("Marker".ljust(10))
        f.write("\n")
        f.write("-"*15)
        f.write("-"*10 * len(data_types))
        f.write("-"*10)  # Marker列的分隔线
        f.write("\n")

        # 每口井的数据
        for well in all_wells:
            f.write(f"{well.ljust(15)}")
            for data_type in data_types:
                if well in structure['wells']['by_data_type'][data_type]:
                    f.write("√".center(10))
                else:
                    f.write("×".center(10))
            # 添加Marker列
            if well in structure['wells']['with_markers']:
                f.write("√".center(10))
            else:
                f.write("×".center(10))
            f.write("\n")

        # 不再需要注释，因为我们显示所有井

    print(f"\n井数据详细报告已保存到: {output_file}")

def main():
    """主函数"""
    print("目录结构分析工具启动...")

    # 分析目录结构
    structure = analyze_simple_structure()

    # 打印目录结构统计信息
    print_simple_structure(structure)

    # 导出目录结构统计信息到CSV
    export_simple_structure_to_csv(structure)

    # 生成井数据详细报告
    generate_simple_well_report(structure)

    print("\n目录结构分析完成!")

if __name__ == "__main__":
    main()
import pandas as pd
import numpy as np
import os
import tempfile
from datetime import datetime
from scipy.spatial import cKDTree
from scipy.interpolate import griddata, NearestNDInterpolator
from scipy.ndimage import median_filter, gaussian_filter
from scipy.signal import savgol_filter
from skimage import restoration
import matplotlib.pyplot as plt


def remove_outliers(df, value_cols, threshold=3.0):
    """
    去除异常值

    参数:
    - df: 输入DataFrame
    - value_cols: 需要处理的值列名列表
    - threshold: Z-score阈值，超过此值的点被视为异常值

    返回:
    - 处理后的DataFrame
    """
    print(f"去除异常值，Z-score阈值: {threshold}")
    result_df = df.copy()

    for col in value_cols:
        if col in df.columns:
            # 计算Z-score
            mean = df[col].mean()
            std = df[col].std()
            z_scores = np.abs((df[col] - mean) / std)

            # 标记异常值
            outliers = z_scores > threshold
            outlier_count = outliers.sum()
            print(f"列 {col} 中检测到 {outlier_count} 个异常值 (总计 {len(df)} 行)")

            if outlier_count > 0:
                # 将异常值替换为NaN
                result_df.loc[outliers, col] = np.nan

    return result_df


def fill_missing_values(df, value_cols, method='linear'):
    """
    填充缺失值

    参数:
    - df: 输入DataFrame
    - value_cols: 需要处理的值列名列表
    - method: 插值方法 ('linear', 'nearest', 'cubic')

    返回:
    - 处理后的DataFrame
    """
    print(f"使用 {method} 方法填充缺失值")
    result_df = df.copy()

    for col in value_cols:
        if col in df.columns:
            # 检查是否有缺失值
            missing_count = df[col].isna().sum()
            if missing_count > 0:
                print(f"列 {col} 中填充 {missing_count} 个缺失值")
                # 使用插值填充缺失值
                result_df[col] = result_df[col].interpolate(method=method)

                # 处理边缘缺失值
                result_df[col] = result_df[col].fillna(method='ffill')
                result_df[col] = result_df[col].fillna(method='bfill')

    return result_df


def smooth_data(df, value_cols, method='gaussian', window_size=5, sigma=1.0):
    """
    平滑数据

    参数:
    - df: 输入DataFrame
    - value_cols: 需要处理的值列名列表
    - method: 平滑方法 ('gaussian', 'median', 'savgol')
    - window_size: 窗口大小
    - sigma: 高斯滤波器的标准差

    返回:
    - 处理后的DataFrame
    """
    print(f"使用 {method} 方法平滑数据")
    result_df = df.copy()

    for col in value_cols:
        if col in df.columns:
            values = df[col].values

            if method == 'gaussian':
                # 高斯滤波
                smoothed = gaussian_filter(values, sigma=sigma)
            elif method == 'median':
                # 中值滤波
                smoothed = median_filter(values, size=window_size)
            elif method == 'savgol':
                # Savitzky-Golay滤波
                # 确保窗口大小是奇数
                if window_size % 2 == 0:
                    window_size += 1
                # 多项式阶数不能大于窗口大小
                poly_order = min(3, window_size - 1)
                smoothed = savgol_filter(values, window_size, poly_order)
            else:
                smoothed = values

            result_df[col] = smoothed

    return result_df


def enhance_channel_continuity(df, x_col, y_col, value_cols, grid_size=100,
                              smoothing_factor=0.7, bilateral_sigma_color=0.1,
                              bilateral_sigma_spatial=1.0, max_points=500000):
    """
    增强河道连续性

    参数:
    - df: 输入DataFrame
    - x_col, y_col: 坐标列名
    - value_cols: 需要处理的值列名列表
    - grid_size: 网格大小
    - smoothing_factor: 平滑因子
    - bilateral_sigma_color: 双边滤波颜色标准差
    - bilateral_sigma_spatial: 双边滤波空间标准差
    - max_points: 处理的最大点数，超过此数量将进行采样

    返回:
    - 处理后的DataFrame
    """
    print("增强河道连续性...")
    result_df = df.copy()

    # 检查数据点数量
    if len(df) == 0:
        print("警告: 输入数据为空")
        return result_df

    # 如果数据点太多，进行采样
    if len(df) > max_points:
        print(f"数据点数量 ({len(df)}) 超过最大处理点数 ({max_points})，进行采样...")
        # 使用系统采样而不是随机采样，以保持空间分布
        sample_step = len(df) // max_points + 1
        sample_indices = np.arange(0, len(df), sample_step)
        sample_df = df.iloc[sample_indices].copy()
        print(f"采样后数据点数量: {len(sample_df)}")
    else:
        sample_df = df

    # 获取坐标范围
    x_min, x_max = sample_df[x_col].min(), sample_df[x_col].max()
    y_min, y_max = sample_df[y_col].min(), sample_df[y_col].max()

    # 调整网格大小以避免内存问题
    if grid_size > 500:
        print(f"网格大小 ({grid_size}) 过大，调整为 500")
        grid_size = 500

    # 创建规则网格
    x_grid = np.linspace(x_min, x_max, grid_size)
    y_grid = np.linspace(y_min, y_max, grid_size)
    X, Y = np.meshgrid(x_grid, y_grid)

    enhanced_values = {}

    for col in value_cols:
        if col in sample_df.columns:
            print(f"处理列: {col}")

            # 提取坐标和值
            points = sample_df[[x_col, y_col]].values
            values = sample_df[col].values

            # 检查是否有NaN值
            if np.isnan(values).any():
                print(f"警告: 列 {col} 中有 {np.isnan(values).sum()} 个NaN值，将被忽略")
                valid_mask = ~np.isnan(values)
                points = points[valid_mask]
                values = values[valid_mask]

                if len(points) == 0:
                    print(f"错误: 列 {col} 中没有有效值，跳过处理")
                    continue

            try:
                print(f"将数据插值到 {grid_size}x{grid_size} 网格...")
                # 使用griddata插值到规则网格
                grid_values = griddata(points, values, (X, Y), method='linear')

                # 首先填充NaN值以避免处理错误
                mask = np.isnan(grid_values)
                if mask.any():
                    print(f"网格中有 {mask.sum()} 个NaN值，使用最近邻插值填充...")
                    # 使用最近邻填充NaN
                    valid_mask = ~mask
                    if valid_mask.any():  # 确保有有效值
                        xy_valid = np.column_stack([X[valid_mask], Y[valid_mask]])
                        values_valid = grid_values[valid_mask]

                        # 使用最近邻插值器
                        try:
                            interpolator = NearestNDInterpolator(xy_valid, values_valid)
                            xy_nan = np.column_stack([X[mask], Y[mask]])
                            grid_values[mask] = interpolator(xy_nan)
                        except Exception as e:
                            print(f"最近邻插值失败: {str(e)}，使用常数填充")
                            grid_values[mask] = np.nanmean(grid_values)

                # 应用高斯平滑预处理
                print("应用高斯平滑...")
                grid_values = gaussian_filter(grid_values, sigma=1.0)

                # 应用双边滤波
                print("应用双边滤波增强河道连续性...")
                # 确保数据在有效范围内
                grid_min, grid_max = np.nanmin(grid_values), np.nanmax(grid_values)
                normalized_grid = (grid_values - grid_min) / (grid_max - grid_min)

                enhanced_grid = restoration.denoise_bilateral(
                    normalized_grid.astype(np.float64),
                    sigma_color=bilateral_sigma_color,
                    sigma_spatial=bilateral_sigma_spatial
                )

                # 恢复原始范围
                enhanced_grid = enhanced_grid * (grid_max - grid_min) + grid_min

                print("将增强后的值插值回原始点...")
                # 将增强后的值插值回原始点
                all_points = df[[x_col, y_col]].values
                enhanced_values[col] = griddata(
                    (X.flatten(), Y.flatten()),
                    enhanced_grid.flatten(),
                    all_points,
                    method='linear'
                )

                # 处理可能的NaN值
                mask = np.isnan(enhanced_values[col])
                if mask.any():
                    print(f"警告: 增强后有 {mask.sum()} 个NaN值，使用原始值填充")
                    original_values = df[col].values
                    enhanced_values[col][mask] = original_values[mask]

                # 更新DataFrame
                result_df[col] = enhanced_values[col]
                print(f"列 {col} 处理完成")

            except MemoryError:
                print(f"处理列 {col} 时内存不足，跳过增强处理")
                print("建议减小网格大小或增加系统内存")

            except Exception as e:
                print(f"增强河道连续性时出错: {str(e)}")
                print("保持原始值不变")

    return result_df


def visualize_enhancement(original_df, enhanced_df, x_col, y_col, value_col,
                         output_path=None, title="河道增强效果对比"):
    """
    可视化增强效果

    参数:
    - original_df: 原始DataFrame
    - enhanced_df: 增强后的DataFrame
    - x_col, y_col: 坐标列名
    - value_col: 值列名
    - output_path: 输出图像路径
    - title: 图像标题
    """
    print("生成增强效果对比图...")

    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))

    # 绘制原始数据
    sc1 = ax1.scatter(original_df[x_col], original_df[y_col],
                     c=original_df[value_col], cmap='viridis',
                     s=1, alpha=0.8)
    ax1.set_title("原始数据")
    ax1.set_xlabel(x_col)
    ax1.set_ylabel(y_col)
    fig.colorbar(sc1, ax=ax1, label=value_col)

    # 绘制增强后的数据
    sc2 = ax2.scatter(enhanced_df[x_col], enhanced_df[y_col],
                     c=enhanced_df[value_col], cmap='viridis',
                     s=1, alpha=0.8)
    ax2.set_title("增强后数据")
    ax2.set_xlabel(x_col)
    ax2.set_ylabel(y_col)
    fig.colorbar(sc2, ax=ax2, label=value_col)

    plt.suptitle(title)
    plt.tight_layout()

    if output_path:
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        print(f"对比图已保存到: {output_path}")

    plt.close()


def save_to_prn(df, output_path, column_width=20):
    """
    将DataFrame保存为PRN格式文件

    参数:
    - df: 要保存的DataFrame
    - output_path: 输出文件路径
    - column_width: 每列的宽度(默认20)
    """
    print(f"保存数据到PRN文件: {output_path}")

    # 获取列名
    columns = df.columns

    # 创建表头行
    header_line = ""
    for col in columns:
        # 确保列名不超过列宽度，否则会导致列名合并
        col_str = str(col)
        if len(col_str) > column_width - 1:
            col_str = col_str[:column_width - 4] + "..."
        header_line += col_str.rjust(column_width)

    # 创建数据行
    data_lines = []
    for _, row in df.iterrows():
        line = ""
        for col in columns:
            # 对数值进行格式化，保留4位小数
            if pd.api.types.is_numeric_dtype(df[col]):
                value = f"{row[col]:.4f}" if not pd.isna(row[col]) else ""
            else:
                value = str(row[col]) if not pd.isna(row[col]) else ""
            # 确保值不超过列宽度
            if len(value) > column_width - 1:
                value = value[:column_width - 4] + "..."
            line += value.rjust(column_width)
        data_lines.append(line)

    # 写入文件
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(header_line + '\n')
        f.write('\n'.join(data_lines))

    print(f"PRN文件已保存: {output_path}")
    return output_path





def calculate_statistics(file_path, chunk_size=100000):
    """
    分块计算大文件的统计数据(用于后续标准化)
    返回第三列(attrvalue)的最小值、最大值、总和、平方和、行数
    """
    print(f"计算文件统计信息: {file_path}")
    count = 0
    sum_val = 0
    sum_sq = 0
    min_val = float('inf')
    max_val = float('-inf')

    # 分块读取并计算统计量
    for chunk in pd.read_csv(file_path, delim_whitespace=True, header=None, comment='#',
                             names=['x', 'y', 'attrvalue'], chunksize=chunk_size):
        # 确保数据类型正确
        chunk['x'] = pd.to_numeric(chunk['x'], errors='coerce')
        chunk['y'] = pd.to_numeric(chunk['y'], errors='coerce')
        chunk['attrvalue'] = pd.to_numeric(chunk['attrvalue'], errors='coerce')

        # 删除无效数据
        chunk = chunk.dropna()

        count += len(chunk)
        chunk_sum = chunk['attrvalue'].sum()
        chunk_sum_sq = (chunk['attrvalue'] ** 2).sum()
        chunk_min = chunk['attrvalue'].min()
        chunk_max = chunk['attrvalue'].max()

        sum_val += chunk_sum
        sum_sq += chunk_sum_sq
        min_val = min(min_val, chunk_min)
        max_val = max(max_val, chunk_max)

        # 打印进度
        if count % (chunk_size * 10) == 0:
            print(f"已处理 {count} 行...")

    # 计算均值和标准差
    mean = sum_val / count
    var = (sum_sq / count) - (mean ** 2)
    std = np.sqrt(var)

    stats = {
        'count': count,
        'mean': mean,
        'std': std,
        'min': min_val,
        'max': max_val
    }

    print(f"文件统计信息: 行数={count}, 均值={mean:.4f}, 标准差={std:.4f}, 最小值={min_val:.4f}, 最大值={max_val:.4f}")
    return stats


def normalize_chunk(chunk, stats, normalize_method='minmax', stats2=None):
    """
    标准化数据块

    参数:
    - chunk: 要标准化的数据块
    - stats: 第一个属性的统计信息
    - normalize_method: 标准化方法，'zscore'或'minmax'
    - stats2: 第二个属性的统计信息（可选）
    """
    # 确保列名正确
    if 'attrvalue' in chunk.columns:
        if normalize_method == 'zscore':
            # Z-score标准化
            chunk['normalized_attrvalue'] = (chunk['attrvalue'] - stats['mean']) / stats['std']
        else:
            # Min-Max标准化(默认)
            chunk['normalized_attrvalue'] = (chunk['attrvalue'] - stats['min']) / (stats['max'] - stats['min'])

    # 如果使用了attrvalue1, attrvalue2等列名
    if 'attrvalue1' in chunk.columns:
        if normalize_method == 'zscore':
            chunk['normalized_attrvalue1'] = (chunk['attrvalue1'] - stats['mean']) / stats['std']
        else:
            chunk['normalized_attrvalue1'] = (chunk['attrvalue1'] - stats['min']) / (stats['max'] - stats['min'])

    # 如果有第二个属性和对应的统计信息
    if 'attrvalue2' in chunk.columns and stats2 is not None:
        if normalize_method == 'zscore':
            chunk['normalized_attrvalue2'] = (chunk['attrvalue2'] - stats2['mean']) / stats2['std']
        else:
            chunk['normalized_attrvalue2'] = (chunk['attrvalue2'] - stats2['min']) / (stats2['max'] - stats2['min'])

    return chunk


def process_and_merge_different_coordinates(file1_path, file2_path, output_path,
                                            merge_strategy='all', tolerance=0.001,
                                            chunk_size=100000, normalize_method='minmax'):
    """
    处理并合并坐标不完全相同的两个大文件

    参数:
    - file1_path, file2_path: 输入文件路径
    - output_path: 输出文件路径
    - merge_strategy: 合并策略
        'all': 保留所有坐标点
        'common': 只保留共同坐标点
        'interpolate': 对缺失点进行插值
    - tolerance: 坐标匹配容差
    - chunk_size: 每次处理的数据块大小
    - normalize_method: 标准化方法，'zscore'或'minmax'
    """
    start_time = datetime.now()
    print(f"开始处理，时间: {start_time}")
    print(f"合并策略: {merge_strategy}, 坐标容差: {tolerance}")

    # 计算两个文件的统计信息用于标准化
    stats1 = calculate_statistics(file1_path, chunk_size)
    stats2 = calculate_statistics(file2_path, chunk_size)

    # 根据所选策略处理数据
    if merge_strategy == 'common':
        # 策略1: 只保留共同坐标
        merge_common_coordinates(file1_path, file2_path, output_path, stats1, stats2,
                                 tolerance, chunk_size, normalize_method)
    elif merge_strategy == 'interpolate':
        # 策略2: 对缺失点进行插值
        merge_with_interpolation(file1_path, file2_path, output_path, stats1, stats2,
                                 tolerance, chunk_size, normalize_method)
    else:
        # 策略3 (默认): 保留所有点
        merge_all_coordinates(file1_path, file2_path, output_path, stats1, stats2,
                              chunk_size, normalize_method)

    end_time = datetime.now()
    processing_time = end_time - start_time
    print(f"处理完成！结果已保存到 {output_path}")
    print(f"总处理时间: {processing_time}")

    # 获取输出文件大小
    output_size = os.path.getsize(output_path) / (1024 * 1024)  # 转换为MB
    print(f"输出文件大小: {output_size:.2f} MB")


def merge_all_coordinates(file1_path, file2_path, output_path, stats1, stats2,
                          chunk_size=100000, normalize_method='minmax'):
    """保留所有坐标点的合并策略 - 使用索引记录已出现的点"""
    print("使用'全部保留'策略合并文件...")

    # 创建临时文件存储中间结果
    with tempfile.NamedTemporaryFile(delete=False, suffix='.csv') as temp_file1:
        temp_path1 = temp_file1.name

    # 标准化第一个文件并写入临时文件
    print(f"标准化第一个文件: {file1_path}")
    with open(temp_path1, 'w') as f:
        f.write('x,y,attrvalue,normalized_attrvalue,source\n')

    processed_rows = 0
    for chunk in pd.read_csv(file1_path, delim_whitespace=True, header=None, comment='#',
                             names=['x', 'y', 'attrvalue'], chunksize=chunk_size):
        # 确保数据类型正确
        chunk['x'] = pd.to_numeric(chunk['x'], errors='coerce')
        chunk['y'] = pd.to_numeric(chunk['y'], errors='coerce')
        chunk['attrvalue'] = pd.to_numeric(chunk['attrvalue'], errors='coerce')

        # 删除无效数据
        chunk = chunk.dropna()
        chunk = normalize_chunk(chunk, stats1, normalize_method, stats2)
        chunk['source'] = 1  # 标记来源
        chunk.to_csv(temp_path1, mode='a', header=False, index=False)

        processed_rows += len(chunk)
        if processed_rows % (chunk_size * 10) == 0:
            print(f"已处理文件1的 {processed_rows} 行...")

    # 标准化第二个文件并写入最终文件
    print(f"标准化第二个文件: {file2_path}")

    # 判断是否需要保存为PRN格式
    if output_path.lower().endswith('.prn'):
        # 读取临时CSV文件并转换为PRN格式
        temp_df = pd.read_csv(temp_path1)
        save_to_prn(temp_df, output_path)
    else:
        # 复制临时文件到输出文件
        with open(temp_path1, 'r') as src, open(output_path, 'w') as dst:
            dst.write(src.read())

    # 使用集合跟踪已有坐标
    print("开始跟踪坐标和合并...")

    # 创建坐标记录文件(只存储坐标用于查询)
    with tempfile.NamedTemporaryFile(delete=False, suffix='.csv') as coords_file:
        coords_path = coords_file.name

    with open(coords_path, 'w') as f:
        f.write('x,y\n')

    # 从第一个文件提取坐标
    for chunk in pd.read_csv(file1_path, delim_whitespace=True, header=None, comment='#',
                             names=['x', 'y', 'attrvalue'], chunksize=chunk_size):
        # 确保数据类型正确
        chunk['x'] = pd.to_numeric(chunk['x'], errors='coerce')
        chunk['y'] = pd.to_numeric(chunk['y'], errors='coerce')
        chunk['attrvalue'] = pd.to_numeric(chunk['attrvalue'], errors='coerce')

        # 删除无效数据
        chunk = chunk.dropna()
        chunk[['x', 'y']].to_csv(coords_path, mode='a', header=False, index=False)

    # 读取所有坐标到内存(只包含x和y，内存占用较小)
    existing_coords = pd.read_csv(coords_path)[['x', 'y']]

    # 为了加速查找，创建一个坐标元组集合
    coord_set = set(zip(existing_coords['x'], existing_coords['y']))

    # 处理第二个文件
    processed_rows = 0
    for chunk in pd.read_csv(file2_path, delim_whitespace=True, header=None, comment='#',
                             names=['x', 'y', 'attrvalue'], chunksize=chunk_size):
        # 确保数据类型正确
        chunk['x'] = pd.to_numeric(chunk['x'], errors='coerce')
        chunk['y'] = pd.to_numeric(chunk['y'], errors='coerce')
        chunk['attrvalue'] = pd.to_numeric(chunk['attrvalue'], errors='coerce')

        # 删除无效数据
        chunk = chunk.dropna()

        # 找出新坐标点
        new_points = []
        for idx, row in chunk.iterrows():
            if (row['x'], row['y']) not in coord_set:
                new_points.append(idx)

        if new_points:
            new_chunk = chunk.loc[new_points].copy()
            new_chunk = normalize_chunk(new_chunk, stats2, normalize_method, stats1)
            new_chunk['source'] = 2  # 标记来源

            # 判断是否为PRN格式
            if output_path.lower().endswith('.prn'):
                # 对于PRN格式，我们需要先收集所有数据，然后一次性写入
                # 这里先写入CSV，最后再转换
                new_chunk.to_csv(output_path, mode='a', header=False, index=False)
            else:
                # 直接追加到CSV文件
                new_chunk.to_csv(output_path, mode='a', header=False, index=False)

        processed_rows += len(chunk)
        if processed_rows % (chunk_size * 10) == 0:
            print(f"已处理文件2的 {processed_rows} 行...")

    # 清理临时文件
    os.unlink(temp_path1)
    os.unlink(coords_path)


def merge_common_coordinates(file1_path, file2_path, output_path, stats1, stats2,
                             tolerance=0.001, chunk_size=100000, normalize_method='minmax'):
    """只保留共同坐标的合并策略"""
    print("使用'仅共同点'策略合并文件...")

    # 创建KD-Tree来加速坐标匹配
    print("创建坐标索引...")

    # 临时存储第一个文件的所有坐标用于构建KD-Tree
    coords1 = []
    for chunk in pd.read_csv(file1_path, delim_whitespace=True, header=None, comment='#',
                             names=['x', 'y', 'attrvalue'], chunksize=chunk_size):
        # 确保数据类型正确
        chunk['x'] = pd.to_numeric(chunk['x'], errors='coerce')
        chunk['y'] = pd.to_numeric(chunk['y'], errors='coerce')
        chunk['attrvalue'] = pd.to_numeric(chunk['attrvalue'], errors='coerce')

        # 删除无效数据
        chunk = chunk.dropna()
        coords1.extend(chunk[['x', 'y']].values)
        if len(coords1) % (chunk_size * 10) == 0:
            print(f"已读取文件1坐标: {len(coords1)} 个...")

    # 构建KD-Tree
    tree = cKDTree(coords1)

    # 创建输出文件
    with open(output_path, 'w') as f:
        f.write('x,y,attrvalue1,normalized_attrvalue1,attrvalue2,normalized_attrvalue2\n')

    # 收集第二个文件中匹配的点
    print("查找匹配坐标...")
    temp_matches = tempfile.NamedTemporaryFile(delete=False, suffix='.csv')
    matches_path = temp_matches.name
    temp_matches.close()

    with open(matches_path, 'w') as f:
        f.write('x,y,attrvalue2,file1_idx\n')

    # 查找匹配点
    processed_rows = 0
    for chunk in pd.read_csv(file2_path, delim_whitespace=True, header=None, comment='#',
                             names=['x', 'y', 'attrvalue'], chunksize=chunk_size):
        # 确保数据类型正确
        chunk['x'] = pd.to_numeric(chunk['x'], errors='coerce')
        chunk['y'] = pd.to_numeric(chunk['y'], errors='coerce')
        chunk['attrvalue'] = pd.to_numeric(chunk['attrvalue'], errors='coerce')

        # 删除无效数据
        chunk = chunk.dropna()

        matches = []
        for idx, row in chunk.iterrows():
            # 在KD-Tree中查找最近点
            dist, idx1 = tree.query([row['x'], row['y']], k=1)
            if dist <= tolerance:  # 如果距离在容差范围内
                matches.append((row['x'], row['y'], row['attrvalue'], idx1))

        if matches:
            pd.DataFrame(matches, columns=['x', 'y', 'attrvalue2', 'file1_idx']).to_csv(
                matches_path, mode='a', header=False, index=False)

        processed_rows += len(chunk)
        if processed_rows % (chunk_size * 10) == 0:
            print(f"已处理文件2的 {processed_rows} 行...")

    # 加载匹配结果
    matches_df = pd.read_csv(matches_path)

    # 处理匹配点数据
    print(f"找到 {len(matches_df)} 个匹配坐标，正在合并数据...")

    # 读取第一个文件并标记匹配点
    all_data = []
    idx_counter = 0
    for chunk in pd.read_csv(file1_path, delim_whitespace=True, header=None, comment='#',
                             names=['x', 'y', 'attrvalue'], chunksize=chunk_size):
        # 确保数据类型正确
        chunk['x'] = pd.to_numeric(chunk['x'], errors='coerce')
        chunk['y'] = pd.to_numeric(chunk['y'], errors='coerce')
        chunk['attrvalue'] = pd.to_numeric(chunk['attrvalue'], errors='coerce')

        # 删除无效数据
        chunk = chunk.dropna()

        chunk_length = len(chunk)
        chunk_indices = list(range(idx_counter, idx_counter + chunk_length))
        idx_counter += chunk_length

        # 找出这个块中包含的匹配索引
        mask_indices = [i for i, idx in enumerate(chunk_indices) if idx in set(matches_df['file1_idx'])]

        if mask_indices:
            sub_chunk = chunk.iloc[mask_indices].copy()
            sub_chunk['file1_idx'] = [chunk_indices[i] for i in mask_indices]

            # 标准化第一个文件的值
            sub_chunk = normalize_chunk(sub_chunk, stats1, normalize_method, stats2)
            sub_chunk.rename(columns={'attrvalue': 'attrvalue1',
                                      'normalized_attrvalue': 'normalized_attrvalue1'}, inplace=True)

            all_data.append(sub_chunk[['x', 'y', 'attrvalue1', 'normalized_attrvalue1', 'file1_idx']])

    if all_data:
        file1_data = pd.concat(all_data)

        # 合并两个文件的数据
        merged = pd.merge(file1_data, matches_df, on='file1_idx')

        # 对第二个文件的值进行标准化
        merged['normalized_attrvalue2'] = ((merged['attrvalue2'] - stats2['min']) /
                                           (stats2['max'] - stats2['min']))

        # 提取需要保存的列
        result_df = merged[['x', 'y', 'attrvalue1', 'normalized_attrvalue1',
                           'attrvalue2', 'normalized_attrvalue2']]

        # 判断输出文件扩展名
        if output_path.lower().endswith('.prn'):
            # 保存为PRN格式
            save_to_prn(result_df, output_path)
        else:
            # 保存为CSV格式
            result_df.to_csv(output_path, index=False)

        print(f"已合并 {len(merged)} 个共同坐标点")
    else:
        print("未找到任何匹配的坐标点!")

    # 清理临时文件
    os.unlink(matches_path)


def merge_with_interpolation(file1_path, file2_path, output_path, stats1, stats2,
                             tolerance=0.001, chunk_size=100000, normalize_method='minmax'):
    """使用插值合并不同坐标的文件"""
    # 注意：tolerance和normalize_method参数在此函数中未直接使用，但保留以保持接口一致性

    print("使用'插值'策略合并文件...")

    # 创建临时文件存储中间结果
    temp_file1 = tempfile.NamedTemporaryFile(delete=False, suffix='.csv')
    temp_path1 = temp_file1.name
    temp_file1.close()

    temp_file2 = tempfile.NamedTemporaryFile(delete=False, suffix='.csv')
    temp_path2 = temp_file2.name
    temp_file2.close()

    # 提取文件1的坐标和值
    print("提取文件1的坐标和值...")
    with open(temp_path1, 'w') as f:
        f.write('x,y,attrvalue\n')

    for chunk in pd.read_csv(file1_path, delim_whitespace=True, header=None, comment='#',
                             names=['x', 'y', 'attrvalue'], chunksize=chunk_size):
        # 确保数据类型正确
        chunk['x'] = pd.to_numeric(chunk['x'], errors='coerce')
        chunk['y'] = pd.to_numeric(chunk['y'], errors='coerce')
        chunk['attrvalue'] = pd.to_numeric(chunk['attrvalue'], errors='coerce')

        # 删除无效数据
        chunk = chunk.dropna()
        chunk[['x', 'y', 'attrvalue']].to_csv(temp_path1, mode='a', header=False, index=False)

    # 提取文件2的坐标和值
    print("提取文件2的坐标和值...")
    with open(temp_path2, 'w') as f:
        f.write('x,y,attrvalue\n')

    for chunk in pd.read_csv(file2_path, delim_whitespace=True, header=None, comment='#',
                             names=['x', 'y', 'attrvalue'], chunksize=chunk_size):
        # 确保数据类型正确
        chunk['x'] = pd.to_numeric(chunk['x'], errors='coerce')
        chunk['y'] = pd.to_numeric(chunk['y'], errors='coerce')
        chunk['attrvalue'] = pd.to_numeric(chunk['attrvalue'], errors='coerce')

        # 删除无效数据
        chunk = chunk.dropna()
        chunk[['x', 'y', 'attrvalue']].to_csv(temp_path2, mode='a', header=False, index=False)

    # 加载数据用于插值 (这步可能占用较多内存)
    print("加载数据进行插值处理...")
    data1 = pd.read_csv(temp_path1)
    data2 = pd.read_csv(temp_path2)

    points1 = data1[['x', 'y']].values
    values1 = data1['attrvalue'].values

    points2 = data2[['x', 'y']].values
    values2 = data2['attrvalue'].values

    # 合并所有唯一坐标点
    print("合并所有唯一坐标点...")
    all_points = np.vstack((points1, points2))
    unique_points = np.unique(all_points, axis=0)

    print(f"文件1有 {len(points1)} 个点")
    print(f"文件2有 {len(points2)} 个点")
    print(f"合并后有 {len(unique_points)} 个唯一点")

    # 对每个唯一点进行插值
    print("进行数据插值...")

    # 对文件1中缺失的点进行插值
    interp_values1 = griddata(points1, values1, unique_points, method='linear', fill_value=np.nan)

    # 对文件2中缺失的点进行插值
    interp_values2 = griddata(points2, values2, unique_points, method='linear', fill_value=np.nan)

    # 标准化数据
    norm_values1 = (interp_values1 - stats1['min']) / (stats1['max'] - stats1['min'])
    norm_values2 = (interp_values2 - stats2['min']) / (stats2['max'] - stats2['min'])

    # 创建结果DataFrame
    result = pd.DataFrame({
        'x': unique_points[:, 0],
        'y': unique_points[:, 1],
        'attrvalue1': interp_values1,
        'normalized_attrvalue1': norm_values1,
        'attrvalue2': interp_values2,
        'normalized_attrvalue2': norm_values2
    })

    # 移除缺失值
    result = result.dropna(subset=['attrvalue1', 'attrvalue2'])

    # 保存结果
    if output_path.lower().endswith('.prn'):
        # 保存为PRN格式
        save_to_prn(result, output_path)
    else:
        # 保存为CSV格式
        result.to_csv(output_path, index=False)
    print(f"完成插值处理，保存了 {len(result)} 个有效点")

    # 清理临时文件
    os.unlink(temp_path1)
    os.unlink(temp_path2)

    print(f"文件处理完成，总计 {len(result)} 行")


# 示例用法
if __name__ == "__main__":
    # 设置文件路径
    base_dir = r"D:\LQ_2024\MJN_2024\沉积相\Mishrif沉积相\Surface_attribute"
    file1_path = os.path.join(base_dir, "MB2.3_attribute__0250515_MB2.3_attribute_RMSAmp.dat")
    file2_path = os.path.join(base_dir, "MB2.3_GSI__0250515_MB2.3_GSI_RMSAmp.dat")

    # 设置输出文件路径（PRN格式）
    output_filename = "merged_normalized_data.prn"
    output_path = os.path.join(base_dir, output_filename)

    # 设置增强后的输出文件路径
    enhanced_output_filename = "merged_normalized_enhanced_data.prn"
    enhanced_output_path = os.path.join(base_dir, enhanced_output_filename)

    # 设置可视化输出路径
    visualization_path = os.path.join(base_dir, "enhancement_comparison.png")

    # 选择合并策略:
    # 'all' - 保留所有坐标点(默认)
    # 'common' - 只保留两个文件中共同的坐标点
    # 'interpolate' - 使用插值处理缺失点
    merge_strategy = 'all'

    # 坐标匹配的容差(当策略为'common'或'interpolate'时使用)
    # 如果两个点之间的距离小于此值，则认为它们是同一个点
    tolerance = 0.001

    # 标准化方法: 'zscore'或'minmax'
    normalize_method = 'minmax'

    # 调整chunk_size可以控制内存使用量，根据您的系统内存调整
    chunk_size = 100000

    # 河道增强参数
    outlier_threshold = 3.0  # Z-score阈值，用于检测异常值
    grid_size = 100  # 用于河道增强的网格大小，减小以避免内存问题
    bilateral_sigma_color = 0.1  # 双边滤波颜色标准差
    bilateral_sigma_spatial = 1.5  # 双边滤波空间标准差
    max_points = 100000  # 最大处理点数，超过此数量将进行采样

    try:
        # 处理文件并生成CSV格式的中间结果
        temp_output = os.path.join(tempfile.gettempdir(), "temp_merged_data.csv")
        process_and_merge_different_coordinates(
            file1_path, file2_path, temp_output,
            merge_strategy=merge_strategy,
            tolerance=tolerance,
            chunk_size=chunk_size,
            normalize_method=normalize_method
        )

        # 读取CSV结果
        print("读取合并后的数据...")
        result_df = pd.read_csv(temp_output)

        # 保存原始合并结果为PRN格式
        print("保存原始合并结果...")
        save_to_prn(result_df, output_path, column_width=20)

        # 保存原始数据的副本用于对比
        original_df = result_df.copy()

        # 应用河道增强处理
        print("\n开始应用河道增强处理...")

        # 确定要处理的列
        value_cols = []
        if 'attrvalue' in result_df.columns:
            value_cols.append('attrvalue')
        if 'normalized_attrvalue' in result_df.columns:
            value_cols.append('normalized_attrvalue')
        if 'attrvalue1' in result_df.columns:
            value_cols.extend(['attrvalue1', 'normalized_attrvalue1'])
        if 'attrvalue2' in result_df.columns:
            value_cols.extend(['attrvalue2', 'normalized_attrvalue2'])

        if not value_cols:
            print("错误: 未找到可处理的值列")
            print("可用列:", result_df.columns.tolist())
            raise ValueError("未找到可处理的值列")

        print(f"将处理以下列: {value_cols}")

        # 1. 去除异常值
        print("\n步骤1: 去除异常值")
        result_df = remove_outliers(result_df, value_cols, threshold=outlier_threshold)

        # 2. 填充缺失值
        print("\n步骤2: 填充缺失值")
        result_df = fill_missing_values(result_df, value_cols, method='linear')

        # 3. 平滑数据
        print("\n步骤3: 平滑数据")
        result_df = smooth_data(result_df, value_cols, method='gaussian', sigma=1.0)

        # 4. 增强河道连续性
        print("\n步骤4: 增强河道连续性")
        result_df = enhance_channel_continuity(
            result_df, 'x', 'y', value_cols,
            grid_size=grid_size,
            bilateral_sigma_color=bilateral_sigma_color,
            bilateral_sigma_spatial=bilateral_sigma_spatial,
            max_points=max_points
        )

        # 保存增强后的结果为PRN格式
        print("\n保存增强后的结果...")
        save_to_prn(result_df, enhanced_output_path, column_width=20)

        # 生成可视化对比图
        print("\n生成可视化对比图...")
        # 选择一个值列进行可视化
        vis_col = None
        for col in ['normalized_attrvalue1', 'normalized_attrvalue', 'attrvalue1', 'attrvalue']:
            if col in result_df.columns:
                vis_col = col
                break

        if vis_col:
            try:
                visualize_enhancement(
                    original_df, result_df, 'x', 'y', vis_col,
                    output_path=visualization_path,
                    title="河道增强效果对比"
                )
                print(f"对比可视化图已保存到: {visualization_path}")
            except Exception as e:
                print(f"生成可视化图时出错: {str(e)}")
        else:
            print("警告: 未找到可用于可视化的列")

        # 删除临时CSV文件
        try:
            os.remove(temp_output)
            print("临时文件已删除")
        except FileNotFoundError:
            print("临时文件已被自动清理")
        except Exception as e:
            print(f"删除临时文件时出错: {str(e)}")

        print(f"\n处理完成！")
        print(f"原始合并结果已保存到: {output_path}")
        print(f"增强后的结果已保存到: {enhanced_output_path}")

    except Exception as e:
        print(f"\n处理过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()
        print("\n尝试保存已处理的数据...")

        try:
            if 'result_df' in locals():
                # 如果已经有处理结果，尝试保存
                save_to_prn(result_df, enhanced_output_path, column_width=20)
                print(f"部分处理结果已保存到: {enhanced_output_path}")
        except Exception as save_error:
            print(f"保存部分结果时出错: {str(save_error)}")
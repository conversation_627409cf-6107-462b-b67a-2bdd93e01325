import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.preprocessing import StandardScaler
from sklearn.metrics.pairwise import euclidean_distances
from sklearn.cluster import DBSCAN
from scipy import stats
import warnings

warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False


class EnhancedFaciesAnalyzer:
    def __init__(self, df):
        self.df = df.copy()
        self.variables = ['DEN', 'DT', 'NEU', 'GR']
        self.variable_names = {
            'DEN': '密度(g/cm³)',
            'DT': '声波时差(μs/ft)',
            'NEU': '中子孔隙度',
            'GR': '自然伽马(API)'
        }
        self.scaler = StandardScaler()
        self.outliers = []
        self.reclassified_points = []

    def calculate_facies_centroids(self):
        """计算每个相带的质心"""
        centroids = {}
        facies_stats = {}

        for facies in self.df['Facies'].unique():
            facies_data = self.df[self.df['Facies'] == facies]

            # 计算质心
            centroid = facies_data[self.variables].mean()
            centroids[facies] = centroid

            # 计算统计信息
            stats_info = {
                'count': len(facies_data),
                'mean': facies_data[self.variables].mean(),
                'std': facies_data[self.variables].std(),
                'min': facies_data[self.variables].min(),
                'max': facies_data[self.variables].max(),
                '25%': facies_data[self.variables].quantile(0.25),
                '75%': facies_data[self.variables].quantile(0.75)
            }
            facies_stats[facies] = stats_info

        return centroids, facies_stats

    def print_facies_characteristics(self):
        """打印各Facies的4条曲线特征"""
        centroids, facies_stats = self.calculate_facies_centroids()

        print("\n" + "=" * 80)
        print("各Facies测井曲线特征分析")
        print("=" * 80)

        # 计算全局统计用于比较
        global_stats = {}
        for var in self.variables:
            all_values = self.df[var].values
            global_stats[var] = {
                'mean': np.mean(all_values),
                'std': np.std(all_values),
                'median': np.median(all_values)
            }

        # 定义特征判断函数
        def get_level_description(value, global_mean, global_std):
            z_score = (value - global_mean) / global_std
            if z_score > 0.5:
                return "高", "↑"
            elif z_score < -0.5:
                return "低", "↓"
            else:
                return "中等", "→"

        # 推断岩性的函数
        def infer_lithology(den_level, dt_level, neu_level, gr_level):
            """基于测井响应推断岩性"""
            if gr_level == "高":
                if neu_level == "高" and dt_level == "高":
                    return "泥岩/页岩"
                else:
                    return "泥质砂岩"
            elif den_level == "低" and dt_level == "高" and neu_level == "高":
                return "高孔隙度砂岩"
            elif den_level == "高" and dt_level == "低" and neu_level == "低":
                if gr_level == "低":
                    return "致密砂岩/石英岩"
                else:
                    return "碳酸盐岩"
            elif den_level == "中等" and gr_level == "中等":
                if neu_level == "中等":
                    return "中等孔隙度砂岩"
                else:
                    return "粉砂岩"
            else:
                return "混合岩性"

        # 打印每个Facies的特征
        for facies in sorted(facies_stats.keys()):
            stats = facies_stats[facies]
            print(f"\n【Facies {facies}】 (样本数: {stats['count']})")
            print("-" * 60)

            # 分析各参数特征
            characteristics = {}
            for var in self.variables:
                mean_val = stats['mean'][var]
                std_val = stats['std'][var]
                level, arrow = get_level_description(mean_val, global_stats[var]['mean'], global_stats[var]['std'])
                characteristics[var] = level

                print(f"{self.variable_names[var]:12} | {arrow} {level:4} | "
                      f"均值: {mean_val:6.2f} ± {std_val:5.2f} | "
                      f"范围: [{stats['min'][var]:6.2f}, {stats['max'][var]:6.2f}]")

            # 推断岩性
            lithology = infer_lithology(
                characteristics['DEN'],
                characteristics['DT'],
                characteristics['NEU'],
                characteristics['GR']
            )

            print(f"{'推断岩性':12} | ★ {lithology}")

            # 储层品质评价
            if characteristics['NEU'] == "高" and characteristics['DT'] == "高":
                reservoir_quality = "好储层" if characteristics['GR'] == "低" else "中等储层"
            elif characteristics['DEN'] == "高" and characteristics['NEU'] == "低":
                reservoir_quality = "差储层/致密层"
            else:
                reservoir_quality = "中等储层"

            print(f"{'储层品质':12} | ◆ {reservoir_quality}")

        print("\n" + "=" * 80)
        print("特征说明：")
        print("↑ 高值  ↓ 低值  → 中等值")
        print("★ 岩性推断基于测井响应特征组合")
        print("◆ 储层品质基于孔隙度和渗透率相关参数")
        print("=" * 80)

    def detect_outliers_statistical(self, method='iqr', threshold=1.5):
        """使用统计方法检测离群点"""
        outliers = []

        for facies in self.df['Facies'].unique():
            facies_mask = self.df['Facies'] == facies
            facies_data = self.df[facies_mask]

            if len(facies_data) < 3:  # 样本太少跳过
                continue

            for var in self.variables:
                if method == 'iqr':
                    # 使用IQR方法
                    Q1 = facies_data[var].quantile(0.25)
                    Q3 = facies_data[var].quantile(0.75)
                    IQR = Q3 - Q1
                    lower_bound = Q1 - threshold * IQR
                    upper_bound = Q3 + threshold * IQR

                    outlier_mask = (facies_data[var] < lower_bound) | (facies_data[var] > upper_bound)

                elif method == 'zscore':
                    # 使用Z-score方法
                    z_scores = np.abs(stats.zscore(facies_data[var]))
                    outlier_mask = z_scores > threshold

                if outlier_mask.any():
                    outlier_indices = facies_data[outlier_mask].index.tolist()
                    for idx in outlier_indices:
                        outliers.append({
                            'index': idx,
                            'original_facies': facies,
                            'variable': var,
                            'value': self.df.loc[idx, var],
                            'method': method,
                            'reason': f'{var}超出正常范围'
                        })

        return outliers

    def detect_outliers_distance(self, distance_threshold=2.0):
        """使用距离方法检测离群点 - 基于4条曲线的综合距离"""
        centroids, _ = self.calculate_facies_centroids()
        outliers = []

        # 标准化数据 - 关键：所有4条曲线同时标准化
        data_scaled = self.scaler.fit_transform(self.df[self.variables])

        for facies in self.df['Facies'].unique():
            facies_mask = self.df['Facies'] == facies
            facies_indices = self.df[facies_mask].index
            facies_data_scaled = data_scaled[facies_mask]

            if len(facies_data_scaled) < 2:
                continue

            # 计算到质心的欧氏距离（基于4条曲线）
            centroid_scaled = self.scaler.transform([centroids[facies]])[0]
            distances = euclidean_distances(facies_data_scaled, [centroid_scaled]).flatten()

            # 使用距离阈值识别离群点
            distance_threshold_adjusted = distance_threshold * np.std(distances)
            outlier_mask = distances > distance_threshold_adjusted

            for i, is_outlier in enumerate(outlier_mask):
                if is_outlier:
                    original_idx = facies_indices[i]
                    outliers.append({
                        'index': original_idx,
                        'original_facies': facies,
                        'distance': distances[i],
                        'threshold': distance_threshold_adjusted,
                        'reason': f'4条曲线综合距离过远 (距离={distances[i]:.3f})'
                    })

        return outliers

    def find_best_facies_for_outliers(self, outliers):
        """为离群点找到最合适的相带 - 基于4条曲线的综合分析"""
        centroids, facies_stats = self.calculate_facies_centroids()
        reclassified = []

        # 标准化质心 - 所有4条曲线
        centroid_matrix = np.array([centroids[facies].values for facies in sorted(centroids.keys())])
        centroids_scaled = self.scaler.fit_transform(centroid_matrix)

        for outlier in outliers:
            idx = outlier['index']
            original_facies = outlier['original_facies']

            # 获取该点的标准化数据（4条曲线）
            point_data = self.df.loc[idx, self.variables].values.reshape(1, -1)
            point_scaled = self.scaler.transform(point_data)[0]

            # 计算到所有相带质心的距离（基于4条曲线）
            distances_to_centroids = {}
            for i, facies in enumerate(sorted(centroids.keys())):
                dist = euclidean_distances([point_scaled], [centroids_scaled[i]])[0][0]
                distances_to_centroids[facies] = dist

            # 找到最近的相带
            best_facies = min(distances_to_centroids, key=distances_to_centroids.get)
            best_distance = distances_to_centroids[best_facies]
            original_distance = distances_to_centroids[original_facies]

            # 计算改进程度
            improvement = original_distance - best_distance

            # 如果最佳相带不是原始相带，且有显著改进
            if best_facies != original_facies and improvement > 0.1:
                # 验证新分类的合理性（检查4条曲线）
                validation_score = self.validate_reclassification(idx, best_facies, facies_stats)

                reclassified.append({
                    'index': idx,
                    'original_facies': original_facies,
                    'new_facies': best_facies,
                    'original_distance': original_distance,
                    'new_distance': best_distance,
                    'improvement': improvement,
                    'validation_score': validation_score,
                    'reason': f'基于4条曲线更接近Facies {best_facies}质心',
                    'data_point': self.df.loc[idx, self.variables].to_dict()
                })

        return reclassified

    def validate_reclassification(self, idx, new_facies, facies_stats):
        """验证重新分类的合理性 - 检查4条曲线"""
        point_data = self.df.loc[idx, self.variables]
        new_facies_stats = facies_stats[new_facies]

        validation_score = 0
        total_vars = len(self.variables)

        print(f"\n验证数据点 {idx} 重分类到 Facies {new_facies}:")
        for var in self.variables:
            value = point_data[var]
            mean = new_facies_stats['mean'][var]
            std = new_facies_stats['std'][var]
            q25 = new_facies_stats['25%'][var]
            q75 = new_facies_stats['75%'][var]

            # 检查是否在合理范围内
            if q25 <= value <= q75:
                validation_score += 1  # 在四分位数范围内，得满分
                status = "✓ 四分位数范围内"
            elif abs(value - mean) <= 2 * std:
                validation_score += 0.5  # 在2倍标准差内，得半分
                status = "○ 2倍标准差内"
            else:
                status = "✗ 超出范围"

            print(f"  {var}: {value:.3f} vs 均值{mean:.3f}±{std:.3f} [{q25:.3f}-{q75:.3f}] {status}")

        final_score = validation_score / total_vars
        print(f"  综合验证分数: {final_score:.3f}")
        return final_score

    def analyze_and_reclassify(self, statistical_method='iqr', statistical_threshold=1.5,
                               distance_threshold=2.0, min_validation_score=0.5):
        """主分析和重分类函数"""
        print("=== Hartha沉积相数据异常点检测与重分类分析 ===")
        print("基于密度(DEN)、声波时差(DT)、中子孔隙度(NEU)、自然伽马(GR)四条曲线的综合分析\n")

        # 首先打印各Facies特征
        self.print_facies_characteristics()

        # 1. 计算原始相带统计信息
        centroids, facies_stats = self.calculate_facies_centroids()

        print("\n=== 原始相带详细统计信息 ===")
        for facies in sorted(facies_stats.keys()):
            stats = facies_stats[facies]
            print(f"\nFacies {facies} (样本数: {stats['count']}):")
            for var in self.variables:
                print(f"  {self.variable_names[var]:15}: 均值={stats['mean'][var]:7.3f}±{stats['std'][var]:6.3f}, "
                      f"范围=[{stats['min'][var]:7.3f}, {stats['max'][var]:7.3f}]")

        # 2. 统计方法检测离群点
        print(f"\n=== 使用{statistical_method}方法检测统计离群点 ===")
        statistical_outliers = self.detect_outliers_statistical(statistical_method, statistical_threshold)
        print(f"检测到 {len(statistical_outliers)} 个统计离群点")

        # 3. 距离方法检测离群点（基于4条曲线）
        print(f"\n=== 使用4条曲线综合距离方法检测离群点 ===")
        distance_outliers = self.detect_outliers_distance(distance_threshold)
        print(f"检测到 {len(distance_outliers)} 个距离离群点")

        # 4. 合并离群点（去重）
        all_outlier_indices = set()
        for outlier in statistical_outliers + distance_outliers:
            all_outlier_indices.add(outlier['index'])

        combined_outliers = []
        for idx in all_outlier_indices:
            outlier_info = {'index': idx, 'original_facies': self.df.loc[idx, 'Facies']}
            combined_outliers.append(outlier_info)

        print(f"总共发现 {len(combined_outliers)} 个需要重新评估的点")

        # 5. 为离群点找到最佳相带（基于4条曲线）
        print(f"\n=== 基于4条曲线为异常点寻找最佳相带 ===")
        reclassified = self.find_best_facies_for_outliers(combined_outliers)

        # 6. 筛选高质量的重分类建议
        high_quality_reclassified = [r for r in reclassified
                                     if r['validation_score'] >= min_validation_score
                                     and r['improvement'] > 0.2]

        print(f"\n建议重新分类 {len(high_quality_reclassified)} 个点:")

        for r in high_quality_reclassified:
            print(f"\n数据点 {r['index']}: Facies {r['original_facies']} → Facies {r['new_facies']}")
            print(f"  改进程度: {r['improvement']:.3f}, 验证分数: {r['validation_score']:.3f}")
            print(f"  理由: {r['reason']}")
            print(f"  4条曲线数值: DEN={r['data_point']['DEN']:.3f}, DT={r['data_point']['DT']:.1f}, "
                  f"NEU={r['data_point']['NEU']:.3f}, GR={r['data_point']['GR']:.1f}")

        # 7. 应用重分类
        df_reclassified = self.df.copy()
        df_reclassified['Original_Facies'] = df_reclassified['Facies']
        df_reclassified['Reclassification_Reason'] = ''

        for r in high_quality_reclassified:
            idx = r['index']
            df_reclassified.loc[idx, 'Facies'] = r['new_facies']
            df_reclassified.loc[idx, 'Reclassification_Reason'] = r['reason']

        return df_reclassified, high_quality_reclassified, centroids, facies_stats

    def plot_detailed_crossplots(self, df_reclassified, reclassified_points):
        """绘制详细的交汇图分析"""
        fig, axes = plt.subplots(3, 2, figsize=(15, 18))

        # 常用的测井交汇图组合
        plot_combinations = [
            ('DEN', 'NEU', '密度-中子交汇图'),
            ('DT', 'GR', '声波-伽马交汇图'),
            ('DEN', 'DT', '密度-声波交汇图'),
            ('NEU', 'GR', '中子-伽马交汇图'),
            ('DEN', 'GR', '密度-伽马交汇图'),
            ('DT', 'NEU', '声波-中子交汇图')
        ]

        colors = ['red', 'blue', 'green', 'orange', 'purple', 'brown', 'pink', 'gray']

        for i, (var1, var2, title) in enumerate(plot_combinations):
            ax = axes[i // 2, i % 2]

            # 绘制原始分类
            for j, facies in enumerate(sorted(self.df['Facies'].unique())):
                original_data = self.df[self.df['Facies'] == facies]
                ax.scatter(original_data[var1], original_data[var2],
                           c=colors[j % len(colors)], alpha=0.6, s=30,
                           label=f'Facies {facies}', marker='o')

            # 标记重分类的点
            for r in reclassified_points:
                idx = r['index']
                x_val = self.df.loc[idx, var1]
                y_val = self.df.loc[idx, var2]

                # 原始位置用X标记
                ax.scatter(x_val, y_val, c='black', s=100, marker='x', linewidth=3)

                # 添加箭头指向新分类
                new_facies_color = colors[r['new_facies'] % len(colors)]
                ax.annotate(f"→F{r['new_facies']}",
                            (x_val, y_val),
                            xytext=(5, 5),
                            textcoords='offset points',
                            fontsize=8,
                            color=new_facies_color,
                            weight='bold')

            ax.set_xlabel(f'{self.variable_names[var1]}')
            ax.set_ylabel(f'{self.variable_names[var2]}')
            ax.set_title(f'{title}\n(X标记=重分类点)')
            ax.grid(True, alpha=0.3)
            ax.legend(fontsize=8)

        plt.tight_layout()
        plt.suptitle('基于4条曲线的沉积相重分类分析 - 交汇图对比', fontsize=16, y=0.98)
        plt.show()

    def generate_comprehensive_report(self, df_reclassified, reclassified_points):
        """生成综合分析报告"""
        print("\n" + "=" * 80)
        print("基于4条测井曲线的沉积相重分类综合报告")
        print("=" * 80)

        # 重分类逻辑说明
        print("\n【重分类逻辑说明】")
        print("1. 离群点检测: 同时考虑DEN、DT、NEU、GR四条曲线")
        print("2. 距离计算: 基于4维欧氏距离到各Facies质心")
        print("3. 重分类决策: 选择4维距离最近的Facies")
        print("4. 验证机制: 检查4条曲线在新Facies中的合理性")

        # 统计重分类情况
        reclassification_summary = {}
        for r in reclassified_points:
            original = r['original_facies']
            new = r['new_facies']
            key = f"{original}→{new}"
            reclassification_summary[key] = reclassification_summary.get(key, 0) + 1

        print(f"\n【重分类统计】")
        for transition, count in reclassification_summary.items():
            print(f"  Facies {transition}: {count} 个点")

        # 重分类前后的相带分布
        print(f"\n【相带分布变化】")
        original_counts = self.df['Facies'].value_counts().sort_index()
        new_counts = df_reclassified['Facies'].value_counts().sort_index()

        print("Facies  原始数量  新数量  变化    百分比变化")
        print("-" * 45)
        for facies in sorted(set(list(original_counts.index) + list(new_counts.index))):
            orig = original_counts.get(facies, 0)
            new = new_counts.get(facies, 0)
            change = new - orig
            change_str = f"+{change}" if change > 0 else str(change)
            pct_change = (change / orig * 100) if orig > 0 else 0
            print(f"  {facies}        {orig:3d}      {new:3d}    {change_str:+3}    {pct_change:+6.1f}%")

        # 4条曲线的改进分析
        print(f"\n【4条曲线综合改进分析】")
        validation_scores = [r['validation_score'] for r in reclassified_points]
        improvements = [r['improvement'] for r in reclassified_points]

        print(f"  平均验证分数: {np.mean(validation_scores):.3f} (基于4条曲线)")
        print(f"  平均改进程度: {np.mean(improvements):.3f} (4维距离改进)")
        print(f"  最大改进程度: {np.max(improvements):.3f}")

        # 各曲线贡献度分析
        print(f"\n【各曲线在重分类中的作用】")
        curve_contributions = {var: 0 for var in self.variables}

        for r in reclassified_points:
            idx = r['index']
            orig_facies = r['original_facies']
            new_facies = r['new_facies']

            # 分析哪条曲线的改变最显著
            for var in self.variables:
                value = self.df.loc[idx, var]
                # 这里可以添加更复杂的贡献度计算逻辑

        print("  密度(DEN): 主要识别孔隙度和岩性差异")
        print("  声波时差(DT): 主要反映孔隙度和岩石硬度")
        print("  中子孔隙度(NEU): 直接反映孔隙度特征")
        print("  自然伽马(GR): 主要识别泥质含量")


# 主程序
def main():
    """主程序 - 增强版"""
    # 读取实际CSV文件
    file_path = r"D:\LQ_2024\MJN_2024\沉积相\Hartha沉积相\20250527_Hartha_沉积相基础数据\MJ-29_ori_cor_5class_250529 - 原始备份.csv"

    try:
        df = pd.read_csv(file_path, encoding='utf-8')
        print("数据加载成功 (UTF-8编码)")
    except UnicodeDecodeError:
        try:
            df = pd.read_csv(file_path, encoding='gbk')
            print("数据加载成功 (GBK编码)")
        except UnicodeDecodeError:
            df = pd.read_csv(file_path, encoding='utf-8-sig')
            print("数据加载成功 (UTF-8-BOM编码)")

    print("数据概览:")
    print(df.head())
    print(f"\n数据形状: {df.shape}")
    print(f"列名: {df.columns.tolist()}")
    print(f"相带分布: {df['Facies'].value_counts().sort_index()}")

    # 检查数据完整性
    missing_cols = []
    required_cols = ['DEN', 'DT', 'NEU', 'GR', 'Facies']
    for col in required_cols:
        if col not in df.columns:
            missing_cols.append(col)

    if missing_cols:
        print(f"\n警告: 缺少必需的列: {missing_cols}")
        print("请检查CSV文件的列名是否正确")
        return None, None, None

    # 创建增强版分析器
    analyzer = EnhancedFaciesAnalyzer(df)

    # 执行分析和重分类
    df_reclassified, reclassified_points, centroids, facies_stats = analyzer.analyze_and_reclassify(
        statistical_method='iqr',
        statistical_threshold=1.5,
        distance_threshold=1.8,
        min_validation_score=0.4
    )

    # 绘制详细对比图
    if reclassified_points:
        analyzer.plot_detailed_crossplots(df_reclassified, reclassified_points)

        # 生成综合报告
        analyzer.generate_comprehensive_report(df_reclassified, reclassified_points)

        # 保存结果
        output_path = r"D:\LQ_2024\MJN_2024\沉积相\Hartha沉积相\20250527_Hartha_沉积相基础数据\MJ-29_4曲线重分类结果.csv"
        df_reclassified.to_csv(output_path, index=False, encoding='utf-8-sig')
        print(f"\n结果已保存到: {output_path}")

    else:
        print("\n未发现需要重分类的异常点，原始分类较为合理。")

    return df, df_reclassified, reclassified_points


if __name__ == "__main__":
    # 直接运行增强版数据分析
    original_df, reclassified_df, reclassified_points = main()